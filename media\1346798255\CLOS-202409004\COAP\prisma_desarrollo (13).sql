-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 27-09-2024 a las 22:55:31
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `prisma_desarrollo`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tc_empresas`
--

CREATE TABLE `tc_empresas` (
  `int_idEmpresa` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tc_empresas`
--

INSERT INTO `tc_empresas` (`int_idEmpresa`, `str_idSuscripcion`, `str_NombreEmpresa`, `str_RazonSocial`, `str_Ruc`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, '1346798255', 'MERCADO VIRTUAL PERU S.A.C.', 'Empresa de tegnología', '104569874561', NULL, NULL, NULL, NULL),
(3, '1346798255', 'Lauren David', 'Empresa de prueba', '102354798121', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_clausulaslegales`
--

CREATE TABLE `tm_clausulaslegales` (
  `int_idClausulasLegales` int NOT NULL,
  `str_CodClausulasLegales` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_clausulaslegales`
--

INSERT INTO `tm_clausulaslegales` (`int_idClausulasLegales`, `str_CodClausulasLegales`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, 'CLA2', '1346798255', 'Cláusula de indemnidad', '2024-09-12 17:26:26', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_especialidades`
--

CREATE TABLE `tm_especialidades` (
  `int_idEspecialidades` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodigoEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_estados`
--

CREATE TABLE `tm_estados` (
  `int_idEstado` int NOT NULL,
  `str_codEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_estados`
--

INSERT INTO `tm_estados` (`int_idEstado`, `str_codEstado`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'E001', '1346798255', 'Nuevo', '2024-09-06 17:15:43', '2024-09-06 17:18:51', 1, 1),
(2, 'E002', '1346798255', 'Asignado', '2024-09-18 12:14:10', NULL, 1, NULL),
(3, 'E003', '1346798255', 'En Proceso', '2024-09-13 16:06:24', NULL, 1, NULL),
(4, 'E004', '1346798255', 'En Validacion', '2024-09-18 12:14:10', NULL, 1, NULL),
(5, 'E005', '1346798255', 'Aceptado', '2024-09-18 12:15:08', NULL, 1, NULL),
(6, 'E006', '1346798255', 'En Aprobacion', '2024-09-18 12:18:15', NULL, 1, NULL),
(7, 'E007', '1346798255', 'Aprobado', '2024-09-18 12:18:15', NULL, 1, NULL),
(8, 'E008', '1346798255', 'Firmado', '2024-09-18 12:19:04', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_interlocutores`
--

CREATE TABLE `tm_interlocutores` (
  `int_idInterlocutor` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Interlocutor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TipoDoc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Documento` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Domicilio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Correo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_obligaciones` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_ValorAporte` int DEFAULT NULL,
  `int_PorcentajeAporte` float DEFAULT NULL,
  `str_ValorServicios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ValorHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RepLegal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_RLPartida` varchar(50) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RLTipoDocumento` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RLDocumento` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_interlocutores`
--

INSERT INTO `tm_interlocutores` (`int_idInterlocutor`, `str_idSuscripcion`, `str_Interlocutor`, `str_RazonSocial`, `str_TipoDoc`, `str_Documento`, `str_Domicilio`, `str_Correo`, `str_obligaciones`, `int_ValorAporte`, `int_PorcentajeAporte`, `str_ValorServicios`, `str_ValorHonorarios`, `str_RepLegal`, `int_RLPartida`, `str_RLTipoDocumento`, `str_RLDocumento`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, '1346798255', NULL, 'Empresa de Ejemplo S.A.', 'RUC', '10456789012', 'Av. Ejemplo 123, Lima, Perú', '<EMAIL>', 'Pago de impuestos', 10000, 29.5, 'Consultoría', '5000', 'Juan Pérez', '456789', NULL, NULL, '2024-09-11 16:45:52', '2024-09-11 17:19:20', 1, NULL),
(3, '1346798255', NULL, 'Empresa de Ejemplo S.A.', 'RUC', '10456789012', 'Av. Ejemplo 123, Lima, Perú', '<EMAIL>', 'Pago de impuestos', 10000, 25.5, 'Consultoría', '5000', 'Juan Pérez', '456789', NULL, NULL, '2024-09-11 16:56:18', NULL, 1, NULL),
(4, '1346798255', NULL, 'Empresa de Ejemplo S.A.', 'RUC', '10456789012', 'Av. Ejemplo 123, Lima, Perú', '<EMAIL>', 'Pago de impuestos', 10000, 25.5, 'Consultoría', '5000', 'Juan Pérez', '456789', NULL, NULL, '2024-09-11 16:56:19', NULL, 1, NULL),
(5, '1346798255', 'Juan Carlos Martinez', '', 'RUC', '10726405684', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', NULL, NULL, NULL, '2024-09-19 23:43:27', '2024-09-20 15:27:03', 1, NULL),
(6, '1346798255', 'jose perez jua2', '', 'RUC', '10726405688', 'av ricardo angulo 745', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', '123654', 'RUC', '10726405009', '2024-09-20 08:16:25', '2024-09-24 16:42:30', 1, 1),
(7, '1346798255', 'Alexa Nicole Alva', '', 'RUC', '10726405685', '', '<EMAIL>', '', NULL, NULL, '', '', 'Juan pepito perez', '20547878', NULL, NULL, '2024-09-23 11:13:47', '2024-09-23 18:08:39', 1, NULL),
(8, '1346798255', 'jose perez juan', '', 'RUC', '10726405686', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '975846', NULL, NULL, '2024-09-23 11:22:47', '2024-09-23 11:23:47', 1, NULL),
(9, '1346798255', 'Alexa Nicole Alva', '', 'RUC', '10726405687', '', '<EMAIL>', '', NULL, NULL, '', '', 'Juan pepito perez', '20547878', NULL, NULL, '2024-09-23 11:25:25', '2024-09-23 11:25:46', 1, NULL),
(12, '1346798255', 'jose perez juan', '', 'RUC', '10726405691', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '2054788', NULL, NULL, '2024-09-23 11:36:04', '2024-09-24 13:42:13', 1, NULL),
(13, '1346798255', 'jose perez juan', '', 'RUC', '10726405693', '', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', '20547878', NULL, NULL, '2024-09-23 11:48:06', '2024-09-23 11:49:48', 1, NULL),
(14, '1346798255', 'jose perez juan', '', 'RUC', '10726405692', '', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', '20547878', NULL, NULL, '2024-09-23 11:51:44', '2024-09-23 12:04:17', 1, NULL),
(15, '1346798255', 'jose perez juan', '', 'RUC', '10726405694', '', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', '20547878', NULL, NULL, '2024-09-23 11:55:18', NULL, 1, NULL),
(16, '1346798255', 'jose perez juan', '', 'RUC', '10726405695', '', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', '20547878', NULL, NULL, '2024-09-23 11:58:08', NULL, 1, NULL),
(17, '1346798255', 'jose perez juan', '', 'RUC', '10726405696', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '2054788', NULL, NULL, '2024-09-23 12:04:35', NULL, 1, NULL),
(18, '1346798255', 'jose perez juan', '', 'RUC', '10726405697', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '2054788', NULL, NULL, '2024-09-23 12:07:09', NULL, 1, NULL),
(19, '1346798255', 'jose perez juan', '', 'RUC', '10726405698', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '2054788', NULL, NULL, '2024-09-23 12:08:33', '2024-09-23 12:08:39', 1, NULL),
(20, '1346798255', 'jose perez juan', '', 'RUC', '10726405699', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '2054788', NULL, NULL, '2024-09-23 12:08:46', NULL, 1, NULL),
(21, '1346798255', 'jose perez juan', '', 'RUC', '10726405690', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '2054788', NULL, NULL, '2024-09-23 12:10:34', NULL, 1, NULL),
(22, '1346798255', 'jose perez juan', '', 'RUC', '10726405601', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '2054788', NULL, NULL, '2024-09-23 12:13:30', NULL, 1, NULL),
(23, '1346798255', 'jose perez juan', '', 'RUC', '10726405602', '', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', '2054788', NULL, NULL, '2024-09-23 12:14:11', NULL, 1, NULL),
(24, '1346798255', 'jose perez juan', '', 'RUC', '10726405603', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', '975846', NULL, NULL, '2024-09-23 12:17:48', NULL, 1, NULL),
(25, '1346798255', 'jose perez juan', '', 'RUC', '10726405605', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', '2054788', NULL, NULL, '2024-09-23 12:19:25', NULL, 1, NULL),
(26, '1346798255', 'jose perez juan', '', 'RUC', '10726405606', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '20547878', NULL, NULL, '2024-09-23 12:20:16', NULL, 1, NULL),
(27, '1346798255', 'jose perez juan', '', 'RUC', '10726405607', '', '<EMAIL>', '', NULL, NULL, '', '', 'yii', '20547878', NULL, NULL, '2024-09-23 12:22:20', NULL, 1, NULL),
(28, '1346798255', 'jose perez juan', '', 'RUC', '10726405608', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', '975846', NULL, NULL, '2024-09-23 12:27:23', NULL, 1, NULL),
(29, '1346798255', 'jose perez jua2', '', 'RUC', '10726405609', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', '20547878', NULL, NULL, '2024-09-23 12:32:28', NULL, 1, NULL),
(30, '1346798255', 'jose perez jua2', '', 'RUC', '10726405610', '', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', NULL, NULL, NULL, '2024-09-23 13:17:01', NULL, 1, NULL),
(31, '1346798255', 'jose perez jua2', '', 'DNI', '10726405001', '', '<EMAIL>', '', NULL, NULL, '', '', 'carlos matinez', '20547878', NULL, NULL, '2024-09-23 13:48:24', NULL, 1, NULL),
(32, '1346798255', 'jose perez juan', '', 'RUC', '10726405002', 'av...................', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', '975846', 'DNI', '72640568', '2024-09-23 13:54:58', '2024-09-24 15:14:03', 1, NULL),
(33, '1346798255', 'jose perez jua2', '', 'RUC', '10726400002', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', '123654', '', '', '2024-09-24 14:48:24', NULL, 1, NULL),
(34, '1346798255', 'josesito', '', 'DNI', '424414144141', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422525', 'RUC', '4272773773', '2024-09-24 15:39:09', '2024-09-24 15:39:53', 1, NULL),
(35, '1346798255', 'josesito', '', 'DNI', '424414144141', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422525', 'RUC', '4272773773', '2024-09-24 15:39:18', NULL, 1, NULL),
(36, '1346798255', 'josesito', '', 'DNI', '424414144141', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422525', 'RUC', '4272773773', '2024-09-24 15:39:33', NULL, 1, NULL),
(37, '1346798255', 'josesito', '', 'DNI', '424414144141', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422525', 'RUC', '4272773773', '2024-09-24 15:39:34', NULL, 1, NULL),
(38, '1346798255', 'josesito', '', 'DNI', '424414144141', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422525', 'RUC', '4272773773', '2024-09-24 15:39:35', NULL, 1, NULL),
(39, '1346798255', 'josesitojuioi', '', 'DNI', '424414144142', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422526', 'DNI', '41411111', '2024-09-24 15:48:45', NULL, 1, NULL),
(40, '1346798255', 'josesitojuioi', '', 'DNI', '424414144142', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422526', 'DNI', '41411111', '2024-09-24 15:49:05', NULL, 1, NULL),
(41, '1346798255', 'josesitojuioi', '', 'DNI', '424414144142', 'av la mar', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '2422526', 'DNI', '41411111', '2024-09-24 15:49:06', NULL, 1, NULL),
(42, '1346798255', 'jose perez juan', '', 'RUC', '10726400004', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', NULL, '', '', '2024-09-24 16:03:13', NULL, 1, NULL),
(43, '1346798255', 'josesito', '', 'DNI', '424414144144', '', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '0', 'RUC', '4272773773', '2024-09-24 16:10:07', NULL, 1, NULL),
(44, '1346798255', 'josesito', '', 'DNI', '424414144145', '', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '1256985', 'RUC', '4272773773', '2024-09-24 16:11:40', '2024-09-24 16:21:26', 1, NULL),
(45, '1346798255', 'josesito', '', 'DNI', '424414144146', '', '', '', NULL, NULL, '', '', 'juanito pepito pepe', '', '', '4272773773', '2024-09-24 16:42:30', NULL, 1, NULL),
(46, '1346798255', 'jose perez juan', '', 'RUC', '10726405645', '', '<EMAIL>', '', NULL, NULL, '', '', 'jose maria torres', '123654', '', '', '2024-09-24 16:45:58', '2024-09-24 16:52:50', 1, NULL),
(47, '1346798255', 'jose perez jua2', '', 'RUC', '10726405444', '', 'jlñoñkñ', '', NULL, NULL, '', '', 'carlos matinez', '123654', '', '', '2024-09-24 16:53:23', '2024-09-24 16:54:59', 1, NULL),
(48, '1346798255', 'yipupup', '', 'RUC', '10726405655', '', '9pup9upu9p', '', NULL, NULL, '', '', 'pupup9p9p', NULL, '', '', '2024-09-24 16:56:09', '2024-09-26 18:01:55', 1, NULL),
(49, '1346798255', 'jose perez juan', '', 'RUC', '10726405005', '', 'úúoó', '', NULL, NULL, '', '', 'uoú', NULL, '', '', '2024-09-24 17:18:01', '2024-09-26 18:08:37', 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_suscripcion`
--

CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_suscripcion`
--

INSERT INTO `tm_suscripcion` (`str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
('1346798255', 'Suscripción Prueba\r\n', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipodocumento`
--

CREATE TABLE `tm_tipodocumento` (
  `int_idTipoDocumentos` int NOT NULL,
  `str_CodTipoDocumento` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tipodocumento`
--

INSERT INTO `tm_tipodocumento` (`int_idTipoDocumentos`, `str_CodTipoDocumento`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'DOAD', '1346798255', 'Documentos adjuntos', '2024-09-20 04:27:03', NULL, 1, NULL),
(2, 'DOPC', '1346798255', 'Plantilla modelo de contrato', '2024-09-11 09:11:11', NULL, 1, NULL),
(3, 'COAP', '1346798255', 'Contratos Aprobados', '2024-09-26 22:41:34', NULL, 1, NULL),
(4, 'COFI', '1346798255', 'Contratos Firmados', '2024-09-27 19:53:53', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tiposolicitud`
--

CREATE TABLE `tm_tiposolicitud` (
  `int_idTipoSolicitud` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodTipoSol` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tiposolicitud`
--

INSERT INTO `tm_tiposolicitud` (`int_idTipoSolicitud`, `str_idSuscripcion`, `str_CodTipoSol`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, '1346798255', 'CPRS', 'Contrato de prestación de servicios', '2024-09-11 17:30:34', NULL, 1, NULL),
(2, '1346798255', 'CLOS', 'Contrato de locación de servicios', '2024-09-17 15:07:42', NULL, 1, NULL),
(3, '1346798255', 'CPSP', 'Contrato de prestación de servicios profesionales', '2024-09-17 15:07:42', NULL, 1, NULL),
(4, '1346798255', 'COAB', 'Contrato de arrendamiento de bienes muebles o inmuebles', '2024-09-23 10:10:34', NULL, 1, NULL),
(5, '1346798255', 'CCVB', 'Contrato de compra / venta de bienes muebles o inmuebles', '2024-09-23 15:12:19', NULL, 1, NULL),
(6, '1346798255', 'CCON', 'Contrato de consorcio', '2024-09-23 15:12:19', NULL, 1, NULL),
(7, '1346798255', 'CACO', 'Acuerdo de confidencialidad', '2024-09-23 15:14:31', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_unidadesnegocios`
--

CREATE TABLE `tm_unidadesnegocios` (
  `int_idUnidadesNegocio` int NOT NULL,
  `str_Descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_fechaCreacion` datetime NOT NULL,
  `dt_fechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreador` int NOT NULL,
  `int_idUsuarioModificador` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_unidadesnegocios`
--

INSERT INTO `tm_unidadesnegocios` (`int_idUnidadesNegocio`, `str_Descripcion`, `str_idSuscripcion`, `dt_fechaCreacion`, `dt_fechaModificacion`, `int_idUsuarioCreador`, `int_idUsuarioModificador`) VALUES
(1, 'Física', '1346798255', '2024-09-19 18:04:15', '2024-09-19 23:04:15', 1, 24),
(2, 'Tecnologia', '1346798255', '2024-09-22 17:35:56', '2024-09-22 22:35:55', 1, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_usuarios`
--

CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Documento` int NOT NULL,
  `str_UnidadNegocio` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idEspecialidad` int DEFAULT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_Estado` int DEFAULT '1',
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_usuarios`
--

INSERT INTO `tm_usuarios` (`int_idUsuarios`, `str_Nombres`, `str_Apellidos`, `str_Correo`, `int_Documento`, `str_UnidadNegocio`, `int_idEspecialidad`, `str_Clave`, `int_Estado`, `str_Codigo`, `dt_FechaCreacion`, `dt_FechaModificacion`, `str_idUsuarioCreacion`, `str_idUsuarioModificacion`) VALUES
(1, 'Jose Maria', 'Torres Chirinos', '<EMAIL>', 2, 'wfwfw', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(24, 'Lauren David', 'Arica guerrero', '<EMAIL>', 12346785, '<EMAIL>', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(26, 'Nicole Alexa', 'Alva ', '<EMAIL>', 14789632, 'Unidad de Negocio', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(27, 'Usuario', 'Aprobador 1', '<EMAIL>', 12369854, '<EMAIL>', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(28, 'Usuario', 'Aprobador 2', '<EMAIL>', 12369854, 'wrgwrgwrg', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(29, 'Usuario', 'Aprobador 3', '<EMAIL>', 12369854, 'wrgwrgwrg', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(30, 'Usuario', 'Aprobador 4', '<EMAIL>', 12369854, 'wrgwrgwrg', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(33, 'Usuario2', 'Aprobador 4', '<EMAIL>', 123467854, '66652fwf', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_aprobadores`
--

CREATE TABLE `tr_aprobadores` (
  `int_idAprobador` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuario` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaAceptacion` datetime DEFAULT NULL,
  `int_OrdenAprobacion` tinyint(1) NOT NULL,
  `int_EstadoAprobacion` tinyint(1) NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_aprobadores`
--

INSERT INTO `tr_aprobadores` (`int_idAprobador`, `str_idSuscripcion`, `int_idUsuario`, `int_idSolicitudes`, `dt_FechaAceptacion`, `int_OrdenAprobacion`, `int_EstadoAprobacion`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(10, '1346798255', 33, 188, NULL, 1, 0, '2024-09-26 18:12:22', NULL, 26, NULL),
(11, '1346798255', 29, 188, NULL, 2, 0, '2024-09-26 18:12:22', NULL, 26, NULL),
(12, '1346798255', 27, 188, NULL, 3, 0, '2024-09-26 18:12:22', NULL, 26, NULL),
(13, '1346798255', 28, 187, NULL, 1, 0, '2024-09-27 12:12:23', NULL, 26, NULL),
(14, '1346798255', 33, 187, NULL, 2, 0, '2024-09-27 12:12:23', NULL, 26, NULL),
(15, '1346798255', 30, 187, NULL, 3, 0, '2024-09-27 12:12:23', NULL, 26, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_archivosdocumentos`
--

CREATE TABLE `tr_archivosdocumentos` (
  `int_idArchivos` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idTipoDocumento` int NOT NULL,
  `str_RutaArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ExtencionArchivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_TamañoArchivo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_archivosdocumentos`
--

INSERT INTO `tr_archivosdocumentos` (`int_idArchivos`, `str_idSuscriptor`, `int_idSolicitudes`, `int_idTipoDocumento`, `str_RutaArchivo`, `str_NombreArchivo`, `str_ExtencionArchivo`, `str_TamañoArchivo`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(59, '1346798255', 189, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\DOAD\\prisma_seguridad (6).sql', 'prisma_seguridad (6)', 'sql', '123757', '2024-09-24 17:21:09', NULL, 1, NULL),
(62, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_seguridad (7).sql', 'prisma_seguridad (7)', 'sql', '123757', '2024-09-24 17:47:31', NULL, 1, NULL),
(63, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_desarrollo (11).sql', 'prisma_desarrollo (11)', 'sql', '58422', '2024-09-24 17:47:32', NULL, 1, NULL),
(64, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_seguridad (6).sql', 'prisma_seguridad (6)', 'sql', '123757', '2024-09-24 17:47:32', NULL, 1, NULL),
(65, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_desarrollo (10).sql', 'prisma_desarrollo (10)', 'sql', '55196', '2024-09-24 17:47:32', NULL, 1, NULL),
(66, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_seguridad (5).sql', 'prisma_seguridad (5)', 'sql', '123358', '2024-09-24 17:47:32', NULL, 1, NULL),
(67, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_desarrollo (9).sql', 'prisma_desarrollo (9)', 'sql', '52675', '2024-09-24 17:47:32', NULL, 1, NULL),
(68, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_desarrollo (2) (1).sql', 'prisma_desarrollo (2) (1)', 'sql', '50739', '2024-09-24 17:47:32', NULL, 1, NULL),
(69, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_seguridad (2) (1).sql', 'prisma_seguridad (2) (1)', 'sql', '122062', '2024-09-24 17:47:32', NULL, 1, NULL),
(70, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_seguridad (4).sql', 'prisma_seguridad (4)', 'sql', '122062', '2024-09-24 17:47:32', NULL, 1, NULL),
(71, '1346798255', 188, 1, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CPRS-202409027\\DOAD\\prisma_desarrollo (8).sql', 'prisma_desarrollo (8)', 'sql', '69850', '2024-09-24 17:47:32', NULL, 1, NULL),
(72, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version2.pdf', 'archivo_descargado-version2', 'pdf', '121427', '2024-09-26 17:46:12', NULL, 26, NULL),
(73, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado.pdf', 'archivo_descargado', 'pdf', '121427', '2024-09-26 17:46:12', NULL, 26, NULL),
(74, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version3.pdf', 'archivo_descargado-version3', 'pdf', '121427', '2024-09-26 17:46:12', NULL, 26, NULL),
(75, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version4.pdf', 'archivo_descargado-version4', 'pdf', '121427', '2024-09-26 17:46:12', NULL, 26, NULL),
(76, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version5.pdf', 'archivo_descargado-version5', 'pdf', '121427', '2024-09-26 17:47:08', NULL, 26, NULL),
(77, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version6.pdf', 'archivo_descargado-version6', 'pdf', '121427', '2024-09-26 17:47:08', NULL, 26, NULL),
(78, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version7.pdf', 'archivo_descargado-version7', 'pdf', '121427', '2024-09-26 17:47:37', NULL, 26, NULL),
(79, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version8.pdf', 'archivo_descargado-version8', 'pdf', '121427', '2024-09-26 17:47:37', NULL, 26, NULL),
(80, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version9.pdf', 'archivo_descargado-version9', 'pdf', '121427', '2024-09-26 17:48:44', NULL, 26, NULL),
(81, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version10.pdf', 'archivo_descargado-version10', 'pdf', '121427', '2024-09-26 17:50:07', NULL, 26, NULL),
(82, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\Plantilla_CLOS-202409004.pdf', 'Plantilla_CLOS-202409004', 'pdf', '90177', '2024-09-26 17:52:13', NULL, 26, NULL),
(83, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\Plantilla_CLOS-202409004-version2.pdf', 'Plantilla_CLOS-202409004-version2', 'pdf', '90177', '2024-09-26 18:11:16', NULL, 26, NULL),
(84, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version11.pdf', 'archivo_descargado-version11', 'pdf', '121427', '2024-09-27 10:49:36', NULL, 26, NULL),
(85, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version12.pdf', 'archivo_descargado-version12', 'pdf', '121427', '2024-09-27 10:56:31', NULL, 26, NULL),
(86, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version13.pdf', 'archivo_descargado-version13', 'pdf', '121427', '2024-09-27 11:04:23', NULL, 26, NULL),
(87, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version14.pdf', 'archivo_descargado-version14', 'pdf', '121427', '2024-09-27 11:08:04', NULL, 26, NULL),
(88, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version15.pdf', 'archivo_descargado-version15', 'pdf', '121427', '2024-09-27 11:13:01', NULL, 26, NULL),
(89, '1346798255', 189, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CLOS-202409004\\COAP\\archivo_descargado-version16.pdf', 'archivo_descargado-version16', 'pdf', '121427', '2024-09-27 11:50:13', NULL, 26, NULL),
(90, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado.pdf', 'archivo_descargado', 'pdf', '121427', '2024-09-27 15:37:06', NULL, 26, NULL),
(91, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version2.pdf', 'archivo_descargado-version2', 'pdf', '121427', '2024-09-27 15:41:59', NULL, 26, NULL),
(92, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version3.pdf', 'archivo_descargado-version3', 'pdf', '121427', '2024-09-27 15:56:57', NULL, 26, NULL),
(93, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version4.pdf', 'archivo_descargado-version4', 'pdf', '121427', '2024-09-27 15:57:36', NULL, 26, NULL),
(94, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version5.pdf', 'archivo_descargado-version5', 'pdf', '121427', '2024-09-27 16:02:27', NULL, 26, NULL),
(95, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version6.pdf', 'archivo_descargado-version6', 'pdf', '121427', '2024-09-27 16:03:51', NULL, 26, NULL),
(96, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version7.pdf', 'archivo_descargado-version7', 'pdf', '121427', '2024-09-27 16:04:52', NULL, 26, NULL),
(97, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version8.pdf', 'archivo_descargado-version8', 'pdf', '121427', '2024-09-27 16:05:53', NULL, 26, NULL),
(98, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version9.pdf', 'archivo_descargado-version9', 'pdf', '121427', '2024-09-27 16:06:51', NULL, 26, NULL),
(99, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version10.pdf', 'archivo_descargado-version10', 'pdf', '121427', '2024-09-27 16:11:16', NULL, 26, NULL),
(100, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version11.pdf', 'archivo_descargado-version11', 'pdf', '121427', '2024-09-27 16:11:54', NULL, 26, NULL),
(101, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version12.pdf', 'archivo_descargado-version12', 'pdf', '121427', '2024-09-27 16:19:55', NULL, 26, NULL),
(102, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version13.pdf', 'archivo_descargado-version13', 'pdf', '121427', '2024-09-27 17:30:43', NULL, 26, NULL),
(103, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version14.pdf', 'archivo_descargado-version14', 'pdf', '121427', '2024-09-27 17:32:46', NULL, 26, NULL),
(104, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version15.pdf', 'archivo_descargado-version15', 'pdf', '121427', '2024-09-27 17:33:57', NULL, 26, NULL),
(105, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\RHE10726405684E00115.pdf', 'RHE10726405684E00115', 'pdf', '2553', '2024-09-27 17:35:18', NULL, 26, NULL),
(106, '1346798255', 187, 3, 'E:\\José Torres\\Prisma\\Contratos\\backend-prisma-contratos\\backendPrismaProcesos\\media\\1346798255\\CACO-202409014\\COAP\\archivo_descargado-version16.pdf', 'archivo_descargado-version16', 'pdf', '121427', '2024-09-27 17:45:19', NULL, 26, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_clausulasactivas`
--

CREATE TABLE `tr_clausulasactivas` (
  `int_idClausulasActivas` int NOT NULL,
  `str_idSuscripcion` int NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `int_idClausulaLegal` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_clausulasincluidas`
--

CREATE TABLE `tr_clausulasincluidas` (
  `int_idClausulasIncluidas` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `int_idClausulaLegal` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_clausulasincluidas`
--

INSERT INTO `tr_clausulasincluidas` (`int_idClausulasIncluidas`, `str_idSuscripcion`, `int_idTipoSolicitud`, `int_idClausulaLegal`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, '1346798255', 1, 2, '2024-09-12 17:27:45', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_consorcio`
--

CREATE TABLE `tr_consorcio` (
  `int_idConsorcio` int NOT NULL,
  `str_idCorrelativo` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idInterlocutor` int DEFAULT NULL,
  `str_NombreConsorcio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Oblicacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_CantidadObligacion` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime NOT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_consorcioporsolicitud`
--

CREATE TABLE `tr_consorcioporsolicitud` (
  `int_idConsorcioPorSolicitud` int NOT NULL,
  `int_idConsorcio` int NOT NULL,
  `int_idSolicitudCont` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_correlativosolicitud`
--

CREATE TABLE `tr_correlativosolicitud` (
  `int_idCorrelativo` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `str_CorrelativoTipoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_correlativosolicitud`
--

INSERT INTO `tr_correlativosolicitud` (`int_idCorrelativo`, `str_idSuscriptor`, `int_idTipoSolicitud`, `str_CorrelativoTipoSolicitud`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(6, '1346798255', 1, '027', '2024-09-20 14:43:52', '2024-09-24 16:56:09', NULL, 1),
(7, '1346798255', 2, '004', '2024-09-22 20:27:27', '2024-09-24 17:18:01', NULL, 1),
(8, '1346798255', 3, '003', '2024-09-23 10:08:06', '2024-09-24 16:45:58', NULL, 1),
(9, '1346798255', 7, '014', '2024-09-23 11:13:47', '2024-09-24 16:52:50', NULL, 1),
(10, '1346798255', 4, '036', '2024-09-23 17:48:51', '2024-09-24 15:14:03', NULL, 1),
(11, '1346798255', 5, '041', '2024-09-24 15:32:31', '2024-09-24 16:42:30', NULL, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_estadoregistros`
--

CREATE TABLE `tr_estadoregistros` (
  `int_idEstadoRegistros` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idEstado` int NOT NULL,
  `dt_FechaCambio` datetime NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_estadoregistros`
--

INSERT INTO `tr_estadoregistros` (`int_idEstadoRegistros`, `str_idSuscriptor`, `int_idSolicitudes`, `int_idEstado`, `dt_FechaCambio`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(28, '1346798255', 187, 2, '2024-09-24 16:55:10', '2024-09-24 16:55:10', NULL, 1, NULL),
(29, '1346798255', 187, 5, '2024-09-24 17:15:26', '2024-09-24 17:15:26', NULL, 1, NULL),
(30, '1346798255', 187, 6, '2024-09-26 11:54:38', '2024-09-26 11:54:38', NULL, 26, NULL),
(31, '1346798255', 189, 2, '2024-09-26 15:02:16', '2024-09-26 15:02:16', NULL, 1, NULL),
(32, '1346798255', 188, 2, '2024-09-26 18:02:15', '2024-09-26 18:02:15', NULL, 1, NULL),
(33, '1346798255', 188, 5, '2024-09-26 18:03:15', '2024-09-26 18:03:15', NULL, 1, NULL),
(34, '1346798255', 188, 6, '2024-09-26 18:03:57', '2024-09-26 18:03:57', NULL, 26, NULL),
(35, '1346798255', 189, 2, '2024-09-26 18:08:54', '2024-09-26 18:08:54', NULL, 1, NULL),
(36, '1346798255', 188, 5, '2024-09-26 18:11:58', '2024-09-26 18:11:58', NULL, 1, NULL),
(37, '1346798255', 188, 6, '2024-09-26 18:12:23', '2024-09-26 18:12:23', NULL, 26, NULL),
(38, '1346798255', 187, 6, '2024-09-27 09:42:11', '2024-09-27 09:42:11', NULL, 26, NULL),
(39, '1346798255', 187, 6, '2024-09-27 09:42:40', '2024-09-27 09:42:40', NULL, 26, NULL),
(40, '1346798255', 187, 6, '2024-09-27 09:43:26', '2024-09-27 09:43:26', NULL, 26, NULL),
(41, '1346798255', 187, 6, '2024-09-27 09:44:17', '2024-09-27 09:44:17', NULL, 26, NULL),
(42, '1346798255', 189, 4, '2024-09-27 11:50:21', '2024-09-27 11:50:21', NULL, 26, NULL),
(43, '1346798255', 187, 6, '2024-09-27 12:12:23', '2024-09-27 12:12:23', NULL, 26, NULL),
(44, '1346798255', 187, 4, '2024-09-27 16:02:27', '2024-09-27 16:02:27', NULL, 26, NULL),
(45, '1346798255', 187, 4, '2024-09-27 16:03:51', '2024-09-27 16:03:51', NULL, 26, NULL),
(46, '1346798255', 187, 4, '2024-09-27 16:04:52', '2024-09-27 16:04:52', NULL, 26, NULL),
(47, '1346798255', 187, 4, '2024-09-27 16:05:54', '2024-09-27 16:05:54', NULL, 26, NULL),
(48, '1346798255', 187, 4, '2024-09-27 16:06:52', '2024-09-27 16:06:52', NULL, 26, NULL),
(49, '1346798255', 187, 4, '2024-09-27 16:11:17', '2024-09-27 16:11:17', NULL, 26, NULL),
(50, '1346798255', 187, 4, '2024-09-27 16:11:55', '2024-09-27 16:11:55', NULL, 26, NULL),
(51, '1346798255', 187, 8, '2024-09-27 17:30:44', '2024-09-27 17:30:44', NULL, 26, NULL),
(52, '1346798255', 187, 8, '2024-09-27 17:32:47', '2024-09-27 17:32:47', NULL, 26, NULL),
(53, '1346798255', 187, 8, '2024-09-27 17:33:58', '2024-09-27 17:33:58', NULL, 26, NULL),
(54, '1346798255', 187, 8, '2024-09-27 17:35:18', '2024-09-27 17:35:18', NULL, 26, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_solicitudcont`
--

CREATE TABLE `tr_solicitudcont` (
  `int_idSolicitudCont` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idInterlocutor` int NOT NULL,
  `int_idInterlocutorComprador` int DEFAULT NULL,
  `int_NumAsociados` int DEFAULT NULL,
  `str_Moneda` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `db_Presupuesto` double DEFAULT NULL,
  `str_Margen` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PlazoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TipoServicio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_InfoAdicional` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DocAdjuntos` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_CondicionPago` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ConsultorAsignado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RenovacionAuto` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleRenovAuto` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_AjusteHonorarios` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleAjusteHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Garantia` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleGarantia` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ResolucionAnticipada` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleResolucionAnticipada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Penalidades` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetallePenalidades` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienMuebleInmueble` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienPartidaCertificada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDireccion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienUso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDescripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObjetivoContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_MonedaContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RentaPactada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ImporteVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_FormaPago` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PlazoArriendo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaArriendo` datetime DEFAULT NULL,
  `dt_FechaVenta` datetime DEFAULT NULL,
  `str_InteresRetraso` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObligacionesConjuntas` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_GarantiaVenta` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleGarantiaVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_InfoCompartida` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_solicitudcont`
--

INSERT INTO `tr_solicitudcont` (`int_idSolicitudCont`, `str_idSuscriptor`, `int_idSolicitudes`, `int_idInterlocutor`, `int_idInterlocutorComprador`, `int_NumAsociados`, `str_Moneda`, `db_Presupuesto`, `str_Margen`, `str_PlazoSolicitud`, `str_TipoServicio`, `str_InfoAdicional`, `str_DocAdjuntos`, `str_CondicionPago`, `str_ConsultorAsignado`, `str_RenovacionAuto`, `str_DetalleRenovAuto`, `str_AjusteHonorarios`, `str_DetalleAjusteHonorarios`, `str_Garantia`, `str_DetalleGarantia`, `str_ResolucionAnticipada`, `str_DetalleResolucionAnticipada`, `str_Penalidades`, `str_DetallePenalidades`, `str_BienMuebleInmueble`, `str_BienPartidaCertificada`, `str_BienDireccion`, `str_BienUso`, `str_BienDescripcion`, `str_ObjetivoContrato`, `str_MonedaContrato`, `str_RentaPactada`, `str_ImporteVenta`, `str_FormaPago`, `str_PlazoArriendo`, `dt_FechaArriendo`, `dt_FechaVenta`, `str_InteresRetraso`, `str_ObligacionesConjuntas`, `str_GarantiaVenta`, `str_DetalleGarantiaVenta`, `str_InfoCompartida`) VALUES
(55, '1346798255', 187, 47, NULL, NULL, 'soles', NULL, NULL, 'ujill', '', 'ilioñiiñioñ', 'no', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '7lululu', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ugjgkh', NULL, NULL, NULL),
(56, '1346798255', 188, 48, NULL, NULL, 'dolares', 5000, '1 semana', '2 dias', 'Electrónica', '7\'7\'7\'78', 'no', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'u9pu9pupu9p', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(57, '1346798255', 189, 49, NULL, NULL, 'dolares', NULL, NULL, '2 dias', 'Electrónica', 'uúúoúo´´ou', 'no', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '´jjjjyyjjy', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_solicitudes`
--

CREATE TABLE `tr_solicitudes` (
  `int_idSolicitudes` int NOT NULL,
  `str_CodSolicitudes` varchar(17) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitante` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idEmpresa` int NOT NULL,
  `int_idEstado` int NOT NULL,
  `int_idTipoSol` int NOT NULL,
  `int_idUnidadNegocio` int DEFAULT NULL,
  `int_SolicitudGuardada` int DEFAULT '0',
  `str_DeTerceros` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `db_Honorarios` double DEFAULT NULL,
  `dt_FirmaContrato` datetime DEFAULT NULL,
  `int_HorasTrabajadas` int DEFAULT NULL,
  `str_Visible` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaRegistro` datetime NOT NULL,
  `dt_FechaEsperada` datetime NOT NULL,
  `int_idGestor` int DEFAULT NULL,
  `int_idClienteAsociado` int DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_solicitudes`
--

INSERT INTO `tr_solicitudes` (`int_idSolicitudes`, `str_CodSolicitudes`, `int_idSolicitante`, `str_idSuscriptor`, `int_idEmpresa`, `int_idEstado`, `int_idTipoSol`, `int_idUnidadNegocio`, `int_SolicitudGuardada`, `str_DeTerceros`, `db_Honorarios`, `dt_FirmaContrato`, `int_HorasTrabajadas`, `str_Visible`, `dt_FechaRegistro`, `dt_FechaEsperada`, `int_idGestor`, `int_idClienteAsociado`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(187, 'CACO-202409014', 1, '1346798255', 2, 8, 6, 1, 0, 'si', 0, '2024-09-02 17:35:10', 50, 'no', '2024-09-24 16:52:50', '2024-09-28 05:00:00', 26, NULL, '2024-09-24 16:52:50', '2024-09-27 17:53:58', 1, NULL),
(188, 'CPRS-202409027', 1, '1346798255', 2, 6, 1, 1, 0, 'no', 25000, NULL, NULL, 'si', '2024-09-24 16:56:09', '2024-09-18 21:55:35', 26, NULL, '2024-09-24 16:56:09', '2024-09-26 18:12:23', 1, 26),
(189, 'CLOS-202409004', 1, '1346798255', 3, 4, 3, 1, 0, 'no', 80000, NULL, NULL, 'si', '2024-09-24 17:18:01', '2024-09-28 05:00:00', 26, NULL, '2024-09-24 17:18:01', '2024-09-27 11:50:21', 1, 26);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_tags`
--

CREATE TABLE `tr_tags` (
  `int_idTags` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_tags`
--

INSERT INTO `tr_tags` (`int_idTags`, `int_idSolicitudes`, `str_descripcion`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, 1, 'admin', '2024-09-11 14:58:23', '2024-09-12 19:58:23', 1, 1),
(3, 1, 'efff', '2024-09-12 20:17:27', '2024-09-12 20:17:27', 1, 1),
(4, 1, 'tag', '2024-09-13 08:56:45', NULL, 1, NULL),
(5, 2, 't', '2024-09-19 09:11:13', NULL, 1, NULL),
(6, 189, 'tagsito', '2024-09-27 10:50:42', NULL, 26, NULL),
(7, 189, 'tag2', '2024-09-27 10:50:42', NULL, 26, NULL),
(8, 189, 'f', '2024-09-27 10:50:42', NULL, 26, NULL),
(9, 189, 'tagsito', '2024-09-27 10:53:15', NULL, 26, NULL),
(10, 189, 'tag2', '2024-09-27 10:53:15', NULL, 26, NULL),
(11, 189, 'f', '2024-09-27 10:53:15', NULL, 26, NULL),
(12, 189, 'tagsito', '2024-09-27 10:54:59', NULL, 26, NULL),
(13, 189, 'tag2', '2024-09-27 10:54:59', NULL, 26, NULL),
(14, 189, 'f', '2024-09-27 10:54:59', NULL, 26, NULL),
(15, 189, 'adad', '2024-09-27 10:56:39', NULL, 26, NULL),
(16, 189, 'adadad', '2024-09-27 10:56:39', NULL, 26, NULL),
(17, 189, 'adad', '2024-09-27 11:04:28', NULL, 26, NULL),
(18, 189, 'adadad', '2024-09-27 11:04:28', NULL, 26, NULL),
(19, 189, 'adad', '2024-09-27 11:06:24', NULL, 26, NULL),
(20, 189, 'adadad', '2024-09-27 11:06:24', NULL, 26, NULL),
(21, 189, 'adad', '2024-09-27 11:06:39', NULL, 26, NULL),
(22, 189, 'adadad', '2024-09-27 11:06:39', NULL, 26, NULL),
(23, 189, 'adad', '2024-09-27 11:08:11', NULL, 26, NULL),
(24, 189, 'adadad', '2024-09-27 11:08:11', NULL, 26, NULL),
(25, 189, 'adad', '2024-09-27 11:13:05', NULL, 26, NULL),
(26, 189, 'adadad', '2024-09-27 11:13:05', NULL, 26, NULL),
(27, 189, 'adad', '2024-09-27 11:50:20', NULL, 26, NULL),
(28, 189, 'adadad', '2024-09-27 11:50:20', NULL, 26, NULL);

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD PRIMARY KEY (`int_idEmpresa`),
  ADD KEY `empresas_suscriptor_FK` (`str_idSuscripcion`),
  ADD KEY `empresas_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `empresas_usuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  ADD PRIMARY KEY (`int_idClausulasLegales`),
  ADD KEY `clausulasLegales_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `clausulasLegales_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `clausulas_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD PRIMARY KEY (`int_idEspecialidades`),
  ADD KEY `Especialidad_Suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD PRIMARY KEY (`int_idEstado`),
  ADD KEY `Estados_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Estados_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Estados_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD PRIMARY KEY (`int_idInterlocutor`),
  ADD KEY `Interlocutor_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Interlocutor_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Interlocutor_Suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD PRIMARY KEY (`str_idSuscripcion`),
  ADD KEY `Suscripciones_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Suscripciones_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD PRIMARY KEY (`int_idTipoDocumentos`),
  ADD KEY `TipoDocumentos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoDocumentos_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  ADD PRIMARY KEY (`int_idTipoSolicitud`),
  ADD UNIQUE KEY `codigo_tipoSolicitud` (`int_idTipoSolicitud`),
  ADD KEY `TipoSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `TipoSolicitud_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_unidadesnegocios`
--
ALTER TABLE `tm_unidadesnegocios`
  ADD PRIMARY KEY (`int_idUnidadesNegocio`),
  ADD KEY `unidadNegocio_Suscripcion_FK` (`str_idSuscripcion`),
  ADD KEY `unidadNegocio_UsuarioCreador_FK` (`int_idUsuarioCreador`),
  ADD KEY `unidadNegocio_UsuarioModificador_FK` (`int_idUsuarioModificador`);

--
-- Indices de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  ADD PRIMARY KEY (`int_idUsuarios`) USING BTREE,
  ADD UNIQUE KEY `correo` (`str_Correo`);

--
-- Indices de la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  ADD PRIMARY KEY (`int_idAprobador`),
  ADD KEY `aprobador_suscriptor_FK` (`str_idSuscripcion`),
  ADD KEY `aprobador_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `aprobador_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `aprobador_solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `aprobador_Usuario_FK` (`int_idUsuario`);

--
-- Indices de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD PRIMARY KEY (`int_idArchivos`),
  ADD KEY `archivos_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `archivos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `archivos_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `archivos_tipoDocumentos_FK` (`int_idTipoDocumento`),
  ADD KEY `archivos_solicitud_FK` (`int_idSolicitudes`);

--
-- Indices de la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  ADD PRIMARY KEY (`int_idClausulasActivas`),
  ADD KEY `ClausulasActivas_tipoSol_FK` (`int_idTipoSolicitud`),
  ADD KEY `ClausulasActivas_Clausula_FK` (`int_idClausulaLegal`),
  ADD KEY `ClausulasActivas_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ClausulasActivas_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `ClausulasActivas_Solicitudes_FK` (`int_idSolicitudes`),
  ADD KEY `ClausulasActivas_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  ADD PRIMARY KEY (`int_idClausulasIncluidas`),
  ADD KEY `ClausulasIncluida_tipoSol_FK` (`int_idTipoSolicitud`),
  ADD KEY `ClausulasIncluida_Clausula_FK` (`int_idClausulaLegal`),
  ADD KEY `ClausulasIncluida_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ClausulasIncluida_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `ClausulasIncluidas_solicitud_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  ADD PRIMARY KEY (`int_idConsorcio`),
  ADD KEY `Consorcio_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Consorcio_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Consorcio_Interlocutor_FK` (`int_idInterlocutor`);

--
-- Indices de la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  ADD PRIMARY KEY (`int_idConsorcioPorSolicitud`),
  ADD KEY `Consorio_SolicitudCont_FK` (`int_idConsorcio`),
  ADD KEY `SolicitudCont_Consorio_FK` (`int_idSolicitudCont`),
  ADD KEY `ConsorcioSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ConsorcioSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  ADD PRIMARY KEY (`int_idCorrelativo`),
  ADD KEY `correlativo_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `correlativo_tipoSolicitud_FK` (`int_idTipoSolicitud`),
  ADD KEY `correlativo_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `correlativo_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  ADD PRIMARY KEY (`int_idEstadoRegistros`),
  ADD KEY `EstadoRegistros_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `EstadoRegistros_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `EstadoRegistros_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `EstadoRegistros_Estado_FK` (`int_idEstado`),
  ADD KEY `EstadoRegistros_Solicitudes_FK` (`int_idSolicitudes`);

--
-- Indices de la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  ADD PRIMARY KEY (`int_idSolicitudCont`),
  ADD KEY `SolicitudCont_Solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `SolicitudCont_Suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `SolicitudCont_Interlocutor_FK` (`int_idInterlocutor`),
  ADD KEY ` SolicitudCont_InterlocutorComprador_FK` (`int_idInterlocutorComprador`);

--
-- Indices de la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  ADD PRIMARY KEY (`int_idSolicitudes`),
  ADD KEY `solicitud_usuario_FK` (`int_idSolicitante`),
  ADD KEY `solicitud_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `solicitud_empresa_FK` (`int_idEmpresa`),
  ADD KEY `solicitud_usuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `solicitud_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `solicitud_Gestor_FK` (`int_idGestor`),
  ADD KEY `solicitud_tipoSolicitud_FK` (`int_idTipoSol`),
  ADD KEY `solicitud_clienteAsociado_FK` (`int_idClienteAsociado`),
  ADD KEY `solicitud_estados_FK` (`int_idEstado`),
  ADD KEY `Solicitud_Unidad_Negocio_FK` (`int_idUnidadNegocio`);

--
-- Indices de la tabla `tr_tags`
--
ALTER TABLE `tr_tags`
  ADD PRIMARY KEY (`int_idTags`),
  ADD KEY `tags_solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `tags_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `tags_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  MODIFY `int_idEmpresa` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  MODIFY `int_idClausulasLegales` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  MODIFY `int_idEspecialidades` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  MODIFY `int_idEstado` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  MODIFY `int_idInterlocutor` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  MODIFY `int_idTipoDocumentos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  MODIFY `int_idTipoSolicitud` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT de la tabla `tm_unidadesnegocios`
--
ALTER TABLE `tm_unidadesnegocios`
  MODIFY `int_idUnidadesNegocio` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  MODIFY `int_idUsuarios` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- AUTO_INCREMENT de la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  MODIFY `int_idAprobador` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  MODIFY `int_idArchivos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=107;

--
-- AUTO_INCREMENT de la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  MODIFY `int_idClausulasActivas` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  MODIFY `int_idClausulasIncluidas` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  MODIFY `int_idConsorcio` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  MODIFY `int_idConsorcioPorSolicitud` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  MODIFY `int_idCorrelativo` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT de la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  MODIFY `int_idEstadoRegistros` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- AUTO_INCREMENT de la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  MODIFY `int_idSolicitudCont` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=58;

--
-- AUTO_INCREMENT de la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  MODIFY `int_idSolicitudes` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=190;

--
-- AUTO_INCREMENT de la tabla `tr_tags`
--
ALTER TABLE `tr_tags`
  MODIFY `int_idTags` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD CONSTRAINT `empresas_suscriptor_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  ADD CONSTRAINT `clausulas_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `clausulasLegales_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `clausulasLegales_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD CONSTRAINT `Especialidad_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD CONSTRAINT `Estados_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD CONSTRAINT `Interlocutor_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Interlocutor_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Interlocutor_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD CONSTRAINT `Suscripciones_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Suscripciones_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD CONSTRAINT `TipoDocumentos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoDocumentos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  ADD CONSTRAINT `TipoSolicitud_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoSolicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoSolicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_unidadesnegocios`
--
ALTER TABLE `tm_unidadesnegocios`
  ADD CONSTRAINT `unidadNegocio_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `unidadNegocio_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `unidadNegocio_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  ADD CONSTRAINT `aprobador_solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `aprobador_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `aprobador_Usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `aprobador_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `aprobador_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD CONSTRAINT `archivos_solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `archivos_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_tipoDocumentos_FK` FOREIGN KEY (`int_idTipoDocumento`) REFERENCES `tm_tipodocumento` (`int_idTipoDocumentos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  ADD CONSTRAINT `ClausulasActivas_Clausulas_FK` FOREIGN KEY (`int_idClausulaLegal`) REFERENCES `tm_clausulaslegales` (`int_idClausulasLegales`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_Solicitudes_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  ADD CONSTRAINT `ClausulasIncluida_Clausulas_FK` FOREIGN KEY (`int_idClausulaLegal`) REFERENCES `tm_clausulaslegales` (`int_idClausulasLegales`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluida_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluida_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluidas_solicitud_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluidas_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  ADD CONSTRAINT `Consorcio_Interlocutor_FK` FOREIGN KEY (`int_idInterlocutor`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Consorcio_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Consorcio_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  ADD CONSTRAINT `ConsorcioSolicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ConsorcioSolicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Consorio_SolicitudCont_FK` FOREIGN KEY (`int_idConsorcio`) REFERENCES `tr_consorcio` (`int_idConsorcio`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `SolicitudCont_Consorio_FK` FOREIGN KEY (`int_idSolicitudCont`) REFERENCES `tr_solicitudcont` (`int_idSolicitudCont`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  ADD CONSTRAINT `correlativo_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  ADD CONSTRAINT `EstadoRegistros_Estado_FK` FOREIGN KEY (`int_idEstado`) REFERENCES `tm_estados` (`int_idEstado`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_Solicitudes_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `EstadoRegistros_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  ADD CONSTRAINT ` SolicitudCont_InterlocutorComprador_FK` FOREIGN KEY (`int_idInterlocutorComprador`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `SolicitudCont_Interlocutor_FK` FOREIGN KEY (`int_idInterlocutor`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `SolicitudCont_solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE CASCADE ON UPDATE RESTRICT,
  ADD CONSTRAINT `SolicitudCont_Suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  ADD CONSTRAINT `Solicitud_ClienteAsociado_FK` FOREIGN KEY (`int_idClienteAsociado`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Empresa_FK` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Estado_FK` FOREIGN KEY (`int_idEstado`) REFERENCES `tm_estados` (`int_idEstado`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Gestor_FK` FOREIGN KEY (`int_idGestor`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Solicitante_FK` FOREIGN KEY (`int_idSolicitante`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSol`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Unidad_Negocio_FK` FOREIGN KEY (`int_idUnidadNegocio`) REFERENCES `tm_unidadesnegocios` (`int_idUnidadesNegocio`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
