-- MySQL dump 10.13  Distrib 8.0.39, for Linux (x86_64)
--
-- Host: localhost    Database: db_prisma_contratos
-- ------------------------------------------------------
-- Server version       8.0.39-0ubuntu0.24.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tc_empresas`
--

DROP TABLE IF EXISTS `tc_empresas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tc_empresas` (
  `int_idEmpresa` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEmpresa` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RazonSocial` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_Pais` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Moneda` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_SimboloMoneda` varchar(50) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  PRIMARY KEY (`int_idEmpresa`),
  KEY `empresas_suscriptor_FK` (`str_idSuscripcion`),
  KEY `empresas_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `empresas_usuarioModificador_FK` (`int_idUsuarioModificacion`),
  CONSTRAINT `empresas_suscriptor_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_CredentialBot`
--

DROP TABLE IF EXISTS `tm_CredentialBot`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_CredentialBot` (
  `int_idCredentialBot` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(15) NOT NULL,
  `str_claveApi` varchar(255) NOT NULL,
  `dt_fechaCambio` datetime DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `bool_estado` tinyint NOT NULL,
  PRIMARY KEY (`int_idCredentialBot`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_clausulaslegales`
--

DROP TABLE IF EXISTS `tm_clausulaslegales`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_clausulaslegales` (
  `int_idClausulasLegales` int NOT NULL AUTO_INCREMENT,
  `str_CodClausulasLegales` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idEmpresa` int DEFAULT NULL,
  PRIMARY KEY (`int_idClausulasLegales`),
  KEY `clausulasLegales_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `clausulasLegales_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `clausulas_suscripcion_FK` (`str_idSuscripcion`),
  CONSTRAINT `clausulas_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clausulasLegales_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `clausulasLegales_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_especialidades`
--

DROP TABLE IF EXISTS `tm_especialidades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_especialidades` (
  `int_idEspecialidades` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodigoEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idEspecialidades`),
  KEY `Especialidad_Suscripcion_FK` (`str_idSuscripcion`),
  CONSTRAINT `Especialidad_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_estados`
--

DROP TABLE IF EXISTS `tm_estados`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_estados` (
  `int_idEstado` int NOT NULL AUTO_INCREMENT,
  `str_codEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idEstado`),
  KEY `Estados_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `Estados_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `Estados_suscripcion_FK` (`str_idSuscripcion`),
  CONSTRAINT `Estados_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Estados_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Estados_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_interlocutores`
--

DROP TABLE IF EXISTS `tm_interlocutores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_interlocutores` (
  `int_idInterlocutor` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Interlocutor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TipoDoc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Documento` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Domicilio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Correo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_obligaciones` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_ValorAporte` int DEFAULT NULL,
  `int_PorcentajeAporte` float DEFAULT NULL,
  `str_ValorServicios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ValorHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RepLegal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_RLPartida` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RLTipoDocumento` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RLDocumento` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idInterlocutor`),
  KEY `Interlocutor_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `Interlocutor_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `Interlocutor_Suscripcion_FK` (`str_idSuscripcion`),
  CONSTRAINT `Interlocutor_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Interlocutor_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Interlocutor_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=443 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_suscripcion`
--

DROP TABLE IF EXISTS `tm_suscripcion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`str_idSuscripcion`),
  KEY `Suscripciones_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `Suscripciones_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  CONSTRAINT `Suscripciones_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Suscripciones_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_tipoCambio`
--

DROP TABLE IF EXISTS `tm_tipoCambio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_tipoCambio` (
  `int_idTipoCambio` int NOT NULL AUTO_INCREMENT,
  `str_valorCambio` varchar(50) DEFAULT NULL,
  `int_estado` int DEFAULT NULL,
  `dt_FechaCambio` datetime DEFAULT NULL,
  `str_idSuscripcion` varchar(50) DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idEmpresa` int DEFAULT NULL,
  PRIMARY KEY (`int_idTipoCambio`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_tipodocumento`
--

DROP TABLE IF EXISTS `tm_tipodocumento`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_tipodocumento` (
  `int_idTipoDocumentos` int NOT NULL AUTO_INCREMENT,
  `str_CodTipoDocumento` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idTipoDocumentos`),
  KEY `TipoDocumentos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `TipoDocumentos_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  CONSTRAINT `TipoDocumentos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `TipoDocumentos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_tiposolicitud`
--

DROP TABLE IF EXISTS `tm_tiposolicitud`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_tiposolicitud` (
  `int_idTipoSolicitud` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodTipoSol` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idTipoSolicitud`),
  UNIQUE KEY `codigo_tipoSolicitud` (`int_idTipoSolicitud`),
  KEY `TipoSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `TipoSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `TipoSolicitud_suscripcion_FK` (`str_idSuscripcion`),
  CONSTRAINT `TipoSolicitud_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `TipoSolicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `TipoSolicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_unidadesnegocios`
--

DROP TABLE IF EXISTS `tm_unidadesnegocios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_unidadesnegocios` (
  `int_idUnidadesNegocio` int NOT NULL AUTO_INCREMENT,
  `str_Descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_fechaCreacion` datetime NOT NULL,
  `dt_fechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreador` int NOT NULL,
  `int_idUsuarioModificador` int DEFAULT NULL,
  `int_idEmpresa` int NOT NULL,
  PRIMARY KEY (`int_idUnidadesNegocio`),
  KEY `unidadNegocio_Suscripcion_FK` (`str_idSuscripcion`),
  KEY `unidadNegocio_UsuarioCreador_FK` (`int_idUsuarioCreador`),
  KEY `unidadNegocio_UsuarioModificador_FK` (`int_idUsuarioModificador`),
  CONSTRAINT `unidadNegocio_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `unidadNegocio_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `unidadNegocio_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_usuarios`
--

DROP TABLE IF EXISTS `tm_usuarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL AUTO_INCREMENT,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Documento` varchar(100) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_UnidadNegocio` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idEspecialidad` int DEFAULT NULL,
  `int_Estado` int DEFAULT '1',
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  PRIMARY KEY (`int_idUsuarios`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_aprobadores`
--

DROP TABLE IF EXISTS `tr_aprobadores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_aprobadores` (
  `int_idAprobador` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuario` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaAceptacion` datetime DEFAULT NULL,
  `int_OrdenAprobacion` tinyint(1) NOT NULL,
  `int_EstadoAprobacion` tinyint(1) NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_NumBloque` int DEFAULT NULL,
  PRIMARY KEY (`int_idAprobador`),
  KEY `aprobador_suscriptor_FK` (`str_idSuscripcion`),
  KEY `aprobador_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `aprobador_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `aprobador_solicitud_FK` (`int_idSolicitudes`),
  KEY `aprobador_Usuario_FK` (`int_idUsuario`),
  CONSTRAINT `Aprobador_Solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Aprobador_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Aprobador_Usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_archivosdocumentos`
--

DROP TABLE IF EXISTS `tr_archivosdocumentos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_archivosdocumentos` (
  `int_idArchivos` int NOT NULL AUTO_INCREMENT,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idTipoDocumento` int NOT NULL,
  `str_RutaArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ExtencionArchivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_TamañoArchivo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idArchivos`),
  KEY `archivos_suscriptor_FK` (`str_idSuscriptor`),
  KEY `archivos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `archivos_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `archivos_tipoDocumentos_FK` (`int_idTipoDocumento`),
  KEY `archivos_solicitud_FK` (`int_idSolicitudes`),
  CONSTRAINT `archivos_solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `archivos_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `archivos_tipoDocumentos_FK` FOREIGN KEY (`int_idTipoDocumento`) REFERENCES `tm_tipodocumento` (`int_idTipoDocumentos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `archivos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `archivos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=405 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_clausulasactivas`
--

DROP TABLE IF EXISTS `tr_clausulasactivas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_clausulasactivas` (
  `int_idClausulasActivas` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idClausulaIncluida` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idClausulasActivas`),
  KEY `ClausulasActivas_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `ClausulasActivas_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `ClausulasActivas_Solicitudes_FK` (`int_idSolicitudes`),
  KEY `ClausulasActivas_suscripcion_FK` (`str_idSuscripcion`),
  KEY `ClausulasActivas_Incluida_FK` (`int_idClausulaIncluida`)
) ENGINE=InnoDB AUTO_INCREMENT=260 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_clausulasincluidas`
--

DROP TABLE IF EXISTS `tr_clausulasincluidas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_clausulasincluidas` (
  `int_idClausulasIncluidas` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `int_idClausulaLegal` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idClausulasIncluidas`),
  KEY `ClausulasIncluida_tipoSol_FK` (`int_idTipoSolicitud`),
  KEY `ClausulasIncluida_Clausula_FK` (`int_idClausulaLegal`),
  KEY `ClausulasIncluida_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `ClausulasIncluida_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `ClausulasIncluidas_solicitud_FK` (`str_idSuscripcion`)
) ENGINE=InnoDB AUTO_INCREMENT=328 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_consorcio`
--

DROP TABLE IF EXISTS `tr_consorcio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_consorcio` (
  `int_idConsorcio` int NOT NULL AUTO_INCREMENT,
  `int_idInterlocutor` int DEFAULT NULL,
  `str_NombreConsorcio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Obligacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_ValorAporte` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PorcentajeAporte` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ValorServicios` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PorcentajeServicios` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ValorHonorarios` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PorcentajeHonorarios` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  PRIMARY KEY (`int_idConsorcio`),
  KEY `Consorcio_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `Consorcio_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `Consorcio_Interlocutor_FK` (`int_idInterlocutor`)
) ENGINE=InnoDB AUTO_INCREMENT=419 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_consorcioporsolicitud`
--

DROP TABLE IF EXISTS `tr_consorcioporsolicitud`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_consorcioporsolicitud` (
  `int_idConsorcioPorSolicitud` int NOT NULL AUTO_INCREMENT,
  `int_idConsorcio` int NOT NULL,
  `int_idSolicitudCont` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idConsorcioPorSolicitud`),
  KEY `Consorio_SolicitudCont_FK` (`int_idConsorcio`),
  KEY `SolicitudCont_Consorio_FK` (`int_idSolicitudCont`),
  KEY `ConsorcioSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `ConsorcioSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`)
) ENGINE=InnoDB AUTO_INCREMENT=411 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_correlativosolicitud`
--

DROP TABLE IF EXISTS `tr_correlativosolicitud`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_correlativosolicitud` (
  `int_idCorrelativo` int NOT NULL AUTO_INCREMENT,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `str_CorrelativoTipoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idCorrelativo`),
  KEY `correlativo_suscriptor_FK` (`str_idSuscriptor`),
  KEY `correlativo_tipoSolicitud_FK` (`int_idTipoSolicitud`),
  KEY `correlativo_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `correlativo_UsuarioModificador_FK` (`int_idUsuarioModificacion`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_estadoregistros`
--

DROP TABLE IF EXISTS `tr_estadoregistros`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_estadoregistros` (
  `int_idEstadoRegistros` int NOT NULL AUTO_INCREMENT,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idEstado` int NOT NULL,
  `dt_FechaCambio` datetime NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idEstadoRegistros`),
  KEY `EstadoRegistros_suscriptor_FK` (`str_idSuscriptor`),
  KEY `EstadoRegistros_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `EstadoRegistros_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `EstadoRegistros_Estado_FK` (`int_idEstado`),
  KEY `EstadoRegistros_Solicitudes_FK` (`int_idSolicitudes`)
) ENGINE=InnoDB AUTO_INCREMENT=859 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_gestortiposolicitud`
--

DROP TABLE IF EXISTS `tr_gestortiposolicitud`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_gestortiposolicitud` (
  `int_idGestorxTipo` int NOT NULL AUTO_INCREMENT,
  `int_idGestor` int NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `str_idSuscripcion` varchar(10) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreador` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idEmpresa` int DEFAULT NULL,
  PRIMARY KEY (`int_idGestorxTipo`),
  KEY `GestorxTipo_Gestor` (`int_idGestor`),
  KEY `GestorxTipo_Tipo` (`int_idTipoSolicitud`),
  CONSTRAINT `GestorxTipo_Gestor` FOREIGN KEY (`int_idGestor`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `GestorxTipo_Tipo` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_plantillas`
--

DROP TABLE IF EXISTS `tr_plantillas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_plantillas` (
  `int_idPlantilla` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idTipoSolicitud` int DEFAULT NULL,
  `str_RutaArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_NombreArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ExtencionArchivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TamañoArchivo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idEmpresa` int NOT NULL,
  `str_TipoPlantilla` varchar(50) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  PRIMARY KEY (`int_idPlantilla`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_solicitudcont`
--

DROP TABLE IF EXISTS `tr_solicitudcont`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_solicitudcont` (
  `int_idSolicitudCont` int NOT NULL AUTO_INCREMENT,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idInterlocutor` int DEFAULT NULL,
  `int_idInterlocutorComprador` int DEFAULT NULL,
  `int_NumAsociados` int DEFAULT NULL,
  `str_Moneda` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `db_Presupuesto` double DEFAULT NULL,
  `str_Margen` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PlazoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TipoServicio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_InfoAdicional` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DocAdjuntos` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_CondicionPago` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ConsultorAsignado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RenovacionAuto` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleRenovAuto` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_AjusteHonorarios` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleAjusteHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Garantia` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleGarantia` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ResolucionAnticipada` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleResolucionAnticipada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Penalidades` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetallePenalidades` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienMuebleInmueble` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienPartidaCertificada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDireccion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienUso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDescripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObjetivoContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_MonedaContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RentaPactada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ImporteVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_FormaPago` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PlazoArriendo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaArriendo` datetime DEFAULT NULL,
  `dt_FechaVenta` datetime DEFAULT NULL,
  `str_InteresRetraso` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_detalleInteresRetraso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObligacionesConjuntas` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_GarantiaVenta` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleGarantiaVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_InfoCompartida` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ReajusteRenta` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleReajusteRenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaRenAut` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`int_idSolicitudCont`),
  KEY `SolicitudCont_Solicitud_FK` (`int_idSolicitudes`),
  KEY `SolicitudCont_Suscriptor_FK` (`str_idSuscriptor`),
  KEY `SolicitudCont_Interlocutor_FK` (`int_idInterlocutor`),
  KEY ` SolicitudCont_InterlocutorComprador_FK` (`int_idInterlocutorComprador`)
) ENGINE=InnoDB AUTO_INCREMENT=278 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_solicitudes`
--

DROP TABLE IF EXISTS `tr_solicitudes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_solicitudes` (
  `int_idSolicitudes` int NOT NULL AUTO_INCREMENT,
  `str_CodSolicitudes` varchar(20) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idSolicitante` int DEFAULT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idEmpresa` int DEFAULT NULL,
  `int_idEstado` int NOT NULL,
  `int_idTipoSol` int NOT NULL,
  `int_idUnidadNegocio` int DEFAULT NULL,
  `int_SolicitudGuardada` int DEFAULT '0',
  `str_DeTerceros` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `db_Honorarios` double DEFAULT NULL,
  `dt_FirmaContrato` datetime DEFAULT NULL,
  `int_HorasTrabajadas` int DEFAULT NULL,
  `str_Visible` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaRegistro` datetime NOT NULL,
  `dt_FechaEsperada` datetime DEFAULT NULL,
  `dt_FechaFin` datetime DEFAULT NULL,
  `int_idGestor` int DEFAULT NULL,
  `int_idClienteAsociado` int DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_Firmante1` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Firmante2` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_documentoFirmante1` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_documentoFirmante2` varchar(255) COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleAcuerdo` text COLLATE utf8mb4_spanish2_ci,
  `dt_FechaFirmaEJ` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`int_idSolicitudes`),
  KEY `solicitud_usuario_FK` (`int_idSolicitante`),
  KEY `solicitud_suscriptor_FK` (`str_idSuscriptor`),
  KEY `solicitud_empresa_FK` (`int_idEmpresa`),
  KEY `solicitud_usuarioModificador_FK` (`int_idUsuarioModificacion`),
  KEY `solicitud_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `solicitud_Gestor_FK` (`int_idGestor`),
  KEY `solicitud_tipoSolicitud_FK` (`int_idTipoSol`),
  KEY `solicitud_clienteAsociado_FK` (`int_idClienteAsociado`),
  KEY `solicitud_estados_FK` (`int_idEstado`),
  KEY `Solicitud_Unidad_Negocio_FK` (`int_idUnidadNegocio`)
) ENGINE=InnoDB AUTO_INCREMENT=521 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_tags`
--

DROP TABLE IF EXISTS `tr_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_tags` (
  `int_idTags` int NOT NULL AUTO_INCREMENT,
  `int_idSolicitudes` int NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`int_idTags`),
  KEY `tags_solicitud_FK` (`int_idSolicitudes`),
  KEY `tags_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  KEY `tags_UsuarioModificador_FK` (`int_idUsuarioModificacion`)
) ENGINE=InnoDB AUTO_INCREMENT=258 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_tiemresptipsoli`
--

DROP TABLE IF EXISTS `tr_tiemresptipsoli`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_tiemresptipsoli` (
  `int_idTiempoRespuesta` int NOT NULL AUTO_INCREMENT,
  `int_idTipoSolicitud` int NOT NULL,
  `int_TiempoRespuesta` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_fechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idEmpresa` int DEFAULT NULL,
  PRIMARY KEY (`int_idTiempoRespuesta`),
  KEY `TiempoRespuesta_TipoSolicitud` (`int_idTipoSolicitud`),
  KEY `TiempoRespuesta_Suscripcion` (`str_idSuscripcion`),
  CONSTRAINT `TiempoRespuesta_Suscripcion` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `TiempoRespuesta_TipoSolicitud` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-02-09 17:27:26
