import os
from pathlib import Path
from configparser import ConfigParser

config_obj = ConfigParser()
config_path = "config.ini"
config_obj.read(config_path)
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-087quz2=2e4$92s2p=3qwl3l*!)c951@q)!&liq8apz)s2*@+i'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['qaapis.greta.pe' , 'greta.pe']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'clausulasLegales',
    'archivos',
    'estados',
    'tiposSolicitud',
    'tiposDocumentos',
    'solicitudes',
    'empresas',
    'interlocutores',
    'clausulasSolicitud',
    'tags',
    'consorcio',
    'corsheaders',
    'unidadesNegocio',
    'graficos',
    'seguridad',
    'tipoCambio',
    'gestorTipoSolicitud',
    'tiempoEstimado',
    'usuarios',
    'credentialBot'
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    #'utils.middleware.JWTMiddleware',

]
 

ROOT_URLCONF = 'backendPrismaProcesos.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'backendPrismaProcesos.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
     "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": config_obj.get("DB", "DB_NAME"),
        "USER": config_obj.get("DB", "USER"),
        "PASSWORD": config_obj.get("DB", "PASSWORD"),
        "HOST": config_obj.get("DB", "HOST"),
        "PORT": config_obj.get("DB", "PORT"),
    }
}

CORS_ALLOWED_ORIGINS = [
    "https://greta.pe",
    "https://qaprisma.greta.pe",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:5175",
    "https://petrotal.greta.pe"
]


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'es-pe'

TIME_ZONE = 'America/Lima'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
PLANTILLAS_ROOT = os.path.join(BASE_DIR, 'Plantillas')
FIRMAS_ROOT = os.path.join(BASE_DIR, 'firmas')
MANUALES_ROOT = os.path.join(BASE_DIR, 'manuales')
HISTORICOS_ROOT = os.path.join(BASE_DIR, 'historicos')
MEDIA_URL = '/media/'
ARCHIVOS_LECTOR =  config_obj.get("MEDIA", "ARCHIVOS_LECTOR")
DATA_UPLOAD_MAX_MEMORY_SIZE = 31457280