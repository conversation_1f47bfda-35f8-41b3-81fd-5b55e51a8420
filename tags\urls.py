from django.urls import path
from .views import TagsCreate,TagsDetalle,BuscarTagsSolicitud,BuscarTagsNombre,BuscarTagsSolicitudNombre,EliminarTags
urlpatterns = [
    path('api/tags/', TagsCreate.as_view(), name='tags-crear'),
    path('api/tags/<str:pk>/', TagsDetalle.as_view(), name='tags-detail'),
    path('api/tags/solicitud/<int:int_idSolicitudes>/', BuscarTagsSolicitud.as_view(), name='tags-detail-solicitud'),
    path('api/tags/nombre/<str:nombre>/', BuscarTagsNombre.as_view(), name='tags-detail-nombre'),
    path('api/tags/solicitudNombre/<int:int_idSolicitudes>/<str:str_descripcion>/', BuscarTagsSolicitudNombre.as_view(), name='tags-detail-nombre'),
    path('api/tags/eliminar/<int:solicitud>/', EliminarTags.as_view(), name='tags-detail-nombre'),

]