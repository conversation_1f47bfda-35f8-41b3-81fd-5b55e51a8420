-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 11-10-2024 a las 04:05:49
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `prisma_desarrollo`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tc_empresas`
--

CREATE TABLE `tc_empresas` (
  `int_idEmpresa` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tc_empresas`
--

INSERT INTO `tc_empresas` (`int_idEmpresa`, `str_idSuscripcion`, `str_NombreEmpresa`, `str_RazonSocial`, `str_Ruc`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(4, 'GTLBS-0001', 'Greta Labs', '----', '201234567890', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_clausulaslegales`
--

CREATE TABLE `tm_clausulaslegales` (
  `int_idClausulasLegales` int NOT NULL,
  `str_CodClausulasLegales` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_clausulaslegales`
--

INSERT INTO `tm_clausulaslegales` (`int_idClausulasLegales`, `str_CodClausulasLegales`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, 'C001', 'GTLBS-0001', 'Cláusula de indemnidad', '2024-09-12 17:26:26', NULL, 1, NULL),
(3, 'C002', 'GTLBS-0001', 'Cláusula de exclusividad', '2024-09-29 02:33:42', NULL, 1, NULL),
(4, 'C003', 'GTLBS-0001', 'Cláusula de indemnidad', '2024-09-29 02:33:42', NULL, 1, NULL),
(5, 'C004', 'GTLBS-0001', 'Cláusula de penalidades', '2024-09-29 02:34:34', NULL, 1, NULL),
(6, 'C005', 'GTLBS-0001', 'Cláusula de obligaciones  SST', '2024-09-29 02:33:42', NULL, 1, NULL),
(7, 'C006', 'GTLBS-0001', 'Cláusula de confidencialidad', '2024-09-29 02:34:34', NULL, 1, NULL),
(8, 'C007', 'GTLBS-0001', 'Cláusula anticorrupción', '2024-09-29 02:50:22', NULL, 1, NULL),
(10, 'C008', 'GTLBS-0001', 'Cláusula de protección de datos personales', '2024-10-03 08:27:02', NULL, 1, NULL),
(11, 'C009', 'GTLBS-0001', 'Cláusula de contratación de pólizas de seguros', '2024-10-03 08:27:17', NULL, 1, NULL),
(12, 'C010', 'GTLBS-0001', 'Cláusula de cumplimiento de obligaciones laborales', '2024-10-03 08:27:24', NULL, 1, NULL),
(13, 'C011', 'GTLBS-0001', 'Cláusula de resolución de controversias', '2024-10-03 08:27:30', NULL, 1, NULL),
(14, 'C012', 'GTLBS-0001', 'Cláusula de seguro por daños', '2024-10-03 08:27:37', NULL, 1, NULL),
(20, 'C013', 'GTLBS-0001', 'hghh', '2024-10-10 08:33:58', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_especialidades`
--

CREATE TABLE `tm_especialidades` (
  `int_idEspecialidades` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodigoEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_estados`
--

CREATE TABLE `tm_estados` (
  `int_idEstado` int NOT NULL,
  `str_codEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_estados`
--

INSERT INTO `tm_estados` (`int_idEstado`, `str_codEstado`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'E001', 'GTLBS-0001', 'Nuevo', '2024-09-06 17:15:43', '2024-09-06 17:18:51', 1, 1),
(2, 'E002', 'GTLBS-0001', 'Asignado', '2024-09-18 12:14:10', NULL, 1, NULL),
(3, 'E003', 'GTLBS-0001', 'En Proceso', '2024-09-13 16:06:24', NULL, 1, NULL),
(4, 'E004', 'GTLBS-0001', 'En Validacion', '2024-09-18 12:14:10', NULL, 1, NULL),
(5, 'E005', 'GTLBS-0001', 'Aceptado', '2024-09-18 12:15:08', NULL, 1, NULL),
(6, 'E006', 'GTLBS-0001', 'En Aprobacion', '2024-09-18 12:18:15', NULL, 1, NULL),
(7, 'E007', 'GTLBS-0001', 'Aprobado', '2024-09-18 12:18:15', NULL, 1, NULL),
(8, 'E008', 'GTLBS-0001', 'Firmado', '2024-09-18 12:19:04', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_interlocutores`
--

CREATE TABLE `tm_interlocutores` (
  `int_idInterlocutor` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Interlocutor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TipoDoc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Documento` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Domicilio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Correo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_obligaciones` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_ValorAporte` int DEFAULT NULL,
  `int_PorcentajeAporte` float DEFAULT NULL,
  `str_ValorServicios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ValorHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RepLegal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_RLPartida` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RLTipoDocumento` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RLDocumento` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_interlocutores`
--

INSERT INTO `tm_interlocutores` (`int_idInterlocutor`, `str_idSuscripcion`, `str_Interlocutor`, `str_RazonSocial`, `str_TipoDoc`, `str_Documento`, `str_Domicilio`, `str_Correo`, `str_obligaciones`, `int_ValorAporte`, `int_PorcentajeAporte`, `str_ValorServicios`, `str_ValorHonorarios`, `str_RepLegal`, `int_RLPartida`, `str_RLTipoDocumento`, `str_RLDocumento`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(92, 'GTLBS-0001', 'Pedro Perez', '', 'RUC', '10726405688', '', '<EMAIL>', '', NULL, NULL, '', '', 'David Martinez', NULL, '', '', '2024-10-10 23:01:13', '2024-10-10 23:04:25', 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_suscripcion`
--

CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_suscripcion`
--

INSERT INTO `tm_suscripcion` (`str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
('GTLBS-0001', 'Suscripcion Greta', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipodocumento`
--

CREATE TABLE `tm_tipodocumento` (
  `int_idTipoDocumentos` int NOT NULL,
  `str_CodTipoDocumento` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tipodocumento`
--

INSERT INTO `tm_tipodocumento` (`int_idTipoDocumentos`, `str_CodTipoDocumento`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'DOAD', 'GTLBS-0001', 'Documentos adjuntos', '2024-09-20 04:27:03', NULL, 1, NULL),
(2, 'DOPC', 'GTLBS-0001', 'Plantilla modelo de contrato', '2024-09-11 09:11:11', NULL, 1, NULL),
(3, 'COAP', 'GTLBS-0001', 'Contratos Aprobados', '2024-09-26 22:41:34', NULL, 1, NULL),
(4, 'COFI', 'GTLBS-0001', 'Contratos Firmados', '2024-09-27 19:53:53', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tiposolicitud`
--

CREATE TABLE `tm_tiposolicitud` (
  `int_idTipoSolicitud` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodTipoSol` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tiposolicitud`
--

INSERT INTO `tm_tiposolicitud` (`int_idTipoSolicitud`, `str_idSuscripcion`, `str_CodTipoSol`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'GTLBS-0001', 'CPRS', 'Contrato de prestación de servicios', '2024-09-11 17:30:34', NULL, 1, NULL),
(2, 'GTLBS-0001', 'CLOS', 'Contrato de locación de servicios', '2024-09-17 15:07:42', NULL, 1, NULL),
(3, 'GTLBS-0001', 'CPSP', 'Contrato de prestación de servicios profesionales', '2024-09-17 15:07:42', NULL, 1, NULL),
(4, 'GTLBS-0001', 'COAB', 'Contrato de arrendamiento de bienes muebles o inmuebles', '2024-09-23 10:10:34', NULL, 1, NULL),
(5, 'GTLBS-0001', 'CCVB', 'Contrato de compra / venta de bienes muebles o inmuebles', '2024-09-23 15:12:19', NULL, 1, NULL),
(6, 'GTLBS-0001', 'CCON', 'Contrato de consorcio', '2024-09-23 15:12:19', NULL, 1, NULL),
(7, 'GTLBS-0001', 'CACO', 'Acuerdo de confidencialidad', '2024-09-23 15:14:31', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_unidadesnegocios`
--

CREATE TABLE `tm_unidadesnegocios` (
  `int_idUnidadesNegocio` int NOT NULL,
  `str_Descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_fechaCreacion` datetime NOT NULL,
  `dt_fechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreador` int NOT NULL,
  `int_idUsuarioModificador` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_unidadesnegocios`
--

INSERT INTO `tm_unidadesnegocios` (`int_idUnidadesNegocio`, `str_Descripcion`, `str_idSuscripcion`, `dt_fechaCreacion`, `dt_fechaModificacion`, `int_idUsuarioCreador`, `int_idUsuarioModificador`) VALUES
(6, 'Tecnología', 'GTLBS-0001', '2024-10-11 03:52:33', NULL, 1, NULL),
(7, 'Administración', 'GTLBS-0001', '2024-10-11 03:53:15', NULL, 1, NULL),
(8, 'Comercio Exterior', 'GTLBS-0001', '2024-10-11 03:53:27', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_usuarios`
--

CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Documento` int NOT NULL,
  `str_UnidadNegocio` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idEspecialidad` int DEFAULT NULL,
  `int_Estado` int DEFAULT '1',
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_usuarios`
--

INSERT INTO `tm_usuarios` (`int_idUsuarios`, `str_Nombres`, `str_Apellidos`, `str_Correo`, `int_Documento`, `str_UnidadNegocio`, `str_Clave`, `int_idEspecialidad`, `int_Estado`, `str_Codigo`, `dt_FechaCreacion`, `dt_FechaModificacion`, `str_idUsuarioCreacion`, `str_idUsuarioModificacion`) VALUES
(1, 'Administrador ', 'Greta Labs', '<EMAIL>', 12345678, 'Unidad De negocio', NULL, 4, 1, NULL, NULL, NULL, NULL, NULL),
(35, 'José Carlos', 'Rodríguez Gonzales', '<EMAIL>', 98745632, 'Unidad de Negocio', NULL, 6, 1, NULL, NULL, NULL, NULL, NULL),
(36, 'Marcos Alfredo', 'Flores Mora', '<EMAIL>', 14789632, 'Unidad de Negocio', NULL, 6, 1, NULL, NULL, NULL, NULL, NULL),
(37, 'Julio Piero', 'Guerrero Farfan', '<EMAIL>', 36987412, 'Unidad de Negocio', NULL, 5, 1, NULL, NULL, NULL, NULL, NULL),
(38, 'Pedro Antonio', 'Gallese Lima', '<EMAIL>', 25874136, 'Unidad de Negocio', NULL, 4, 1, NULL, NULL, NULL, NULL, NULL),
(39, 'Miguel Renzo', 'García Jimenez', '<EMAIL>', 14563298, 'Unidad de Negocio', NULL, 5, 1, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_aprobadores`
--

CREATE TABLE `tr_aprobadores` (
  `int_idAprobador` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuario` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaAceptacion` datetime DEFAULT NULL,
  `int_OrdenAprobacion` tinyint(1) NOT NULL,
  `int_EstadoAprobacion` tinyint(1) NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_archivosdocumentos`
--

CREATE TABLE `tr_archivosdocumentos` (
  `int_idArchivos` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idTipoDocumento` int NOT NULL,
  `str_RutaArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ExtencionArchivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_TamañoArchivo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_archivosdocumentos`
--

INSERT INTO `tr_archivosdocumentos` (`int_idArchivos`, `str_idSuscriptor`, `int_idSolicitudes`, `int_idTipoDocumento`, `str_RutaArchivo`, `str_NombreArchivo`, `str_ExtencionArchivo`, `str_TamañoArchivo`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(147, 'GTLBS-0001', 228, 1, 'C:\\TRABAJO\\GRETA\\PRISMA\\CONTRATOS\\backend-prisma-contratos\\backendPrismaProcesos\\media\\GTLBS-0001\\CPRS-202410001\\DOAD\\GRETA-Arquitectura de desarrollo.pptx.pptx', 'GRETA-Arquitectura de desarrollo.pptx', 'pptx', '3912176', '2024-10-10 23:04:25', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_clausulasactivas`
--

CREATE TABLE `tr_clausulasactivas` (
  `int_idClausulasActivas` int NOT NULL,
  `str_idSuscripcion` int NOT NULL,
  `int_idClausulaIncluida` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_clausulasincluidas`
--

CREATE TABLE `tr_clausulasincluidas` (
  `int_idClausulasIncluidas` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `int_idClausulaLegal` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_consorcio`
--

CREATE TABLE `tr_consorcio` (
  `int_idConsorcio` int NOT NULL,
  `int_idInterlocutor` int DEFAULT NULL,
  `str_NombreConsorcio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Oblicacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_consorcioporsolicitud`
--

CREATE TABLE `tr_consorcioporsolicitud` (
  `int_idConsorcioPorSolicitud` int NOT NULL,
  `int_idConsorcio` int NOT NULL,
  `int_idSolicitudCont` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_correlativosolicitud`
--

CREATE TABLE `tr_correlativosolicitud` (
  `int_idCorrelativo` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `str_CorrelativoTipoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_correlativosolicitud`
--

INSERT INTO `tr_correlativosolicitud` (`int_idCorrelativo`, `str_idSuscriptor`, `int_idTipoSolicitud`, `str_CorrelativoTipoSolicitud`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(14, 'GTLBS-0001', 1, '001', '2024-10-10 23:01:13', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_estadoregistros`
--

CREATE TABLE `tr_estadoregistros` (
  `int_idEstadoRegistros` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idEstado` int NOT NULL,
  `dt_FechaCambio` datetime NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_estadoregistros`
--

INSERT INTO `tr_estadoregistros` (`int_idEstadoRegistros`, `str_idSuscriptor`, `int_idSolicitudes`, `int_idEstado`, `dt_FechaCambio`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(163, 'GTLBS-0001', 228, 1, '2024-10-10 23:01:14', '2024-10-10 23:01:14', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_solicitudcont`
--

CREATE TABLE `tr_solicitudcont` (
  `int_idSolicitudCont` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idInterlocutor` int DEFAULT NULL,
  `int_idInterlocutorComprador` int DEFAULT NULL,
  `int_NumAsociados` int DEFAULT NULL,
  `str_Moneda` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `db_Presupuesto` double DEFAULT NULL,
  `str_Margen` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PlazoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TipoServicio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_InfoAdicional` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DocAdjuntos` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_CondicionPago` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ConsultorAsignado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RenovacionAuto` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleRenovAuto` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_AjusteHonorarios` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleAjusteHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Garantia` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleGarantia` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ResolucionAnticipada` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleResolucionAnticipada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Penalidades` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetallePenalidades` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienMuebleInmueble` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienPartidaCertificada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDireccion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienUso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDescripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObjetivoContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_MonedaContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RentaPactada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ImporteVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_FormaPago` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PlazoArriendo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaArriendo` datetime DEFAULT NULL,
  `dt_FechaVenta` datetime DEFAULT NULL,
  `str_InteresRetraso` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_detalleInteresRetraso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObligacionesConjuntas` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_GarantiaVenta` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleGarantiaVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_InfoCompartida` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ReajusteRenta` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleReajusteRenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_solicitudcont`
--

INSERT INTO `tr_solicitudcont` (`int_idSolicitudCont`, `str_idSuscriptor`, `int_idSolicitudes`, `int_idInterlocutor`, `int_idInterlocutorComprador`, `int_NumAsociados`, `str_Moneda`, `db_Presupuesto`, `str_Margen`, `str_PlazoSolicitud`, `str_TipoServicio`, `str_InfoAdicional`, `str_DocAdjuntos`, `str_CondicionPago`, `str_ConsultorAsignado`, `str_RenovacionAuto`, `str_DetalleRenovAuto`, `str_AjusteHonorarios`, `str_DetalleAjusteHonorarios`, `str_Garantia`, `str_DetalleGarantia`, `str_ResolucionAnticipada`, `str_DetalleResolucionAnticipada`, `str_Penalidades`, `str_DetallePenalidades`, `str_BienMuebleInmueble`, `str_BienPartidaCertificada`, `str_BienDireccion`, `str_BienUso`, `str_BienDescripcion`, `str_ObjetivoContrato`, `str_MonedaContrato`, `str_RentaPactada`, `str_ImporteVenta`, `str_FormaPago`, `str_PlazoArriendo`, `dt_FechaArriendo`, `dt_FechaVenta`, `str_InteresRetraso`, `str_detalleInteresRetraso`, `str_ObligacionesConjuntas`, `str_GarantiaVenta`, `str_DetalleGarantiaVenta`, `str_InfoCompartida`, `str_ReajusteRenta`, `str_DetalleReajusteRenta`) VALUES
(79, 'GTLBS-0001', 228, 92, NULL, NULL, 'soles', 5000, '1 dia', '2 Dias', 'Tecnología', '----', 'si', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '-', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_solicitudes`
--

CREATE TABLE `tr_solicitudes` (
  `int_idSolicitudes` int NOT NULL,
  `str_CodSolicitudes` varchar(17) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitante` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idEmpresa` int NOT NULL,
  `int_idEstado` int NOT NULL,
  `int_idTipoSol` int NOT NULL,
  `int_idUnidadNegocio` int DEFAULT NULL,
  `int_SolicitudGuardada` int DEFAULT '0',
  `str_DeTerceros` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `db_Honorarios` double DEFAULT NULL,
  `dt_FirmaContrato` datetime DEFAULT NULL,
  `int_HorasTrabajadas` int DEFAULT NULL,
  `str_Visible` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaRegistro` datetime NOT NULL,
  `dt_FechaEsperada` datetime NOT NULL,
  `dt_FechaFin` datetime DEFAULT NULL,
  `int_idGestor` int DEFAULT NULL,
  `int_idClienteAsociado` int DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_solicitudes`
--

INSERT INTO `tr_solicitudes` (`int_idSolicitudes`, `str_CodSolicitudes`, `int_idSolicitante`, `str_idSuscriptor`, `int_idEmpresa`, `int_idEstado`, `int_idTipoSol`, `int_idUnidadNegocio`, `int_SolicitudGuardada`, `str_DeTerceros`, `db_Honorarios`, `dt_FirmaContrato`, `int_HorasTrabajadas`, `str_Visible`, `dt_FechaRegistro`, `dt_FechaEsperada`, `dt_FechaFin`, `int_idGestor`, `int_idClienteAsociado`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(228, 'CPRS-202410001', 1, 'GTLBS-0001', 4, 1, 1, 7, 0, 'no', 5000, NULL, NULL, 'si', '2024-10-10 23:01:13', '2024-10-19 04:00:06', NULL, NULL, NULL, '2024-10-10 23:01:13', '2024-10-10 23:04:25', 1, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_tags`
--

CREATE TABLE `tr_tags` (
  `int_idTags` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD PRIMARY KEY (`int_idEmpresa`),
  ADD KEY `empresas_suscriptor_FK` (`str_idSuscripcion`),
  ADD KEY `empresas_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `empresas_usuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  ADD PRIMARY KEY (`int_idClausulasLegales`),
  ADD KEY `clausulasLegales_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `clausulasLegales_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `clausulas_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD PRIMARY KEY (`int_idEspecialidades`),
  ADD KEY `Especialidad_Suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD PRIMARY KEY (`int_idEstado`),
  ADD KEY `Estados_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Estados_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Estados_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD PRIMARY KEY (`int_idInterlocutor`),
  ADD KEY `Interlocutor_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Interlocutor_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Interlocutor_Suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD PRIMARY KEY (`str_idSuscripcion`),
  ADD KEY `Suscripciones_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Suscripciones_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD PRIMARY KEY (`int_idTipoDocumentos`),
  ADD KEY `TipoDocumentos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoDocumentos_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  ADD PRIMARY KEY (`int_idTipoSolicitud`),
  ADD UNIQUE KEY `codigo_tipoSolicitud` (`int_idTipoSolicitud`),
  ADD KEY `TipoSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `TipoSolicitud_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_unidadesnegocios`
--
ALTER TABLE `tm_unidadesnegocios`
  ADD PRIMARY KEY (`int_idUnidadesNegocio`),
  ADD KEY `unidadNegocio_Suscripcion_FK` (`str_idSuscripcion`),
  ADD KEY `unidadNegocio_UsuarioCreador_FK` (`int_idUsuarioCreador`),
  ADD KEY `unidadNegocio_UsuarioModificador_FK` (`int_idUsuarioModificador`);

--
-- Indices de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  ADD PRIMARY KEY (`int_idUsuarios`) USING BTREE;

--
-- Indices de la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  ADD PRIMARY KEY (`int_idAprobador`),
  ADD KEY `aprobador_suscriptor_FK` (`str_idSuscripcion`),
  ADD KEY `aprobador_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `aprobador_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `aprobador_solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `aprobador_Usuario_FK` (`int_idUsuario`);

--
-- Indices de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD PRIMARY KEY (`int_idArchivos`),
  ADD KEY `archivos_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `archivos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `archivos_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `archivos_tipoDocumentos_FK` (`int_idTipoDocumento`),
  ADD KEY `archivos_solicitud_FK` (`int_idSolicitudes`);

--
-- Indices de la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  ADD PRIMARY KEY (`int_idClausulasActivas`),
  ADD KEY `ClausulasActivas_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ClausulasActivas_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `ClausulasActivas_Solicitudes_FK` (`int_idSolicitudes`),
  ADD KEY `ClausulasActivas_suscripcion_FK` (`str_idSuscripcion`),
  ADD KEY `ClausulasActivas_Incluida_FK` (`int_idClausulaIncluida`);

--
-- Indices de la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  ADD PRIMARY KEY (`int_idClausulasIncluidas`),
  ADD KEY `ClausulasIncluida_tipoSol_FK` (`int_idTipoSolicitud`),
  ADD KEY `ClausulasIncluida_Clausula_FK` (`int_idClausulaLegal`),
  ADD KEY `ClausulasIncluida_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ClausulasIncluida_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `ClausulasIncluidas_solicitud_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  ADD PRIMARY KEY (`int_idConsorcio`),
  ADD KEY `Consorcio_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Consorcio_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Consorcio_Interlocutor_FK` (`int_idInterlocutor`);

--
-- Indices de la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  ADD PRIMARY KEY (`int_idConsorcioPorSolicitud`),
  ADD KEY `Consorio_SolicitudCont_FK` (`int_idConsorcio`),
  ADD KEY `SolicitudCont_Consorio_FK` (`int_idSolicitudCont`),
  ADD KEY `ConsorcioSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ConsorcioSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  ADD PRIMARY KEY (`int_idCorrelativo`),
  ADD KEY `correlativo_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `correlativo_tipoSolicitud_FK` (`int_idTipoSolicitud`),
  ADD KEY `correlativo_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `correlativo_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  ADD PRIMARY KEY (`int_idEstadoRegistros`),
  ADD KEY `EstadoRegistros_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `EstadoRegistros_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `EstadoRegistros_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `EstadoRegistros_Estado_FK` (`int_idEstado`),
  ADD KEY `EstadoRegistros_Solicitudes_FK` (`int_idSolicitudes`);

--
-- Indices de la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  ADD PRIMARY KEY (`int_idSolicitudCont`),
  ADD KEY `SolicitudCont_Solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `SolicitudCont_Suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `SolicitudCont_Interlocutor_FK` (`int_idInterlocutor`),
  ADD KEY ` SolicitudCont_InterlocutorComprador_FK` (`int_idInterlocutorComprador`);

--
-- Indices de la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  ADD PRIMARY KEY (`int_idSolicitudes`),
  ADD KEY `solicitud_usuario_FK` (`int_idSolicitante`),
  ADD KEY `solicitud_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `solicitud_empresa_FK` (`int_idEmpresa`),
  ADD KEY `solicitud_usuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `solicitud_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `solicitud_Gestor_FK` (`int_idGestor`),
  ADD KEY `solicitud_tipoSolicitud_FK` (`int_idTipoSol`),
  ADD KEY `solicitud_clienteAsociado_FK` (`int_idClienteAsociado`),
  ADD KEY `solicitud_estados_FK` (`int_idEstado`),
  ADD KEY `Solicitud_Unidad_Negocio_FK` (`int_idUnidadNegocio`);

--
-- Indices de la tabla `tr_tags`
--
ALTER TABLE `tr_tags`
  ADD PRIMARY KEY (`int_idTags`),
  ADD KEY `tags_solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `tags_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `tags_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  MODIFY `int_idEmpresa` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  MODIFY `int_idClausulasLegales` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  MODIFY `int_idEspecialidades` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  MODIFY `int_idEstado` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  MODIFY `int_idInterlocutor` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=93;

--
-- AUTO_INCREMENT de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  MODIFY `int_idTipoDocumentos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  MODIFY `int_idTipoSolicitud` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT de la tabla `tm_unidadesnegocios`
--
ALTER TABLE `tm_unidadesnegocios`
  MODIFY `int_idUnidadesNegocio` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  MODIFY `int_idUsuarios` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT de la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  MODIFY `int_idAprobador` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- AUTO_INCREMENT de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  MODIFY `int_idArchivos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=148;

--
-- AUTO_INCREMENT de la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  MODIFY `int_idClausulasActivas` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=73;

--
-- AUTO_INCREMENT de la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  MODIFY `int_idClausulasIncluidas` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=69;

--
-- AUTO_INCREMENT de la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  MODIFY `int_idConsorcio` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT de la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  MODIFY `int_idConsorcioPorSolicitud` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  MODIFY `int_idCorrelativo` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT de la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  MODIFY `int_idEstadoRegistros` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=164;

--
-- AUTO_INCREMENT de la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  MODIFY `int_idSolicitudCont` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;

--
-- AUTO_INCREMENT de la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  MODIFY `int_idSolicitudes` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=229;

--
-- AUTO_INCREMENT de la tabla `tr_tags`
--
ALTER TABLE `tr_tags`
  MODIFY `int_idTags` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD CONSTRAINT `empresas_suscriptor_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  ADD CONSTRAINT `clausulas_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `clausulasLegales_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `clausulasLegales_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD CONSTRAINT `Especialidad_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD CONSTRAINT `Estados_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD CONSTRAINT `Interlocutor_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Interlocutor_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Interlocutor_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD CONSTRAINT `Suscripciones_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Suscripciones_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD CONSTRAINT `TipoDocumentos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoDocumentos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  ADD CONSTRAINT `TipoSolicitud_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoSolicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoSolicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_unidadesnegocios`
--
ALTER TABLE `tm_unidadesnegocios`
  ADD CONSTRAINT `unidadNegocio_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `unidadNegocio_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `unidadNegocio_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  ADD CONSTRAINT `Aprobador_Solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Aprobador_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Aprobador_Usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD CONSTRAINT `archivos_solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `archivos_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_tipoDocumentos_FK` FOREIGN KEY (`int_idTipoDocumento`) REFERENCES `tm_tipodocumento` (`int_idTipoDocumentos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
