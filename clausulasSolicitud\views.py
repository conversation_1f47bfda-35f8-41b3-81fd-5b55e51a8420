from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import ClausulasIncluidasSerializer,ClausulasIncluidasUpdateSerializer,ClausulasActivasSerializer,ClausulasActivasUpdateSerializer,ClausulasActivasNombreSerializer

class ClausulasIncluidasList(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        int_idTipoSolicitud = request.query_params.get('int_idTipoSolicitud')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'int_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = """
            SELECT 
                c.int_idClausulasIncluidas, 
                c.str_idSuscripcion , 
                c.int_idTipoSolicitud, 
                ts.str_CodTipoSol AS tipo_solicitud_codigo,
                ts.str_Nombre AS tipo_solicitud_nombre,
                c.int_idClausulaLegal, 
                cl.str_CodClausulasLegales AS clausula_legal_codigo,
                cl.str_Nombre AS clausula_legal_nombre,
                c.dt_FechaCreacion, 
                c.dt_FechaModificacion, 
                c.int_idUsuarioCreacion, 
                c.int_idUsuarioModificacion
            FROM 
                tr_clausulasincluidas c
            INNER JOIN 
                tm_tiposolicitud ts ON c.int_idTipoSolicitud = ts.int_idTipoSolicitud
            INNER JOIN 
                tm_clausulaslegales cl ON c.int_idClausulaLegal = cl.int_idClausulasLegales
            WHERE 
                c.str_idSuscripcion = %s && c.int_idTipoSolicitud=  %s && cl.int_idEmpresa = %s 
        """
        params = [str_idSuscripcion,int_idTipoSolicitud,int_idEmpresa]
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'int_idClausulasIncluidas': row[0],
                    'int_idSuscripcion': row[1],
                    'int_idTipoSolicitud': row[2],
                    'tipo_solicitud_codigo': row[3],
                    'tipo_solicitud_nombre': row[4],
                    'int_idClausulaLegal': row[5],
                    'clausula_legal_codigo': row[6],
                    'clausula_legal_nombre': row[7],
                    'dt_FechaCreacion': row[8],
                    'dt_FechaModificacion': row[9],
                    'int_idUsuarioCreacion': row[10],
                    'int_idUsuarioModificacion': row[11],
                }
                for row in rows
            ]
        return Response(clausulas)
    def post(self, request):
        serializer = ClausulasIncluidasSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tr_clausulasincluidas 
                (str_idSuscripcion, int_idTipoSolicitud,int_idClausulaLegal, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s, %s, NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['str_idSuscripcion'],
                    serializer.validated_data['int_idTipoSolicitud'],
                    serializer.validated_data['int_idClausulaLegal'],
                    serializer.validated_data['int_idUsuarioCreacion'],
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ClausulasIncluidasDetalle(APIView):
    def get_object(self, pk):
        # Obtiene un solo registro por clave primaria
        with connection.cursor() as cursor:
            cursor.execute("""
            SELECT 
                c.int_idClausulasIncluidas, 
                c.str_idSuscripcion , 
                c.int_idTipoSolicitud, 
                ts.str_CodTipoSol AS tipo_solicitud_codigo,
                ts.str_Nombre AS tipo_solicitud_nombre,
                c.int_idClausulaLegal, 
                cl.str_CodClausulasLegales AS clausula_legal_codigo,
                cl.str_Nombre AS clausula_legal_nombre,
                c.dt_FechaCreacion, 
                c.dt_FechaModificacion, 
                c.int_idUsuarioCreacion, 
                c.int_idUsuarioModificacion
            FROM 
                tr_clausulasincluidas c
            INNER JOIN 
                tm_tiposolicitud ts ON c.int_idTipoSolicitud = ts.int_idTipoSolicitud
            INNER JOIN 
                tm_clausulaslegales cl ON c.int_idClausulaLegal = cl.int_idClausulasLegales
            WHERE 
                c.int_idClausulasIncluidas = %s 
        """, [pk])
            row = cursor.fetchone()
            if row:
                return {
                    'int_idClausulasIncluidas': row[0],
                    'int_idSuscripcion': row[1],
                    'int_idTipoSolicitud': row[2],
                    'tipo_solicitud_codigo': row[3],
                    'tipo_solicitud_nombre': row[4],
                    'int_idClausulaLegal': row[5],
                    'clausula_legal_codigo': row[6],
                    'clausula_legal_nombre': row[7],
                    'dt_FechaCreacion': row[8],
                    'dt_FechaModificacion': row[9],
                    'int_idUsuarioCreacion': row[10],
                    'int_idUsuarioModificacion': row[11],
                }
            return None

    def get(self, request, pk):
        clausula = self.get_object(pk)
        if clausula:
            return Response(clausula)
        return Response(status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk):
        clausula = self.get_object(pk)
        if not clausula:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = ClausulasIncluidasUpdateSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                UPDATE tr_clausulasincluidas 
                SET  int_idTipoSolicitud=%s,int_idClausulaLegal=%s, dt_FechaModificacion=NOW(), int_idUsuarioModificacion=%s 
                WHERE int_idClausulasIncluidas=%s
                """
                cursor.execute(query, [
                    serializer.validated_data['int_idTipoSolicitud'],
                    serializer.validated_data['int_idClausulaLegal'],
                    serializer.validated_data['int_idUsuarioModificacion'],
                    pk
                ])
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request,suscripcion, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tr_clausulasincluidas WHERE str_idSuscripcion  = %s AND int_idTipoSolicitud = %s", [suscripcion,pk])
        return Response(status=status.HTTP_204_NO_CONTENT)
    

class ClausulasActivasList(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'int_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = """
            SELECT 
                c.int_idClausulasActivas , 
                c.str_idSuscripcion , 
                int_idSolicitudes ,
                c.int_idTipoSolicitud, 
                ts.str_CodTipoSol AS tipo_solicitud_codigo,
                ts.str_Nombre AS tipo_solicitud_nombre,
                c.int_idClausulaLegal, 
                cl.str_CodClausulasLegales AS clausula_legal_codigo,
                cl.str_Nombre AS clausula_legal_nombre,
                c.dt_FechaCreacion, 
                c.dt_FechaModificacion, 
                c.int_idUsuarioCreacion, 
                c.int_idUsuarioModificacion
            FROM 
                tr_clausulasactivas c
            INNER JOIN 
                tm_tiposolicitud ts ON c.int_idTipoSolicitud = ts.int_idTipoSolicitud
            INNER JOIN 
                tm_clausulaslegales cl ON c.int_idClausulaLegal = cl.int_idClausulasLegales
            WHERE 
                c.str_idSuscripcion  = %s
        """
        params = [str_idSuscripcion]
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                     'int_idClausulasActivas': row[0],
                    'int_idSuscripcion': row[1],
                    'int_idSolicitudes': row[2],
                    'int_idTipoSolicitud': row[3],
                    'tipo_solicitud_codigo': row[4],
                    'tipo_solicitud_nombre': row[5],
                    'int_idClausulaLegal': row[6],
                    'clausula_legal_codigo': row[7],
                    'clausula_legal_nombre': row[8],
                    'dt_FechaCreacion': row[9],
                    'dt_FechaModificacion': row[10],
                    'int_idUsuarioCreacion': row[11],
                    'int_idUsuarioModificacion': row[12],
                }
                for row in rows
            ]
        return Response(clausulas)
    def post(self, request):
        serializer = ClausulasActivasSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tr_clausulasactivas 
                (str_idSuscripcion, int_idSolicitudes, int_idClausulaIncluida, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s, %s, NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['str_idSuscripcion'],
                    serializer.validated_data['int_idSolicitudes'],
                    serializer.validated_data['int_idClausulaIncluida'],
                    serializer.validated_data['int_idUsuarioCreacion'],
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
class ActivarClausulaPorNombre(APIView):
    def post(self, request):
        serializer = ClausulasActivasNombreSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
               
                select_query = """
                SELECT int_idClausulasLegales FROM tm_clausulaslegales 
                WHERE str_Nombre = %s AND str_idSuscripcion = %s
                """
                cursor.execute(select_query, [serializer.validated_data['str_Nombre'], serializer.validated_data['str_idSuscripcion']])
                result = cursor.fetchone()

                if result:
                    idclausula = result[0]

                    
                    check_query = """
                    SELECT int_idClausulasIncluidas  FROM tr_clausulasincluidas 
                    WHERE str_idSuscripcion = %s AND int_idTipoSolicitud = %s AND int_idClausulaLegal = %s
                    """
                    cursor.execute(check_query, [
                        serializer.validated_data['str_idSuscripcion'],
                        serializer.validated_data['int_idTipoSolicitud'],
                        idclausula
                    ])
                    exists = cursor.fetchone()

                    if not exists:
                        
                        insert_query = """
                        INSERT INTO tr_clausulasincluidas 
                        (str_idSuscripcion, int_idTipoSolicitud, int_idClausulaLegal, dt_FechaCreacion, int_idUsuarioCreacion)
                        VALUES (%s, %s, %s, NOW(), %s) 
                        """
                        cursor.execute(insert_query, [
                            serializer.validated_data['str_idSuscripcion'],
                            serializer.validated_data['int_idTipoSolicitud'],
                            idclausula,
                            serializer.validated_data['int_idUsuarioCreacion'],
                        ])
                        cursor.execute("SELECT LAST_INSERT_ID()")
                        id_clausula_incluida = cursor.fetchone()[0]
                    else:
                       
                        id_clausula_incluida = exists[0]
                else:
                    
                    raise ValueError("No se encontró una cláusula con el nombre y suscripción proporcionados.")

            
            with connection.cursor() as cursor:
                insert_query = """
                INSERT INTO tr_clausulasactivas 
                (str_idSuscripcion, int_idSolicitudes, int_idClausulaIncluida, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s, %s, NOW(), %s)
                """
                cursor.execute(insert_query, [
                    serializer.validated_data['str_idSuscripcion'],
                    serializer.validated_data['int_idSolicitudes'],
                    id_clausula_incluida,  
                    serializer.validated_data['int_idUsuarioCreacion'],
                ])

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
class ClausulasActivasAPIView(APIView):
    def get(self, request, suscripcion, solicitud):
        try:
            with connection.cursor() as cursor:
                query = """
                SELECT int_idClausulaIncluida 
                FROM tr_clausulasactivas 
                WHERE str_idSuscripcion = %s AND int_idSolicitudes = %s
                """
                cursor.execute(query, [suscripcion, solicitud])
                clausulas_activas = cursor.fetchall()

            clausulas_ids = [clausula[0] for clausula in clausulas_activas]

            return Response({"clausulas_activas": clausulas_ids}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
class EliminarClausulaAPIView(APIView):
    def delete(self, request, clausula_id, suscripcion,solicitud):
        try:
            with connection.cursor() as cursor:
                query = """
                DELETE FROM tr_clausulasactivas 
                WHERE int_idClausulaIncluida  = %s AND  str_idSuscripcion  = %s  AND int_idSolicitudes= %s
                """
                cursor.execute(query, [clausula_id,suscripcion,solicitud])
            return Response({"message": "Cláusula eliminada correctamente"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)