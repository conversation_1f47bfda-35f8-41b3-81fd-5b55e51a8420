from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from django.conf import settings
import os
from .serializers import ArchivoSerializer,PlantillaSerializer,FirmaSerializer,ArchivoFirmadoSerializer
from django.http import FileResponse, Http404
from PIL import Image
from io import BytesIO
import requests
from django.core.files.uploadedfile import InMemoryUploadedFile
from empresas.views import EmpresaList
from tiposSolicitud.views import TipSolicitudListCreateAPIView
from unidadesNegocio.views import UnidadNegocioListGeneralAPIView
from rest_framework.test import APIRequestFactory 
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation
from django.http import HttpResponse
import shutil

class ArchivoUploadAPIView(APIView):
    def post(self, request):
        serializer = ArchivoSerializer(data=request.data)
        
        if serializer.is_valid():
            try:
                # Obtener datos del archivo y otros parámetros
                archivo = request.FILES.get('archivo')
                if not archivo:
                    return Response({"mensaje": "Archivo no proporcionado."}, status=status.HTTP_400_BAD_REQUEST)

                str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
                str_CodSolicitudes = serializer.validated_data['str_CodSolicitudes']
                int_idSolicitudes = serializer.validated_data['int_idSolicitudes']
                str_CodTipoDocumento = serializer.validated_data['str_CodTipoDocumento']
                int_idUsuarioCreacion = serializer.validated_data['int_idUsuarioCreacion']
                str_tipoAdjunto = request.data.get('tipo_adjunto', None)
                with connection.cursor() as cursor:
                    select_query = """
                        SELECT int_idTipoDocumentos  
                        FROM tm_tipodocumento 
                        WHERE str_idSuscripcion  = %s AND str_CodTipoDocumento = %s
                    """
                    cursor.execute(select_query, [str_idSuscriptor, str_CodTipoDocumento])
                    idTipoDocumento = cursor.fetchone()
                    if not idTipoDocumento:
                        return Response(
                            {"error": "No se encontró el tipo de documento."},
                            status=status.HTTP_404_NOT_FOUND
                        )
                    else:
                        idTipoDocumento[0],

                
                    int_idTipoDocumento = idTipoDocumento[0]
                # Verificar la existencia de la solicitud en la base de datos
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM tr_solicitudes WHERE int_idSolicitudes = %s", [int_idSolicitudes])
                    if cursor.fetchone() is None:
                        return Response({"mensaje": "La solicitud especificada no existe."}, status=status.HTTP_400_BAD_REQUEST)

                # Crear directorio de destino
                directorio_base = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento)
                if str_tipoAdjunto:  # Si viene, lo agregas al final
                    directorio_base = os.path.join(directorio_base, str(str_tipoAdjunto))
   
                os.makedirs(directorio_base, exist_ok=True)

                # Procesar nombre y ruta del archivo
                nombre_archivo_original = archivo.name
                extension_archivo = os.path.splitext(nombre_archivo_original)[1]
                nombre_archivo_base = os.path.splitext(nombre_archivo_original)[0]
                ruta_archivo = os.path.join(directorio_base, nombre_archivo_original)

                # Manejar versiones de archivos duplicados
                contador_version = 2
                while os.path.exists(ruta_archivo):
                    nuevo_nombre_archivo = f"{nombre_archivo_base}-version{contador_version}{extension_archivo}"
                    ruta_archivo = os.path.join(directorio_base, nuevo_nombre_archivo)
                    contador_version += 1

                # Guardar el archivo en el sistema de archivos
                with open(ruta_archivo, 'wb+') as destino:
                    for chunk in archivo.chunks():
                        destino.write(chunk)

                # Obtener tamaño y nombre final del archivo
                nombre_archivo = os.path.splitext(os.path.basename(ruta_archivo))[0] 
                tamaño_archivo = archivo.size

                # Insertar registro en la base de datos
                with connection.cursor() as cursor:
                    query = """
                    INSERT INTO tr_archivosdocumentos 
                    (str_idSuscriptor, int_idSolicitudes, int_idTipoDocumento, str_RutaArchivo, str_NombreArchivo, str_ExtencionArchivo, str_TamañoArchivo, dt_FechaCreacion, int_idUsuarioCreacion)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), %s)
                    """
                    cursor.execute(query, [
                        str_idSuscriptor,
                        int_idSolicitudes,
                        int_idTipoDocumento,
                        ruta_archivo,
                        nombre_archivo,
                        extension_archivo[1:],  
                        tamaño_archivo,
                        int_idUsuarioCreacion
                    ])

                return Response({"mensaje": "Archivo subido exitosamente."}, status=status.HTTP_201_CREATED)

            except Exception as e:
                # Manejar errores inesperados
                return Response({"mensaje": f"Error al procesar la solicitud: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Si el serializer no es válido, retornar errores
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
class ArchivoListAPIView(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitudes = request.query_params.get('int_idSolicitudes')
        str_CodTipoDocumento = request.query_params.get('str_CodTipoDocumento')
        str_tipoAdjunto = request.query_params.get('str_tipoAdjunto', None)

        if not all([str_idSuscriptor, int_idSolicitudes, str_CodTipoDocumento]):
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor', 'int_idSolicitudes' y 'int_idTipoDocumento' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with connection.cursor() as cursor:
                select_queryTD = """
                    SELECT int_idTipoDocumentos
                    FROM tm_tipodocumento
                    WHERE str_CodTipoDocumento = %s AND str_idSuscripcion = %s
                    """
                cursor.execute(select_queryTD, [str_CodTipoDocumento, str_idSuscriptor])
                resultadoTD = cursor.fetchone()
                if not resultadoTD:
                    return Response({"error": "Tipo de solicitud no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                int_idTipoDocumento = resultadoTD[0]
                query = """
                SELECT str_RutaArchivo, str_NombreArchivo, str_ExtencionArchivo, str_TamañoArchivo, dt_FechaCreacion, int_idArchivos
                FROM tr_archivosdocumentos
                WHERE str_idSuscriptor = %s AND int_idSolicitudes = %s AND int_idTipoDocumento = %s
                """
                cursor.execute(query, [str_idSuscriptor, int_idSolicitudes, int_idTipoDocumento])
                archivos = cursor.fetchall()

            resultado = []
            for archivo in archivos:
                item = {
                    "ruta_archivo": archivo[0],
                    "nombre_archivo": archivo[1] + '.' + archivo[2],
                    "tamaño_archivo": archivo[3],
                    "fecha_creacion": archivo[4],
                    "int_idArchivos": archivo[5]
                }
                # Si el tipo de documento es DOAD, extraer el tipo de adjunto
                if str_CodTipoDocumento == "DOAD":
                    # Tomar el penúltimo segmento como tipo_adjunto (el último es el archivo)
                    ruta = os.path.normpath(archivo[0])
                    # Separar la ruta en partes
                    partes = ruta.split(os.sep)
                    # El tipo de adjunto sería el penúltimo segmento
                    if len(partes) > 1:
                        item["tipo_adjunto"] = partes[-2]
                    else:
                        item["tipo_adjunto"] = ""
                resultado.append(item)

            return Response(resultado, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class ArchivoDownloadAPIView(APIView):
    def get(self, request):
        # Obtener parámetros de consulta
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        nombre_archivo = request.query_params.get('nombre_archivo')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_CodTipoDocumento = request.query_params.get('str_CodTipoDocumento')
        str_tipoAdjunto = request.query_params.get('tipo_adjunto', None)  # Nuevo: opcional

        # Validar parámetros obligatorios
        if not all([str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento, nombre_archivo]):
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor', 'str_CodSolicitudes', 'str_CodTipoDocumento' y 'nombre_archivo' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Construir la ruta del archivo, agregando str_tipoAdjunto si viene
        directorio_base = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento)
        if str_tipoAdjunto:
            directorio_base = os.path.join(directorio_base, str(str_tipoAdjunto))
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)

        # Verificar si el archivo existe
        if not os.path.exists(ruta_archivo):
            return Response({"error": "El archivo no existe."}, status=status.HTTP_404_NOT_FOUND)

        try:
            # Devolver el archivo como respuesta
            archivo_response = FileResponse(open(ruta_archivo, 'rb'), as_attachment=True)
            archivo_response['Content-Disposition'] = f'attachment; filename="{nombre_archivo}"'
            archivo_response['Content-Type'] = 'application/octet-stream'
            return archivo_response

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class ArchivoDownloadporTipo(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_CodTipoDocumento = request.query_params.get('str_CodTipoDocumento')

        if not all([str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento]):
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor', 'str_CodSolicitudes' y 'str_CodTipoDocumento' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        directorio_base = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento)

        if not os.path.exists(directorio_base):
            return Response({"error": "La carpeta especificada no existe."}, status=status.HTTP_404_NOT_FOUND)

        archivos_encontrados = os.listdir(directorio_base)

        if not archivos_encontrados:
            return Response({"error": "No se encontraron archivos en la carpeta especificada."}, status=status.HTTP_404_NOT_FOUND)

        archivos_encontrados.sort(key=lambda archivo: os.path.getmtime(os.path.join(directorio_base, archivo)), reverse=True)

        nombre_archivo = archivos_encontrados[0]
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)

        try:
            archivo_response = FileResponse(open(ruta_archivo, 'rb'), as_attachment=True)
            archivo_response['Content-Disposition'] = f'attachment; filename="{nombre_archivo}"'
            archivo_response['Content-Type'] = 'application/octet-stream'
            return archivo_response

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class ArchivoDatosporTipo(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_CodTipoDocumento = request.query_params.get('str_CodTipoDocumento')

        # Verificación de parámetros obligatorios
        if not all([str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento]):
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor', 'str_CodSolicitudes' y 'str_CodTipoDocumento' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Construcción del directorio base
        directorio_base = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento)

        # Verificación de existencia del directorio
        if not os.path.exists(directorio_base):
            return Response({"error": "La carpeta especificada no existe."}, status=status.HTTP_404_NOT_FOUND)

        # Listar archivos en el directorio
        archivos_encontrados = os.listdir(directorio_base)

        if not archivos_encontrados:
            return Response({"error": "No se encontraron archivos en la carpeta especificada."}, status=status.HTTP_404_NOT_FOUND)

        # Ordenar archivos por fecha de modificación (el más reciente primero)
        archivos_encontrados.sort(key=lambda archivo: os.path.getmtime(os.path.join(directorio_base, archivo)), reverse=True)

        # Seleccionar el último archivo
        nombre_archivo = archivos_encontrados[0]
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)
        fecha_modificacion = os.path.getmtime(ruta_archivo)
        tamaño_archivo = os.path.getsize(ruta_archivo)

        # Retornar los datos del archivo como JSON
        return Response({
            "nombre_archivo": nombre_archivo,
            "ruta_archivo": ruta_archivo,
            "fecha_modificacion": fecha_modificacion,
            "tamaño_archivo": tamaño_archivo
        }, status=status.HTTP_200_OK)
class ArchivoDownloadPlantilla(APIView):
    def get(self, request):
        str_CodTipoSolicitud = request.query_params.get('str_CodTipoSolicitud')
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        str_TipoPlantilla = request.query_params.get('str_TipoPlantilla')
        directorio_base = os.path.join(settings.PLANTILLAS_ROOT, str_idSuscripcion,int_idEmpresa,str_TipoPlantilla,str_CodTipoSolicitud)

        if not os.path.exists(directorio_base):
            return Response({"error": "La carpeta especificada no existe."}, status=status.HTTP_404_NOT_FOUND)

        archivos_encontrados = os.listdir(directorio_base)

        if not archivos_encontrados:
            return Response({"error": "No se encontraron archivos en la carpeta especificada."}, status=status.HTTP_404_NOT_FOUND)

        archivos_encontrados.sort(key=lambda archivo: os.path.getmtime(os.path.join(directorio_base, archivo)), reverse=True)

        nombre_archivo = archivos_encontrados[0]
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)

        try:
            archivo_response = FileResponse(open(ruta_archivo, 'rb'), as_attachment=True)
            archivo_response['Content-Disposition'] = f'attachment; filename="{nombre_archivo}"'
            archivo_response['Content-Type'] = 'application/octet-stream'
            return archivo_response

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)        

class ArchivoDatosporPlantilla(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        str_CodTipoSolicitud = request.query_params.get('str_CodTipoSolicitud')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        str_TipoPlantilla = request.query_params.get('str_TipoPlantilla')
        
        # Construcción del directorio base
        directorio_base = os.path.join(settings.PLANTILLAS_ROOT, str_idSuscripcion,int_idEmpresa,str_TipoPlantilla,str_CodTipoSolicitud)

        # Verificación de existencia del directorio
        if not os.path.exists(directorio_base):
            return Response({"error": "La carpeta especificada no existe."}, status=status.HTTP_404_NOT_FOUND)

        # Listar archivos en el directorio
        archivos_encontrados = os.listdir(directorio_base)

        if not archivos_encontrados:
            return Response({"error": "No se encontraron archivos en la carpeta especificada."}, status=status.HTTP_404_NOT_FOUND)

        # Ordenar archivos por fecha de modificación (el más reciente primero)
        archivos_encontrados.sort(key=lambda archivo: os.path.getmtime(os.path.join(directorio_base, archivo)), reverse=True)

        # Seleccionar el último archivo
        nombre_archivo = archivos_encontrados[0]
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)
        fecha_modificacion = os.path.getmtime(ruta_archivo)
        tamaño_archivo = os.path.getsize(ruta_archivo)

        # Retornar los datos del archivo como JSON
        return Response({
            "nombre_archivo": nombre_archivo,
            "ruta_archivo": ruta_archivo,
            "fecha_modificacion": fecha_modificacion,
            "tamaño_archivo": tamaño_archivo
        }, status=status.HTTP_200_OK)
class ArchivoDeleteAPIView(APIView):
    def delete(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        nombre_archivo = request.query_params.get('nombre_archivo')
        str_CodTipoDocumento = request.query_params.get('str_CodTipoDocumento')
        int_idArchivos = request.query_params.get('int_idArchivos')
        str_tipoAdjunto = request.query_params.get('tipo_adjunto', None)
        if not all([str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento, nombre_archivo, int_idArchivos]):
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor', 'str_CodSolicitudes', 'str_CodTipoDocumento', 'nombre_archivo' e 'int_idArchivos' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        directorio_base = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento)
        if str_tipoAdjunto:  # Si viene, lo agregas al final
                    directorio_base = os.path.join(directorio_base, str(str_tipoAdjunto))
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)

        # Intentar eliminar el archivo de la base de datos primero
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    DELETE FROM tr_archivosdocumentos
                    WHERE int_idArchivos = %s
                """, [int_idArchivos])
            
            # Verificar si el archivo existe en el sistema de archivos
            if os.path.exists(ruta_archivo):
                os.remove(ruta_archivo)
            else:
                return Response({"error": ruta_archivo}, status=status.HTTP_404_NOT_FOUND)

            return Response({"mensaje": "Archivo y registro en la base de datos eliminados exitosamente."}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        

class ArchivoUploadPlantilla(APIView):
    def post(self, request):
        serializer = PlantillaSerializer(data=request.data)
        
        if serializer.is_valid():
            try:
                # Obtener datos del archivo y otros parámetros
                archivo = request.FILES.get('archivo')
                if not archivo:
                    return Response({"mensaje": "Archivo no proporcionado."}, status=status.HTTP_400_BAD_REQUEST)

                str_idSuscripcion = serializer.validated_data['str_idSuscripcion']
                str_CodTipoSol = serializer.validated_data['str_CodTipoSol']
                int_idUsuarioCreacion = serializer.validated_data['int_idUsuarioCreacion']
                int_idEmpresa = serializer.validated_data['int_idEmpresa']
                str_TipoPlantilla = serializer.validated_data['str_TipoPlantilla']
                with connection.cursor() as cursor:
                    select_query = """
                        SELECT int_idTipoSolicitud  
                        FROM tm_tiposolicitud 
                        WHERE str_idSuscripcion  = %s AND str_CodTipoSol = %s
                    """
                    cursor.execute(select_query, [str_idSuscripcion, str_CodTipoSol])
                    idTipoSolicitud = cursor.fetchone()
                    if not idTipoSolicitud:
                        return Response(
                            {"error": "No se encontró el tipo de solicitud."},
                            status=status.HTTP_404_NOT_FOUND
                        )
                    else:
                        idTipoSolicitud[0],

                
                    int_idTipoSolicitud = idTipoSolicitud[0]

                # Crear directorio de destino
                directorio_base = os.path.join(settings.PLANTILLAS_ROOT, str_idSuscripcion,int_idEmpresa,str_TipoPlantilla, str_CodTipoSol)
                os.makedirs(directorio_base, exist_ok=True)

                # Procesar nombre y ruta del archivo
                nombre_archivo_original = archivo.name
                extension_archivo = os.path.splitext(nombre_archivo_original)[1]
                nombre_archivo_base = os.path.splitext(nombre_archivo_original)[0]
                ruta_archivo = os.path.join(directorio_base, nombre_archivo_original)

                # Manejar versiones de archivos duplicados
                contador_version = 2
                while os.path.exists(ruta_archivo):
                    nuevo_nombre_archivo = f"{nombre_archivo_base}-version{contador_version}{extension_archivo}"
                    ruta_archivo = os.path.join(directorio_base, nuevo_nombre_archivo)
                    contador_version += 1

                # Guardar el archivo en el sistema de archivos
                with open(ruta_archivo, 'wb+') as destino:
                    for chunk in archivo.chunks():
                        destino.write(chunk)
                        
                return Response({"mensaje": "Archivo subido exitosamente."}, status=status.HTTP_201_CREATED)

            except Exception as e:
                # Manejar errores inesperados
                return Response({"mensaje": f"Error al procesar la solicitud: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Si el serializer no es válido, retornar errores
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
class FirmaUpload(APIView):
    def post(self, request):
        serializer = FirmaSerializer(data=request.data)
        
        if serializer.is_valid():
            try:
                firma = request.FILES.get('firma')
                if not firma:
                    return Response(
                        {"mensaje": "Archivo no proporcionado."}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Validar el formato del archivo
                if not firma.name.endswith(".png"):
                    return Response(
                        {"mensaje": "Solo se permiten archivos en formato PNG."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # Datos del serializer
                str_idSuscripcion = serializer.validated_data['str_idSuscripcion']
                int_idUsuario = str(serializer.validated_data['int_idUsuario'])

                # Crear directorio de destino
                directorio_base = os.path.join(settings.FIRMAS_ROOT, str_idSuscripcion, int_idUsuario)
                os.makedirs(directorio_base, exist_ok=True)

                # Definir el nombre del archivo como 'firma.png'
                nombre_archivo = "firma.png"
                ruta_archivo = os.path.join(directorio_base, nombre_archivo)

                # Reemplazar el archivo si ya existe
                with open(ruta_archivo, 'wb+') as destino:
                    for chunk in firma.chunks():
                        destino.write(chunk)

                return Response(
                    {"mensaje": "Firma subida y reemplazada exitosamente."}, 
                    status=status.HTTP_201_CREATED
                )

            except Exception as e:
                # Manejar errores inesperados
                return Response(
                    {"mensaje": f"Error al procesar la solicitud: {str(e)}"}, 
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        
        # Si el serializer no es válido, retornar errores
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
class FirmaDownload(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')  # Corregido el parámetro
        int_idUsuario = request.query_params.get('int_idUsuario')  # Corregido el parámetro

        # Validar parámetros
        if not str_idSuscripcion or not int_idUsuario:
            return Response(
                {"mensaje": "Faltan parámetros obligatorios (str_idSuscripcion o int_idUsuario)."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Directorio del usuario
            directorio_base = os.path.join(settings.FIRMAS_ROOT, str_idSuscripcion, int_idUsuario)

            if not os.path.exists(directorio_base):
                return Response(
                    {"mensaje": "No se encontraron firmas para este usuario."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Obtener archivos del directorio
            archivos = [os.path.join(directorio_base, f) for f in os.listdir(directorio_base) if os.path.isfile(os.path.join(directorio_base, f))]
            if not archivos:
                return Response(
                    {"mensaje": "No se encontraron archivos en el directorio."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Seleccionar el archivo más reciente
            archivo_reciente = max(archivos, key=os.path.getmtime)

            # Servir el archivo como respuesta
            return FileResponse(open(archivo_reciente, 'rb'), content_type='image/png')

        except Exception as e:
            return Response(
                {"mensaje": f"Error al procesar la solicitud: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
class ArchivoUploadFirmado(APIView):
    def post(self, request):
        serializer = ArchivoFirmadoSerializer(data=request.data)

        if serializer.is_valid():
            try:
                str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
                str_CodSolicitudes = serializer.validated_data['str_CodSolicitudes']
                str_CodTipoDocumento = serializer.validated_data['str_CodTipoDocumento']
                int_idUsuarioCreacion = serializer.validated_data['int_idUsuarioCreacion']

                # Definir el directorio donde se encuentra el archivo original y la firma
                directorio_base = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, str_CodTipoDocumento)

                # Buscar el último archivo subido en el directorio base
                archivos = os.listdir(directorio_base)
                archivos = [archivo for archivo in archivos if archivo.endswith(('jpg', 'jpeg', 'png', 'pdf', 'docx'))]  # Filtrar por tipo de archivo
                if not archivos:
                    return Response({"mensaje": "No se encontraron archivos en el directorio."}, status=status.HTTP_404_NOT_FOUND)

                # Ordenar los archivos por fecha de modificación (último archivo subido)
                archivos.sort(key=lambda x: os.path.getmtime(os.path.join(directorio_base, x)), reverse=True)
                archivo_original = archivos[0]  # Tomar el más reciente
                ruta_archivo_original = os.path.join(directorio_base, archivo_original)

                # Obtener la última firma en la ruta de firmas
                directorio_firmas = os.path.join(settings.FIRMAS_ROOT, str_idSuscriptor, int_idUsuarioCreacion)
                imagenes_firmas = os.listdir(directorio_firmas)
                imagenes_firmas = [imagen for imagen in imagenes_firmas if imagen.endswith(('jpg', 'jpeg', 'png'))]  # Filtrar por imagen
                if not imagenes_firmas:
                    return Response({"mensaje": "No se encontraron firmas en el directorio."}, status=status.HTTP_404_NOT_FOUND)

                # Ordenar las imágenes de firma por fecha (última firma)
                imagenes_firmas.sort(key=lambda x: os.path.getmtime(os.path.join(directorio_firmas, x)), reverse=True)
                imagen_firma = imagenes_firmas[0]  # Tomar la más reciente
                ruta_imagen_firma = os.path.join(directorio_firmas, imagen_firma)

                # Cargar la firma como imagen
                firma_imagen = Image.open(ruta_imagen_firma)
                firma_imagen = firma_imagen.convert("RGBA")  # Asegurarse de que la firma tenga transparencia

                # Si el archivo original es un DOCX
                if archivo_original.endswith('.docx'):
                    self.firmar_docx(ruta_archivo_original, firma_imagen, directorio_base)

                return Response({"mensaje": "Archivo firmado y guardado exitosamente."}, status=status.HTTP_201_CREATED)

            except Exception as e:
                return Response({"mensaje": f"Error al procesar la solicitud: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def firmar_docx(self, ruta_docx, firma, directorio_base):
        """
        Función para firmar un archivo DOCX con una imagen de firma.
        La firma se coloca en el pie de página de cada sección del documento.
        """
        from docx import Document
        from docx.shared import Inches
        from io import BytesIO
    
        # Abrir el archivo DOCX con python-docx
        doc = Document(ruta_docx)
    
        # Convertir la imagen de la firma a un formato adecuado para insertar en el documento
        with BytesIO() as img_stream:
            firma.save(img_stream, format="PNG")
            img_stream.seek(0)
    
            # Recorrer todas las secciones del documento y agregar la firma al pie de página de cada una
            for section in doc.sections:
                footer = section.footer  # Obtener el pie de página de la sección
    
                # Si la sección tiene párrafos, agregar la firma en el primero
                # Si no, agregar un nuevo párrafo
                footer_paragraph = footer.paragraphs[0] if footer.paragraphs else footer.add_paragraph()
                run = footer_paragraph.add_run()
                run.add_picture(img_stream, width=Inches(0.75))  # Ajustar el tamaño de la firma según sea necesario
    
        # Generar un nombre único para el archivo firmado
        nombre_archivo_firmado = self.obtener_nombre_unico(ruta_docx, directorio_base)
    
        # Guardar el archivo DOCX con la firma insertada
        ruta_docx_firmado = os.path.join(directorio_base, nombre_archivo_firmado)
        doc.save(ruta_docx_firmado)
    def obtener_nombre_unico(self, ruta_archivo_original, directorio_base):
        """
        Función para generar un nombre único para el archivo firmado,
        agregando un sufijo con un número incremental si ya existe un archivo con ese nombre.
        """
        base, ext = os.path.splitext(ruta_archivo_original)
        nombre_base = os.path.basename(base)

        # Verificar si ya existe un archivo firmado con ese nombre
        i = 1
        while True:
            # Crear el nombre del archivo firmado con el sufijo único
            nombre_firmado = f"{nombre_base}_Aprobado{'' if i == 1 else i}{ext}"
            ruta_firmado = os.path.join(directorio_base, nombre_firmado)
            if not os.path.exists(ruta_firmado):
                return nombre_firmado
            i += 1
            
            
class DownloadExcelTemplate(APIView):
    def get(self, request):
        try:
            str_idSuscriptor = request.query_params.get('str_idSuscriptor')
            if not str_idSuscriptor:
                return Response({"error": "El parámetro 'str_idSuscriptor' es requerido."}, status=400)

            factory = APIRequestFactory()
            simulated_request = factory.get('', {"str_idSuscripcion": str_idSuscriptor})
            simulated_request.user = request.user   

            tipo_solicitud_view = TipSolicitudListCreateAPIView.as_view()
            response_tipos = tipo_solicitud_view(simulated_request)
            api_tipo_contratos = response_tipos.data

            empresas_view = EmpresaList.as_view()
            response_empresas = empresas_view(simulated_request)
            api_empresas = response_empresas.data

            unidades_view = UnidadNegocioListGeneralAPIView.as_view()
            response_unidades = unidades_view(simulated_request)
            api_unidades_negocio = response_unidades.data


            # Extraer IDs y Nombres
            tipos_contrato_ids = [tipo["int_idTipoSolicitud"] for tipo in api_tipo_contratos]
            tipos_contrato_nombres = [tipo["int_Nombre"] for tipo in api_tipo_contratos]

            empresas_ids = [empresa["int_idEmpresa"] for empresa in api_empresas]
            empresas_nombres = [empresa["str_NombreEmpresa"] for empresa in api_empresas]

            unidades_ids = [unidad["int_idUnidadesNegocio"] for unidad in api_unidades_negocio]
            unidades_nombres = [unidad["str_Descripcion"] for unidad in api_unidades_negocio]
            
            wb = Workbook()
            ws = wb.active
            ws.title = "Plantilla"

            headers = [
                 "Nom_tipoSol", "Nom_empresa",
                "Honorarios",  "Nom_unidadNegocio",
                "De Terceros", "Fecha Esperada (aaaa-mm-dd)" ,
                "Documento Cliente Asociado", "Nombres Cliente Asociado",
                 "Apellidos Cliente Asociado",  "Moneda", "Objeto del Contrato", 
                 "Presupuesto", "Margen" , "Plazo de Solicitud", "Tipo de Servicio" , 
                 "Informacion Adicional" ,"Condición de Pago" , "Consulto Asignado", 
                 "Renovacion Automática", "Detalle Renovación Automática", "Ajuste de Honorarios", 
                 "Detalle de Ajuste de Honorarios", "Garantía", "Detalle de Garantía", "Forma de Pago", 
                 "Resolución Anticipada", "Detalle de Resolución Anticipada","Penalidades", "Detalle Penalidades" , 
                 "Bien Mueble o Inmueble", "Bien Partida Certificada", "Bien Dirección", "Bien Uso", "Bien Descripcion", 
                 "Renta Pactada", "Importe de Venta" , "Plazo de Arriendo" 
            ]
            ws.append(headers)

            # Aplicar estilos a los encabezados
            header_fill = PatternFill(start_color="BDD7EE", end_color="BDD7EE", fill_type="solid")
            header_font = Font(bold=True, color="000000")
            header_alignment = Alignment(horizontal="center", vertical="center")
            header_border = Border(left=Side(style="thin", color="000000"), right=Side(style="thin", color="000000"), top=Side(style="thin", color="000000"), bottom=Side(style="thin", color="000000"))
            header_width = 25
            header_height = 20

            for col_num, column_title in enumerate(headers, 1):
                col_letter = get_column_letter(col_num)
                cell = ws[f"{col_letter}1"]
                cell.fill = header_fill
                cell.font = header_font
                cell.alignment = header_alignment
                cell.border = header_border
                ws.column_dimensions[col_letter].width = header_width
                ws.row_dimensions[1].height = header_height

            # Crear hojas auxiliares con nombres específicos
            ws_tipos = wb.create_sheet(title="Nom_tipoSol")
            ws_empresas = wb.create_sheet(title="Nom_empresa")
            ws_unidades = wb.create_sheet(title="Nom_unidadNegocio")

            # Función para aplicar bordes a las celdas
            def aplicar_bordes(ws, row, col):
                cell = ws[f"{get_column_letter(col)}{row}"]
                border_style = Side(style="thin", color="000000")
                cell.border = Border(left=border_style, right=border_style, top=border_style, bottom=border_style)
                cell.alignment = Alignment(horizontal="left")

            for index, (id_, nombre) in enumerate(zip(tipos_contrato_ids, tipos_contrato_nombres), start=1):
                ws_tipos[f"A{index}"] = id_
                ws_tipos[f"B{index}"] = f"{nombre}"
                aplicar_bordes(ws_tipos, index, 1)
                aplicar_bordes(ws_tipos, index, 2)

            for index, (id_, nombre) in enumerate(zip(empresas_ids, empresas_nombres), start=1):
                ws_empresas[f"A{index}"] = id_
                ws_empresas[f"B{index}"] = f"{nombre}"
                aplicar_bordes(ws_empresas, index, 1)
                aplicar_bordes(ws_empresas, index, 2)

            for index, (id_, nombre) in enumerate(zip(unidades_ids, unidades_nombres), start=1):
                ws_unidades[f"A{index}"] = id_
                ws_unidades[f"B{index}"] = f"{nombre}"
                aplicar_bordes(ws_unidades, index, 1)
                aplicar_bordes(ws_unidades, index, 2)

            def create_dropdown(ws, col, sheet_name, end_row=100):
                col_letter = get_column_letter(col)
                validation = DataValidation(
                    type="list",
                    formula1=f"'{sheet_name}'!$B$1:$B${len(tipos_contrato_nombres)}",
                    allow_blank=True
                )
                ws.add_data_validation(validation)
                for row in range(2, end_row + 2):
                    validation.add(ws[f"{col_letter}{row}"])
            def create_dropdownMoneda(ws, col, opciones, end_row=100):
                col_letter = get_column_letter(col)
                opciones_str = ",".join(opciones)
                validation = DataValidation(
                    type="list",
                    formula1=f'"{opciones_str}"',
                    allow_blank=True
                )
                validation.error = 'Valor no válido'
                validation.errorTitle = 'Entrada no válida'
                validation.prompt = 'Selecciona una opción'
                validation.promptTitle = 'Opciones disponibles'
                ws.add_data_validation(validation)
                for row in range(2, end_row + 2):
                    validation.add(ws[f"{col_letter}{row}"])
            create_dropdown(ws, 1, "Nom_tipoSol")
            create_dropdown(ws, 2, "Nom_empresa")
            create_dropdown(ws, 4, "Nom_unidadNegocio")

            opciones_moneda = ["Moneda de Empresa", "dolares"]
            opciones_sino = ["si", "no"]
            opciones_bien = ["Inmueble", "Mueble"]

            create_dropdownMoneda(ws, 10, opciones_moneda)
            
            create_dropdownMoneda(ws, 5, opciones_sino)
            create_dropdownMoneda(ws, 19, opciones_sino)
            create_dropdownMoneda(ws, 21, opciones_sino)
            create_dropdownMoneda(ws, 23, opciones_sino)
            create_dropdownMoneda(ws, 26, opciones_sino)
            create_dropdownMoneda(ws, 28, opciones_sino)
            create_dropdownMoneda(ws, 30, opciones_bien)
            response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = 'attachment; filename="Plantilla.xlsx"'
            wb.save(response)
            return response

        except Exception as e:
            return Response({"error": str(e)}, status=500)
            
            
            
class DescargarManualUsuario(APIView):
    def get(self, request):
 
        nombre_manual = request.query_params.get('nombre_manual')
 

   

        # Construir la ruta del archivo
        directorio_base = os.path.join(settings.MANUALES_ROOT)
        ruta_archivo = os.path.join(directorio_base, nombre_manual)

        try:
            # Devolver el archivo como respuesta
            archivo_response = FileResponse(open(ruta_archivo, 'rb'), as_attachment=True)
            archivo_response['Content-Disposition'] = f'attachment; filename="{nombre_manual}"'
            archivo_response['Content-Type'] = 'application/octet-stream'
            return archivo_response

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            
class TransferirArchivosHistoricos(APIView):
    def post(self, request):
        data = request.data
        idEmpresa = data.get('idEmpresa')
        nombre_archivo = data.get('nombre_archivo')
        codContrato = data.get('codContrato')
        suscripcion = data.get('suscripcion')
        int_idSolicitudes = data.get('int_idSolicitudes')
        int_idUsuarioCreacion = data.get('int_idUsuarioCreacion')
        directorio_base = os.path.join(settings.ARCHIVOS_LECTOR, idEmpresa)

        if not os.path.exists(directorio_base):
            return Response(
                {"mensaje": directorio_base},
                status=status.HTTP_404_NOT_FOUND,
            )
        
        ruta_archivo_origen = os.path.join(directorio_base, nombre_archivo)
        if not os.path.exists(ruta_archivo_origen):
            return Response(
                {"mensaje": f"El archivo {nombre_archivo} no existe en el directorio base."},
                status=status.HTTP_404_NOT_FOUND,
            )
        
        # Obtener extensión y tamaño del archivo
        extension_archivo = os.path.splitext(nombre_archivo)[1]
        tamaño_archivo = os.path.getsize(ruta_archivo_origen)
        
        # Construir la ruta del directorio destino
        directorio_destino = os.path.join(settings.HISTORICOS_ROOT, suscripcion, idEmpresa, codContrato)
        
        # Crear el directorio destino si no existe
        if not os.path.exists(directorio_destino):
            try:
                os.makedirs(directorio_destino, exist_ok=True)
            except OSError as e:
                return Response(
                    {"mensaje": f"Error al crear el directorio destino: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        
        # Construir la ruta completa del archivo destino
        ruta_archivo_destino = os.path.join(directorio_destino, nombre_archivo)
        
        # Transferir el archivo
        try:
            shutil.copy2(ruta_archivo_origen, ruta_archivo_destino)
            with connection.cursor() as cursor:
                select_query = """
                    SELECT int_idTipoDocumentos
                    FROM tm_tipodocumento
                    WHERE str_idSuscripcion = %s AND str_CodTipoDocumento = 'COFI'
                """
                cursor.execute(select_query, [suscripcion])
                idTipoDocumento = cursor.fetchone()
                if not idTipoDocumento:
                    return Response(
                        {"error": "No se encontró el tipo de documento."},
                        status=status.HTTP_404_NOT_FOUND
                    )
                int_idTipoDocumento = idTipoDocumento[0]

            # Insertar registro en la base de datos
            with connection.cursor() as cursor:
                query = """
                    INSERT INTO tr_archivosdocumentos 
                    (str_idSuscriptor, int_idSolicitudes, int_idTipoDocumento, str_RutaArchivo, str_NombreArchivo, str_ExtencionArchivo, str_TamañoArchivo, dt_FechaCreacion, int_idUsuarioCreacion)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), %s)
                """
                cursor.execute(query, [
                    suscripcion,
                    int_idSolicitudes,
                    int_idTipoDocumento,
                    ruta_archivo_destino,
                    nombre_archivo,
                    extension_archivo[1:],   
                    tamaño_archivo,
                    int_idUsuarioCreacion
                ])

            return Response({"mensaje": "Archivo subido exitosamente."}, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"mensaje": f"Error al transferir el archivo: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
class DescargarArchivoHistorico(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        int_idEmpresa = request.query_params.get('int_idEmpresa')

        if not all([str_idSuscriptor, str_CodSolicitudes]):
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor', 'str_CodSolicitudes' y 'int_idEmpresa' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        directorio_base = os.path.join(settings.HISTORICOS_ROOT, str_idSuscriptor, int_idEmpresa,str_CodSolicitudes)

        if not os.path.exists(directorio_base):
            return Response({"error": "La carpeta especificada no existe."}, status=status.HTTP_404_NOT_FOUND)

        archivos_encontrados = os.listdir(directorio_base)

        if not archivos_encontrados:
            return Response({"error": "No se encontraron archivos en la carpeta especificada."}, status=status.HTTP_404_NOT_FOUND)

        archivos_encontrados.sort(key=lambda archivo: os.path.getmtime(os.path.join(directorio_base, archivo)), reverse=True)

        nombre_archivo = archivos_encontrados[0]
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)

        try:
            archivo_response = FileResponse(open(ruta_archivo, 'rb'), as_attachment=True)
            archivo_response['Content-Disposition'] = f'attachment; filename="{nombre_archivo}"'
            archivo_response['Content-Type'] = 'application/octet-stream'
            return archivo_response

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class DatosArchivoHistorico(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        int_idEmpresa = request.query_params.get('int_idEmpresa')

        # Verificación de parámetros obligatorios
        if not all([str_idSuscriptor, str_CodSolicitudes]):
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor', 'str_CodSolicitudes' y 'int_idEmpresa' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Construcción del directorio base
        directorio_base = os.path.join(settings.HISTORICOS_ROOT, str_idSuscriptor, int_idEmpresa,str_CodSolicitudes)

        # Verificación de existencia del directorio
        if not os.path.exists(directorio_base):
            return Response({"error": "La carpeta especificada no existe."}, status=status.HTTP_404_NOT_FOUND)

        # Listar archivos en el directorio
        archivos_encontrados = os.listdir(directorio_base)

        if not archivos_encontrados:
            return Response({"error": "No se encontraron archivos en la carpeta especificada."}, status=status.HTTP_404_NOT_FOUND)

        # Ordenar archivos por fecha de modificación (el más reciente primero)
        archivos_encontrados.sort(key=lambda archivo: os.path.getmtime(os.path.join(directorio_base, archivo)), reverse=True)

        # Seleccionar el último archivo
        nombre_archivo = archivos_encontrados[0]
        ruta_archivo = os.path.join(directorio_base, nombre_archivo)
        fecha_modificacion = os.path.getmtime(ruta_archivo)
        tamaño_archivo = os.path.getsize(ruta_archivo)

        # Retornar los datos del archivo como JSON
        return Response({
            "nombre_archivo": nombre_archivo,
            "ruta_archivo": ruta_archivo,
            "fecha_modificacion": fecha_modificacion,
            "tamaño_archivo": tamaño_archivo
        }, status=status.HTTP_200_OK)