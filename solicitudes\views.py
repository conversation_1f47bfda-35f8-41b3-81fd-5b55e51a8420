from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection, DatabaseError
from .serializers import SolicitudSerializer, SolicitudUpdateSerializer,AprobadorSerializer,SolicitudSerializerAdenda,SolicitudSerializerExtraJudicial,SolicitudSerializerHistorico
from datetime import datetime
from django.test import RequestFactory
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.core.mail import send_mail
from django.conf import settings
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail
from datetime import datetime, timedelta
import csv
import xml.etree.ElementTree as ET
import pandas as pd
from openpyxl import load_workbook
import locale
from tempfile import NamedTemporaryFile
import os
from rest_framework.test import APIRequestFactory
from SolicitudContenido.views import ContenidoIngresar
import zipfile
from django.http import HttpResponse,FileResponse

MONTHS_ES = {
    "January": "enero", "February": "febrero", "March": "marzo",
    "April": "abril", "May": "mayo", "June": "junio",
    "July": "julio", "August": "agosto", "September": "septiembre",
    "October": "octubre", "November": "noviembre", "December": "diciembre"
}

def format_date_es(date_obj):
    day = date_obj.day
    month = MONTHS_ES[date_obj.strftime("%B")]
    year = date_obj.year
    return f"{day:02d} de {month} de {year}"

class SolicitudesTodo(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
        SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
            s.str_idSuscriptor = %s
        GROUP BY 
            s.int_idSolicitudes
        ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]                   

                }
                for row in rows
            ]
        return Response(solicitudes)
class SolicitudesArchivadas(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = """
        SELECT 
            s.int_idSolicitudes, 
            s.int_idSolicitante, 
            CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
            s.str_idSuscriptor, 
            s.int_idEmpresa,
            s.int_idEstado, 
            es.str_Nombre AS estado_nombre,
            s.int_idUnidadNegocio, 
            un.str_Descripcion,
            s.str_DeTerceros, 
            s.str_Visible, 
            s.int_idTipoSol,
            ts.str_Nombre AS nombre_TipoSolicitud, 
            s.dt_FechaRegistro, 
            s.dt_FechaEsperada, 
            s.int_idGestor, 
            s.int_idClienteAsociado, 
            e.str_NombreEmpresa, 
            e.str_RazonSocial, 
            e.str_Ruc,
            GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
            s.str_CodSolicitudes,
            s.db_Honorarios,
            s.int_SolicitudGuardada,
            ts.str_CodTipoSol,
             MIN(stc.str_Moneda) AS moneda
        FROM 
            tr_solicitudes s
        LEFT JOIN 
            tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
        LEFT JOIN 
            tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
        LEFT JOIN 
            tm_estados es ON s.int_idEstado = es.int_idEstado 
        LEFT JOIN 
            tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
        LEFT JOIN 
            tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
        LEFT JOIN 
            tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
        LEFT JOIN 
            tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
        WHERE  
            s.str_idSuscriptor = %s
            AND s.str_Visible = 'no'
            OR t.str_descripcion IS NULL
        GROUP BY 
            s.int_idSolicitudes
        ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                   'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class SolicitudesVisibles(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
                       SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE    
                s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
            GROUP BY 
            s.int_idSolicitudes;
            ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class FiltrarSolicitudes(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        int_idUsuario = request.query_params.getlist('int_idUsuario')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_razonSocial = request.query_params.get('str_razonSocial')
        str_documentoCliente = request.query_params.get('str_documentoCliente')
        atrasadas = request.query_params.get('atrasadas')
        query = """
                SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda,
                MIN(a.dt_FechaAceptacion) AS fechaAceptacion,
                e.str_Moneda,
                e.str_SimboloMoneda,                
                CASE WHEN EXISTS (
                    SELECT 1 
                    FROM tr_aprobadores a_sub
                    WHERE a_sub.int_idSolicitudes = s.int_idSolicitudes
                    AND a_sub.int_idUsuario  = %s
                    AND a_sub.int_OrdenAprobacion IS NOT NULL
                ) THEN 'Sí' ELSE 'No' END AS tieneOrdenAprobacion,
                CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos) AS nombre_completo_gestor,
                (SELECT MAX(tr.dt_FechaCambio)
                 FROM tr_estadoregistros tr
                 WHERE tr.int_idSolicitudes = s.int_idSolicitudes) AS ultima_fecha_cambio
                FROM 
                    tr_solicitudes s
                LEFT JOIN 
                    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
                LEFT JOIN 
                    tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
                LEFT JOIN 
                    tm_estados es ON s.int_idEstado = es.int_idEstado 
                LEFT JOIN 
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
                LEFT JOIN 
                    tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
                LEFT JOIN 
                    tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
                LEFT JOIN 
                    tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
                LEFT JOIN 
                    tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes
                LEFT JOIN 
                    tr_aprobadores a ON a.int_idSolicitudes = s.int_idSolicitudes
                LEFT JOIN 
                    tm_interlocutores ti ON ti.int_idInterlocutor  = s.int_idClienteAsociado    
                WHERE  
                    s.str_idSuscriptor = %s 
                
         
        """
        params = [int_idUsuario,str_idSuscriptor]
        if str_CodSolicitudes:
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"{str_CodSolicitudes}%")
            
        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)
        
        if str_razonSocial:
            query += " AND ti.str_RazonSocial = %s"
            params.append(str_razonSocial)
            
        if str_documentoCliente:
            query += " AND ti.str_Documento = %s"
            params.append(str_documentoCliente)
                
        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)
            
        if atrasadas:
            query += """
                AND es.str_Nombre = 'Asignado'
                AND DATE(s.dt_FechaEsperada) < CURDATE()
            """
            

        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)
        # Añadir condiciones para tags
        if str_descripcion:
            # Buscar solicitudes que tengan al menos uno de los tags
            tag_conditions = []
            for tag in str_descripcion:
                tag_conditions.append(f"t.str_descripcion LIKE %s")
                params.append(f'%{tag}%')
            tag_query = " OR ".join(tag_conditions)

            # Añadir la condición de tags a la consulta principal
            query += f"""
                AND s.int_idSolicitudes IN (
                    SELECT DISTINCT t.int_idSolicitudes
                    FROM tr_tags t
                    WHERE {tag_query}
                )
            """
         
        query += " GROUP BY s.int_idSolicitudes  ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25],
                    'dt_FechaAceptacion': row[26],
                    'str_MonedaEmpresa': row[27],
                    'str_SimboloMoneda': row[28],
                    'tieneOrdenAprobacion': row[29],
                    'nombre_gestor': row[30],
                    'db_ultimaActividad': row[31]
                }
                for row in rows
            ]
        return Response(solicitudes, status=status.HTTP_200_OK)
class MatrizGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        firmados = request.query_params.get('firmado')
        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
                SELECT 
                    s.str_CodSolicitudes, 
                    MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
                     MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(sc.db_Presupuesto, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(sc.db_Presupuesto, 2))
    END
    ) AS db_Presupuesto,
                    MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
                    MAX(un.str_Descripcion) AS str_Descripcion,
                    MAX(es.str_Nombre) AS estado_nombre,
                    MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
                    MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
                    MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
                    MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
                    MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
                    MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
                    MAX(sc.str_Garantia) AS str_Garantia,
                    MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(s.db_Honorarios, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(s.db_Honorarios, 2))
    END
    ) AS db_Honorarios,
                    MAX(sc.str_Margen) AS str_Margen,
                    MAX(sc.str_FormaPago) AS str_FormaPago,
                    MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
                    MAX(s.int_HorasTrabajadas) AS int_HorasTrabajadas,
                    MAX(s.int_idSolicitudes) AS int_idSolicitudes,
                    MAX(s.dt_FechaFin) AS dt_FechaFin,
                    MAX(
                        CASE 
                            WHEN sc.str_Moneda = 'dolares' THEN 'Dolares'
                            ELSE e.str_Moneda
                        END
                    ) AS str_Moneda,
                    MAX(sc.str_Penalidades) AS str_Penalidades,
                    MAX(CASE 
                        WHEN clale.str_Nombre = 'Cláusula de no competencia' THEN 'Sí'
                        ELSE NULL
                    END) AS Clausula
                FROM 
                    tr_solicitudes s
                LEFT JOIN 
                    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
                LEFT JOIN 
                    tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
                LEFT JOIN 
                    tm_estados es ON s.int_idEstado = es.int_idEstado 
                LEFT JOIN 
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
                LEFT JOIN 
                    tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
                LEFT JOIN 
                    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
                LEFT JOIN 
                    tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
                LEFT JOIN 
                    tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
                LEFT JOIN 
                    tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
                LEFT JOIN 
                    tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
                LEFT JOIN 
                    tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
                WHERE  
                    s.str_idSuscriptor = %s
                
                """
        

        params = [ str_idSuscriptor]
        
        if firmados == "si":
            query += ' AND es.str_Nombre = "Firmado" '
        elif firmados == "no":
            query += ' AND es.str_Nombre != "Firmado" '
        else:
            query += ''  
            
        if año_filtro and str_CodSolicitudes:
    
            query += """
                AND (
                    (s.str_CodSolicitudes = %s AND YEAR(s.dt_FechaRegistro) = %s)
                    OR s.str_CodSolicitudes LIKE %s
                )
            """
            params.extend([str_CodSolicitudes, año_filtro, f"%{str_CodSolicitudes}%"])
        elif año_filtro:
          
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(año_filtro)
        elif str_CodSolicitudes:
            
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"%{str_CodSolicitudes}%")

      
        if str_CodSolicitudes:
            query += "GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro  ORDER BY s.dt_FechaRegistro ASC;"
        else:
            query += " GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro ORDER BY s.dt_FechaRegistro DESC ;"


        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                    'Moneda': row[20],
                    'Penalidades': row[21],
                    'Clausula de no competencia': row[22],
                }
                for row in rows
            ]
        return Response(solicitudes)

class AsignarGestor(APIView):
    def put(self, request):
        int_idSolicitudes  = request.data.get('int_idSolicitudes')        
        int_idGestor = request.data.get('int_idGestor')
        int_idUsuarioModificacion = request.data.get('int_idUsuarioModificacion')
        fecha_actual = datetime.now()

        try:
            with connection.cursor() as cursor:

                update_query = """
                    UPDATE tr_solicitudes 
                    SET int_idGestor = %s, int_idUsuarioModificacion = %s, dt_FechaModificacion = %s
                    WHERE int_idSolicitudes = %s
                """
                update_params = [int_idGestor, int_idUsuarioModificacion, fecha_actual, int_idSolicitudes ]

            with connection.cursor() as cursor:
                cursor.execute(update_query, update_params)
                if cursor.rowcount == 0:
                    return Response(
                        {"error": "No se encontró la solicitud con el ID proporcionado."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            return Response(
                {"mensaje": "El Gestor fue asignado a la solicitud."},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class ContadorPorGestor(APIView):
    def get(self, request):
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        if int_idGestor is None:
            return Response(
                {"error": "El campo 'int_idGestor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            estados_base = {
                "Asignado": 0,
                "En Proceso": 0,
                "Aprobadas": 0,
                "Firmado": 0,
                "Aprobado": 0,
                "En Aprobacion": 0,
                "En Validacion": 0,
                "Aceptado": 0,
            }
            total_no_firmado = 0  # Total de solicitudes no firmadas
            total_horas_trabajadas = 0  # Total de horas trabajadas (sin firmadas)
            total_horas_todas = 0  # Total de horas trabajadas (todas)
            total_atrasadas = 0  # Total de solicitudes atrasadas

            # Primera consulta: solicitudes por estado (excluyendo 'Firmado')
            with connection.cursor() as cursor:
                query = """
                    SELECT estados.str_Nombre, COUNT(*) as cantidad, SUM(solicitudes.int_HorasTrabajadas) as total_horas
                    FROM tr_solicitudes AS solicitudes
                    INNER JOIN tm_estados AS estados
                        ON solicitudes.int_idEstado = estados.int_idEstado
                    WHERE solicitudes.int_idGestor = %s AND solicitudes.str_idSuscriptor = %s
                      AND estados.str_Nombre != 'Firmado'
                    GROUP BY estados.str_Nombre
                """
                cursor.execute(query, [int_idGestor, str_idSuscriptor])
                rows = cursor.fetchall()

            for estado, cantidad, total_horas in rows:
                if estado in estados_base:
                    estados_base[estado] = cantidad
                total_no_firmado += cantidad
                total_horas_trabajadas += total_horas if total_horas else 0

            # Segunda consulta: total de horas trabajadas (todas las solicitudes)
            with connection.cursor() as cursor:
                query_horas_todas = """
                    SELECT SUM(solicitudes.int_HorasTrabajadas) as total_horas
                    FROM tr_solicitudes AS solicitudes
                    WHERE solicitudes.int_idGestor = %s AND solicitudes.str_idSuscriptor = %s
                """
                cursor.execute(query_horas_todas, [int_idGestor, str_idSuscriptor])
                total_horas_todas_row = cursor.fetchone()
                total_horas_todas = total_horas_todas_row[0] if total_horas_todas_row[0] else 0

            # Tercera consulta: solicitudes atrasadas (estado 'Asignado' y fecha menor a hoy)
            with connection.cursor() as cursor:
                query_atrasadas = """
                    SELECT COUNT(*)
                    FROM tr_solicitudes AS solicitudes
                    INNER JOIN tm_estados AS estados
                        ON solicitudes.int_idEstado = estados.int_idEstado
                    WHERE solicitudes.int_idGestor = %s AND solicitudes.str_idSuscriptor = %s
                      AND estados.str_Nombre = 'Asignado'
                      AND DATE(solicitudes.dt_FechaRegistro) < CURDATE()
                """
                cursor.execute(query_atrasadas, [int_idGestor, str_idSuscriptor])
                total_atrasadas_row = cursor.fetchone()
                total_atrasadas = total_atrasadas_row[0] if total_atrasadas_row[0] else 0

            # Respuesta consolidada
            return Response(
                {
                    "int_idGestor": int_idGestor,
                    "solicitudes_por_estado": estados_base,
                    "total_no_firmado": total_no_firmado,
                    "total_horas_trabajadas_sin_firmado": total_horas_trabajadas,
                    "total_horas_trabajadas_con_firmado": total_horas_todas,
                    "total_atrasadas": total_atrasadas,  # Incluye las atrasadas en la respuesta
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"error": f"Ocurrió un error al obtener los datos: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
class ContadorPorGestorAnio(APIView):
    def get(self, request):
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        anio = request.query_params.get('anio')
        
        if int_idGestor is None:
            return Response(
                {"error": "El campo 'int_idGestor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if anio is None:
            return Response(
                {"error": "El campo 'anio' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Definir los estados posibles
            estados_base = {
                "Asignado": 0,
                "En Proceso": 0,
                "Aprobadas": 0,
                "Firmado": 0,
                "Aprobado": 0,
                "En Aprobacion": 0,
                "En Validacion": 0,
                "Aceptado": 0,
            }
            total_no_firmado = 0  # Total de solicitudes no firmadas
            total_horas_trabajadas = 0  # Total de horas trabajadas (sin firmadas)
            total_horas_todas = 0  # Total de horas trabajadas (todas)
            total_atrasadas = 0  # Total de solicitudes atrasadas

            # Primera consulta: solicitudes por estado (excluyendo 'Firmado') para el año
            with connection.cursor() as cursor:
                query = """
                    SELECT estados.str_Nombre, COUNT(*) as cantidad, SUM(solicitudes.int_HorasTrabajadas) as total_horas
                    FROM tr_solicitudes AS solicitudes
                    INNER JOIN tm_estados AS estados
                        ON solicitudes.int_idEstado = estados.int_idEstado
                    WHERE solicitudes.int_idGestor = %s 
                        AND solicitudes.str_idSuscriptor = %s
                        AND YEAR(solicitudes.dt_FechaRegistro) = %s
                        AND estados.str_Nombre != 'Firmado'
                    GROUP BY estados.str_Nombre
                """
                cursor.execute(query, [int_idGestor, str_idSuscriptor, anio])
                rows = cursor.fetchall()

            for estado, cantidad, total_horas in rows:
                if estado in estados_base:
                    estados_base[estado] = cantidad
                total_no_firmado += cantidad
                total_horas_trabajadas += total_horas if total_horas else 0

            # Segunda consulta: total de horas trabajadas (todas las solicitudes) para el año
            with connection.cursor() as cursor:
                query_horas_todas = """
                    SELECT SUM(solicitudes.int_HorasTrabajadas) as total_horas
                    FROM tr_solicitudes AS solicitudes
                    WHERE solicitudes.int_idGestor = %s
                        AND solicitudes.str_idSuscriptor = %s
                        AND YEAR(solicitudes.dt_FechaRegistro) = %s
                """
                cursor.execute(query_horas_todas, [int_idGestor, str_idSuscriptor, anio])
                total_horas_todas_row = cursor.fetchone()
                total_horas_todas = total_horas_todas_row[0] if total_horas_todas_row[0] else 0

            # Tercera consulta: solicitudes atrasadas (estado 'Asignado' y fecha menor a hoy)
            with connection.cursor() as cursor:
                query_atrasadas = """
                    SELECT COUNT(*)
                    FROM tr_solicitudes AS solicitudes
                    INNER JOIN tm_estados AS estados
                        ON solicitudes.int_idEstado = estados.int_idEstado
                    WHERE solicitudes.int_idGestor = %s
                        AND solicitudes.str_idSuscriptor = %s
                        AND YEAR(solicitudes.dt_FechaRegistro) = %s
                        AND estados.str_Nombre = 'Asignado'
                        AND DATE(solicitudes.dt_FechaRegistro) < CURDATE()
                """
                cursor.execute(query_atrasadas, [int_idGestor, str_idSuscriptor, anio])
                total_atrasadas_row = cursor.fetchone()
                total_atrasadas = total_atrasadas_row[0] if total_atrasadas_row[0] else 0

            # Respuesta consolidada
            return Response(
                {
                    "int_idGestor": int_idGestor,
                    "solicitudes_por_estado": estados_base,
                    "total_no_firmado": total_no_firmado,
                    "total_horas_trabajadas_sin_firmado": total_horas_trabajadas,
                    "total_horas_trabajadas_con_firmado": total_horas_todas,
                    "total_atrasadas": total_atrasadas,  # Incluye las atrasadas en la respuesta
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"error": f"Ocurrió un error al obtener los datos: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
class FirmarContrato(APIView):
    def put(self, request):
        int_idSolicitudes  = request.data.get('int_idSolicitudes')        
        dt_FirmaContrato = request.data.get('dt_FirmaContrato')
        dt_FechaFin = request.data.get('dt_FechaFin')
        int_HorasTrabajadas = request.data.get('int_HorasTrabajadas')
        int_idUsuarioModificacion = request.data.get('int_idUsuarioModificacion')
        fecha_actual = datetime.now()

        try:
            with connection.cursor() as cursor:

                update_query = """
                    UPDATE tr_solicitudes 
                    SET dt_FirmaContrato = %s,int_HorasTrabajadas = %s, int_idUsuarioModificacion = %s, dt_FechaModificacion = %s,dt_FechaFin=%s
                    WHERE int_idSolicitudes = %s
                """
                update_params = [dt_FirmaContrato,int_HorasTrabajadas, int_idUsuarioModificacion, fecha_actual,dt_FechaFin, int_idSolicitudes ]

            with connection.cursor() as cursor:
                cursor.execute(update_query, update_params)
                if cursor.rowcount == 0:
                    return Response(
                        {"error": "No se encontró la solicitud con el ID proporcionado."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            return Response(
                {"mensaje": "El contrato fue subido correctamente."},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class EditarEstadoSolicitud(APIView):
    def put(self, request):
        int_idSolicitudes  = request.data.get('int_idSolicitudes')
        nombre_estado = request.data.get('nombre_estado')
        int_idUsuarioCreacion = request.data.get('int_idUsuarioCreacion')
        str_idSuscriptor = request.data.get('str_idSuscriptor')
        fecha_actual = datetime.now()

        try:
            with connection.cursor() as cursor:
                select_query = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_idSuscripcion  = %s AND str_Nombre = %s
                """
                cursor.execute(select_query, [str_idSuscriptor, nombre_estado])
                estado = cursor.fetchone()
                if not estado:
                    return Response(
                        {"error": "No se encontró un estado con el 'nombre_estado' y 'str_idSuscriptor' proporcionados."},
                        status=status.HTTP_404_NOT_FOUND
                    )
                else:
                        estado[0],

                
                str_idEstado = estado[0]

            # Realiza el UPDATE para cambiar el estado de la solicitud
            update_query = """
                UPDATE tr_solicitudes 
                SET int_idEstado = %s, int_idUsuarioModificacion = %s, dt_FechaModificacion = %s
                WHERE int_idSolicitudes = %s
            """
            update_params = [str_idEstado, int_idUsuarioCreacion, fecha_actual, int_idSolicitudes ]

            with connection.cursor() as cursor:
                cursor.execute(update_query, update_params)
                if cursor.rowcount == 0:
                    return Response(
                        {"error": "No se encontró la solicitud con el ID proporcionado."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            # Registra el cambio de estado en la tabla tr_estadoregistros
            insert_query = """
                INSERT INTO tr_estadoregistros (int_idSolicitudes,str_idSuscriptor  , int_idEstado, dt_FechaCambio, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s,%s, %s, %s, %s)
            """
            insert_params = [int_idSolicitudes ,str_idSuscriptor, str_idEstado, fecha_actual, fecha_actual, int_idUsuarioCreacion]

            with connection.cursor() as cursor:
                cursor.execute(insert_query, insert_params)

            return Response(
                {"mensaje": "El estado de la solicitud se ha actualizado correctamente y el cambio ha sido registrado."},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class HistorialEstadosSolicitud(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitudes = request.query_params.get('int_idSolicitudes')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT
                er.int_idEstadoRegistros,
                er.str_idSuscriptor,
                er.int_idSolicitudes,
                s.int_idSolicitante,
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                er.int_idEstado,
                e.str_Nombre,
                er.dt_FechaCambio
            FROM
                tr_estadoregistros er
            INNER JOIN 
                tm_estados e ON er.int_idEstado = e.int_idEstado
            INNER JOIN 
                tr_solicitudes s ON er.int_idSolicitudes = s.int_idSolicitudes
             INNER JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios
            WHERE
                er.str_idSuscriptor = %s && er.int_idSolicitudes = %s
            ORDER BY 
               s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor,int_idSolicitudes]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idEstadoRegistros': row[0],
                    'str_idSuscriptor': row[1],
                    'int_idSolicitudes': row[2],
                    'Solicitante': row[3],
                    'Nombres_solicitante': row[4],
                    'Estado': row[5],
                    'Nombre_estado': row[6],
                    'fecha_Cambio': row[7],

                    
                }
                for row in rows
            ]
        return Response(solicitudes)
class ArchivarSolicitud(APIView):
    def put(self, request):
        int_idSolicitudes = request.data.get('int_idSolicitudes')
        int_idUsuarioModificacion  = request.data.get('int_idUsuarioModificacion ')
        fecha_actual = datetime.now()

        if not int_idSolicitudes :
            return Response(
                {"error": "Los campos 'int_idSolicitudes' y 'str_idEstado' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        update_query = """
            UPDATE tr_solicitudes 
            SET str_Visible = %s,int_idUsuarioModificacion = %s,dt_FechaModificacion = %s
            WHERE int_idSolicitudes = %s
        """
        update_params = ["no", int_idUsuarioModificacion,fecha_actual,int_idSolicitudes]

        with connection.cursor() as cursor:
            cursor.execute(update_query, update_params)
            if cursor.rowcount == 0:
                return Response(
                    {"error": "No se encontró la solicitud con el ID proporcionado."},
                    status=status.HTTP_404_NOT_FOUND
                )


        return Response(
            {"mensaje": "El estado de la solicitud se ha actualizado correctamente y el cambio ha sido registrado."},
            status=status.HTTP_200_OK
        )
class SolicitudesGestor(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.int_idGestor = %s
                && s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
                
        """
        params = [int_idGestor,str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                     'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]

                }
                for row in rows
            ]
        return Response(solicitudes)

class MatrizGestor(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')
        año_filtro = request.query_params.get('year')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        firmados = request.query_params.get('firmado')
        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
SELECT 
    s.str_CodSolicitudes, 
    MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
    MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(sc.db_Presupuesto, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(sc.db_Presupuesto, 2))
    END
    ) AS db_Presupuesto,
    MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
    MAX(un.str_Descripcion) AS str_Descripcion,
    MAX(es.str_Nombre) AS estado_nombre,
    MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
    MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
    MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
    MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
    MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
    MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
    MAX(sc.str_Garantia) AS str_Garantia,
    MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(s.db_Honorarios, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(s.db_Honorarios, 2))
    END
    ) AS db_Honorarios,
    MAX(sc.str_Margen) AS str_Margen,
    MAX(sc.str_FormaPago) AS str_FormaPago,
    MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
    MAX(s.int_HorasTrabajadas) AS int_HorasTrabajadas,
    MAX(s.int_idSolicitudes) AS int_idSolicitudes,
    MAX(s.dt_FechaFin) AS dt_FechaFin,
    MAX(
                        CASE 
                            WHEN sc.str_Moneda = 'dolares' THEN 'Dolares'
                            ELSE e.str_Moneda
                        END
                    ) AS str_Moneda,
    MAX(sc.str_Penalidades) AS str_Penalidades,
    MAX(CASE 
        WHEN clale.str_Nombre = 'Clausula de no competencia' THEN 'Sí'
        ELSE NULL
    END) AS Clausula
FROM 
    tr_solicitudes s
LEFT JOIN 
    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
LEFT JOIN 
    tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
LEFT JOIN 
    tm_estados es ON s.int_idEstado = es.int_idEstado 
LEFT JOIN 
    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
LEFT JOIN 
    tm_usuarios ug ON s.int_idSolicitante = ug.int_idUsuarios 
LEFT JOIN 
    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
LEFT JOIN 
    tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
LEFT JOIN 
    tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
LEFT JOIN 
    tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
LEFT JOIN 
    tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
LEFT JOIN 
    tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
WHERE  
    s.int_idGestor = %s
    AND s.str_idSuscriptor = %s

"""
        

        params = [int_idGestor, str_idSuscriptor]

        if firmados == "si":
            query += ' AND es.str_Nombre = "Firmado" '
        elif firmados == "no":
            query += ' AND es.str_Nombre != "Firmado" '
        else:
            query += '' 
            
        if año_filtro and str_CodSolicitudes:
    
            query += """
                AND (
                    (s.str_CodSolicitudes = %s AND YEAR(s.dt_FechaRegistro) = %s)
                    OR s.str_CodSolicitudes LIKE %s
                )
            """
            params.extend([str_CodSolicitudes, año_filtro, f"%{str_CodSolicitudes}%"])
        elif año_filtro:
          
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(año_filtro)
        elif str_CodSolicitudes:
            
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"%{str_CodSolicitudes}%")

      
        if str_CodSolicitudes:
            query += "GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro  ORDER BY s.dt_FechaRegistro ASC;"
        else:
            query += " GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro ORDER BY s.dt_FechaRegistro DESC ;"


        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Solicitante': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                    'Moneda': row[20],
                    'Penalidades': row[21],
                    'Clausula de no competencia': row[22],
                }
                for row in rows
            ]
        return Response(solicitudes)
class MatrizSolicitante(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        año_filtro = request.query_params.get('year')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        firmados = request.query_params.get('firmado')
        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
SELECT 
    s.str_CodSolicitudes, 
    MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
    MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(sc.db_Presupuesto, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(sc.db_Presupuesto, 2))
    END
    ) AS db_Presupuesto,
    MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
    MAX(un.str_Descripcion) AS str_Descripcion,
    MAX(es.str_Nombre) AS estado_nombre,
    MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
    MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
    MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
    MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
    MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
    MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
    MAX(sc.str_Garantia) AS str_Garantia,
    MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(s.db_Honorarios, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(s.db_Honorarios, 2))
    END
    ) AS db_Honorarios,
    MAX(sc.str_Margen) AS str_Margen,
    MAX(sc.str_FormaPago) AS str_FormaPago,
    MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
    MAX(s.int_HorasTrabajadas) AS int_HorasTrabajadas,
    MAX(s.int_idSolicitudes) AS int_idSolicitudes,
    MAX(s.dt_FechaFin) AS dt_FechaFin,
    MAX(
                        CASE 
                            WHEN sc.str_Moneda = 'dolares' THEN 'Dolares'
                            ELSE e.str_Moneda
                        END
                    ) AS str_Moneda,
    MAX(sc.str_Penalidades) AS str_Penalidades,
    MAX(CASE 
        WHEN clale.str_Nombre = 'Clausula de no competencia' THEN 'Sí'
        ELSE NULL
    END) AS Clausula
FROM 
    tr_solicitudes s
LEFT JOIN 
    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
LEFT JOIN 
    tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
LEFT JOIN 
    tm_estados es ON s.int_idEstado = es.int_idEstado 
LEFT JOIN 
    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
LEFT JOIN 
    tm_usuarios ug ON s.int_idSolicitante = ug.int_idUsuarios 
LEFT JOIN 
    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
LEFT JOIN 
    tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
LEFT JOIN 
    tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
LEFT JOIN 
    tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
LEFT JOIN 
    tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
LEFT JOIN 
    tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
WHERE  
    s.int_idSolicitante = %s
    AND s.str_idSuscriptor = %s

"""
        

        params = [int_idSolicitante, str_idSuscriptor]

        if firmados == "si":
            query += ' AND es.str_Nombre = "Firmado" '
        elif firmados == "no":
            query += ' AND es.str_Nombre != "Firmado" '
        else:
            query += '' 
            
        if año_filtro and str_CodSolicitudes:
    
            query += """
                AND (
                    (s.str_CodSolicitudes = %s AND YEAR(s.dt_FechaRegistro) = %s)
                    OR s.str_CodSolicitudes LIKE %s
                )
            """
            params.extend([str_CodSolicitudes, año_filtro, f"%{str_CodSolicitudes}%"])
        elif año_filtro:
          
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(año_filtro)
        elif str_CodSolicitudes:
            
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"%{str_CodSolicitudes}%")

      
        if str_CodSolicitudes:
            query += "GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro  ORDER BY s.dt_FechaRegistro ASC;"
        else:
            query += " GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro ORDER BY s.dt_FechaRegistro DESC ;"


        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Solicitante': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                    'Moneda': row[20],
                    'Penalidades': row[21],
                    'Clausula de no competencia': row[22],
                }
                for row in rows
            ]
        return Response(solicitudes)

class FiltrarSolicitudesGestor(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        int_idGestor = request.query_params.get('int_idGestor')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_razonSocial = request.query_params.get('str_razonSocial')
        str_documentoCliente = request.query_params.get('str_documentoCliente')
        # Consulta base
        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda,
                e.str_Moneda,
                e.str_SimboloMoneda,
                (SELECT MAX(tr.dt_FechaCambio)
               FROM tr_estadoregistros tr
               WHERE tr.int_idSolicitudes = s.int_idSolicitudes) AS ultima_fecha_cambio
            FROM 
                tr_solicitudes s
            LEFT JOIN  
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tm_interlocutores ti ON ti.int_idInterlocutor  = s.int_idClienteAsociado  
            WHERE  
                s.str_idSuscriptor = %s 
                AND s.int_idGestor = %s
        """
        params = [str_idSuscriptor, int_idGestor]

        # Añadir condiciones de filtrado
        if str_CodSolicitudes:
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"{str_CodSolicitudes}%")
        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)

        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)
            
        if str_razonSocial:
            query += " AND ti.str_RazonSocial = %s"
            params.append(str_razonSocial)
            
        if str_documentoCliente:
            query += " AND ti.str_Documento = %s"
            params.append(str_documentoCliente)
                
        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)

        # Añadir condiciones para tags
        if str_descripcion:
            # Buscar solicitudes que tengan al menos uno de los tags
            tag_conditions = []
            for tag in str_descripcion:
                tag_conditions.append(f"t.str_descripcion LIKE %s")
                params.append(f'%{tag}%')
            tag_query = " OR ".join(tag_conditions)

            # Añadir la condición de tags a la consulta principal
            query += f"""
                AND s.int_idSolicitudes IN (
                    SELECT DISTINCT t.int_idSolicitudes
                    FROM tr_tags t
                    WHERE {tag_query}
                )
            """

        # Agrupar resultados
        query += " GROUP BY s.int_idSolicitudes ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25],
                    'str_MonedaEmpresa': row[26],
                    'str_SimboloMoneda': row[27],
                    'db_ultimaActividad': row[28]
                }
                for row in rows
            ]

        return Response(solicitudes, status=status.HTTP_200_OK)
class SolicitudesController(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                 s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
                AND s.int_idGestor = %s
                OR es.str_Nombre = "Nuevo"
                OR es.str_Nombre = "Asignado"
            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
                
        """
        params = [str_idSuscriptor,int_idGestor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                      'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class FiltrarSolicitudesController(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        int_idGestor = request.query_params.get('int_idGestor')
        isController = request.query_params.get('isController')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_razonSocial = request.query_params.get('str_razonSocial')
        str_documentoCliente = request.query_params.get('str_documentoCliente')
        query = """
           SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda,
                (SELECT MAX(tr.dt_FechaCambio)
                 FROM tr_estadoregistros tr
                 WHERE tr.int_idSolicitudes = s.int_idSolicitudes) AS ultima_fecha_cambio,
                e.str_Moneda,
                e.str_SimboloMoneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tm_interlocutores ti ON ti.int_idInterlocutor  = s.int_idClienteAsociado  
            WHERE  
                s.str_idSuscriptor = %s 
        """
        params = [str_idSuscriptor]
        
        if str_CodSolicitudes:
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"{str_CodSolicitudes}%")
        
        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)
            
        if isController:
          query += ' AND es.str_Nombre = "Nuevo" OR es.str_Nombre = "Asignado" '
        else:
          query += ' AND s.int_idGestor = %s'
          params.append(int_idGestor)
        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)
            
        if str_razonSocial:
            query += " AND ti.str_RazonSocial = %s"
            params.append(str_razonSocial)
            
        if str_documentoCliente:
            query += " AND ti.str_Documento = %s"
            params.append(str_documentoCliente)
                
        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)

        if str_descripcion:
            query += " AND (t.str_descripcion IN %s OR t.str_descripcion IS NULL)"
            params.append(tuple(str_descripcion))

        query += " GROUP BY s.int_idSolicitudes ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                      'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25],
                    'db_ultimaActividad': row[26],
                    'str_MonedaEmpresa': row[27],
                    'str_SimboloMoneda': row[28]
                }
                for row in rows
            ]
        return Response(solicitudes, status=status.HTTP_200_OK)
class SolicitudesSolicitante(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitante = request.query_params.get('int_idSolicitante')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.int_idSolicitante = %s
                && s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
        """
        params = [int_idSolicitante,str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class FiltrarSolicitudesSolicitante(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_razonSocial = request.query_params.get('str_razonSocial')
        str_documentoCliente = request.query_params.get('str_documentoCliente')
        # Consulta base
        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda,
                e.str_Moneda,
                e.str_SimboloMoneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN  
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes
            LEFT JOIN 
                tm_interlocutores ti ON ti.int_idInterlocutor  = s.int_idClienteAsociado   
            WHERE  
                s.str_idSuscriptor = %s 
                AND s.int_idSolicitante = %s
        """
        params = [str_idSuscriptor, int_idSolicitante]

        # Añadir condiciones de filtrado
        if str_CodSolicitudes:
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"{str_CodSolicitudes}%")
            
        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)
            
        if str_razonSocial:
            query += " AND ti.str_RazonSocial = %s"
            params.append(str_razonSocial)
            
        if str_documentoCliente:
            query += " AND ti.str_Documento = %s"
            params.append(str_documentoCliente)
                
        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)

        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)

        # Añadir condiciones para tags
        if str_descripcion:
            # Buscar solicitudes que tengan al menos uno de los tags
            tag_conditions = []
            for tag in str_descripcion:
                tag_conditions.append(f"t.str_descripcion LIKE %s")
                params.append(f'%{tag}%')
            tag_query = " OR ".join(tag_conditions)

            # Añadir la condición de tags a la consulta principal
            query += f"""
                AND s.int_idSolicitudes IN (
                    SELECT DISTINCT t.int_idSolicitudes
                    FROM tr_tags t
                    WHERE {tag_query}
                )
            """

        # Agrupar resultados
        query += " GROUP BY s.int_idSolicitudes ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25],
                    'str_MonedaEmpresa': row[26],
                    'str_SimboloMoneda': row[27]
                }
                for row in rows
            ]

        return Response(solicitudes, status=status.HTTP_200_OK)
class SolicitudesAprobador(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idAprobador = request.query_params.get('int_idAprobador')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
        SELECT 
            s.int_idSolicitudes,  
            s.int_idSolicitante, 
            CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
            s.str_idSuscriptor, 
            s.int_idEmpresa,
            s.int_idEstado, 
            es.str_Nombre AS estado_nombre,
            s.int_idUnidadNegocio, 
            un.str_Descripcion,
            s.str_DeTerceros, 
            s.str_Visible, 
            s.int_idTipoSol,
            ts.str_Nombre AS nombre_TipoSolicitud, 
            s.dt_FechaRegistro, 
            s.dt_FechaEsperada, 
            s.int_idGestor, 
            s.int_idClienteAsociado, 
            e.str_NombreEmpresa, 
            e.str_RazonSocial, 
            e.str_Ruc,
            GROUP_CONCAT(DISTINCT t.str_descripcion SEPARATOR ', ') AS tags, -- Asegurando que los tags no se repitan
            s.str_CodSolicitudes,
            s.db_Honorarios,
            s.int_SolicitudGuardada,
            ts.str_CodTipoSol,
            MIN(stc.str_Moneda) AS moneda,
            MIN(a.dt_FechaAceptacion) AS fechaAceptacion,
            e.str_Moneda,
            e.str_SimboloMoneda
        FROM 
            tr_solicitudes s
        LEFT JOIN 
            tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
        LEFT JOIN 
            tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
        LEFT JOIN  
            tm_estados es ON s.int_idEstado = es.int_idEstado 
        LEFT JOIN 
            tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
        LEFT JOIN 
            tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
        LEFT JOIN 
            tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
        LEFT JOIN 
            tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
        LEFT JOIN 
            tr_aprobadores a ON a.int_idSolicitudes = s.int_idSolicitudes   
        WHERE    
            a.int_idUsuario = %s
            AND s.str_idSuscriptor = %s
            AND a.int_NumBloque = (
                SELECT MAX(int_NumBloque)
                FROM tr_aprobadores
                WHERE int_idSolicitudes = s.int_idSolicitudes
            )
            AND es.str_Nombre = "En Aprobacion"
            AND s.str_Visible = 'si'
        GROUP BY 
            s.int_idSolicitudes
        ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [int_idAprobador,str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25],
                    'dt_FechaAceptacion': row[26],
                    'str_MonedaEmpresa': row[27],
                    'str_SimboloMoneda': row[28]
                }
                for row in rows
            ]
        return Response(solicitudes)

class SolicitudesCreate(APIView):
    def post(self, request):
        serializer = SolicitudSerializer(data=request.data)
        if serializer.is_valid():
            int_idTipoSolicitud = serializer.validated_data['int_idTipoSol']
            str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
            int_idUsuarioCreacion=serializer.validated_data['int_idUsuarioCreacion'],

            fecha_actual = datetime.now().strftime("%Y%m")  
            try:
                with connection.cursor() as cursor:
                    select_query = """
                    SELECT str_CorrelativoTipoSolicitud 
                    FROM tr_correlativosolicitud 
                    WHERE int_idTipoSolicitud = %s AND str_idSuscriptor = %s 
                    """
                    cursor.execute(select_query, [int_idTipoSolicitud, str_idSuscriptor])
                    resultado = cursor.fetchone()
                    print(resultado)
                    if resultado:
                        correlativo_actual = int(resultado[0])
                        nuevo_correlativo = correlativo_actual + 1
                    else:
                        nuevo_correlativo = 1

                    nuevo_correlativo_str = f"{nuevo_correlativo:03d}" 
                    print(nuevo_correlativo_str)
                    if resultado:
                        update_query = """
                        UPDATE tr_correlativosolicitud 
                        SET str_CorrelativoTipoSolicitud = %s , dt_FechaModificacion = NOW() , int_idUsuarioModificacion =%s
                        WHERE int_idTipoSolicitud = %s AND str_idSuscriptor = %s 
                        """
                        cursor.execute(update_query, [nuevo_correlativo_str,int_idUsuarioCreacion, int_idTipoSolicitud, str_idSuscriptor])
                    else:
                        insert_query = """
                        INSERT INTO tr_correlativosolicitud (int_idCorrelativo, str_idSuscriptor, int_idTipoSolicitud, str_CorrelativoTipoSolicitud, dt_FechaCreacion) 
                        VALUES (DEFAULT, %s, %s, %s, NOW())
                        """
                        cursor.execute(insert_query, [str_idSuscriptor, int_idTipoSolicitud, nuevo_correlativo_str])
                    select_queryTS = """
                    SELECT str_CodTipoSol 
                    FROM tm_tiposolicitud 
                    WHERE int_idTipoSolicitud = %s AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryTS, [int_idTipoSolicitud, str_idSuscriptor])
                    resultadoTS = cursor.fetchone()
                    if not resultadoTS:
                        return Response({"error": "Tipo de solicitud no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    codTipoSolicitud = resultadoTS[0]
                    str_CodSolicitudes = f"{codTipoSolicitud}-{fecha_actual}{nuevo_correlativo_str}"
                    select_queryES = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_Nombre = "Nuevo" AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryES, [str_idSuscriptor])
                    resultadoES = cursor.fetchone()
                    if not resultadoES:
                        return Response({"error": "Estado no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    Id_estado = resultadoES[0]
                    insert_solicitud_query = """
                    INSERT INTO tr_solicitudes 
                    (str_CodSolicitudes, int_idSolicitante, str_idSuscriptor, int_idEmpresa ,db_Honorarios, int_idEstado, int_idUnidadNegocio,int_SolicitudGuardada , 
                    str_DeTerceros, str_Visible, int_idTipoSol, dt_FechaRegistro, dt_FechaEsperada, dt_FechaCreacion, int_idUsuarioCreacion,int_idClienteAsociado) 
                    VALUES (%s, %s, %s, %s, %s, %s,%s, %s,%s, %s, %s,NOW(),  %s, NOW(), %s, %s)
                    """
                    int_idSolicitante = serializer.validated_data.get('int_idSolicitante', None)
                    int_idEmpresa = serializer.validated_data.get('int_idEmpresa', None)
                    db_Honorarios = serializer.validated_data.get('db_Honorarios', None)
                    int_idUnidadNegocio = serializer.validated_data.get('int_idUnidadNegocio', None)
                    int_SolicitudGuardada = serializer.validated_data.get('int_SolicitudGuardada', "no")
                    str_DeTerceros = serializer.validated_data.get('str_DeTerceros', None)
                    dt_FechaEsperada = serializer.validated_data.get('dt_FechaEsperada', None)
                    int_idClienteAsociado  = serializer.validated_data.get('int_idClienteAsociado', None)

                    cursor.execute(insert_solicitud_query, [
                        str_CodSolicitudes,
                       int_idSolicitante,
                        str_idSuscriptor,
                        int_idEmpresa,
                        db_Honorarios,
                        Id_estado,
                        int_idUnidadNegocio,
                        int_SolicitudGuardada,
                        str_DeTerceros,
                        "si",
                        int_idTipoSolicitud,
                        dt_FechaEsperada,
                        int_idUsuarioCreacion,
                        int_idClienteAsociado
                        
                    ])
                    solicitud_id = cursor.lastrowid
                    
                return Response({
                    "data": serializer.data,
                    "int_idSolicitudes": solicitud_id,
                    "str_CodSolicitudes": str_CodSolicitudes
                }, status=status.HTTP_201_CREATED)
            except DatabaseError as e:
                return Response({"error": "Error de base de datos: " + str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
class UploadExcelAndCreateRequests(APIView):
    def post(self, request):
        try:
            str_idSuscriptor = request.data.get('str_idSuscriptor')
            idUsuario = request.data.get('int_idUsuario')

            excel_file = request.FILES.get('file')
            if not excel_file:
                return Response({"error": "No se proporcionó un archivo Excel."}, status=status.HTTP_400_BAD_REQUEST)

            with NamedTemporaryFile(delete=False, suffix=".xlsx") as temp_file:
                for chunk in excel_file.chunks():
                    temp_file.write(chunk)
                temp_file_path = temp_file.name  

            wb = load_workbook(temp_file_path)
            sheet = wb.active  
            headers = [cell.value for cell in sheet[1]] 

            required_columns = [
                "Nom_tipoSol",  "Nom_empresa", "Honorarios",
                "Nom_unidadNegocio", "De Terceros",  "Fecha Esperada (aaaa-mm-dd)", "Documento Cliente Asociado", "Nombres Cliente Asociado", "Apellidos Cliente Asociado", "Moneda", "Objeto del Contrato", "Presupuesto", "Margen" , "Plazo de Solicitud", "Tipo de Servicio" , "Informacion Adicional" ,"Condición de Pago" , "Consulto Asignado", "Renovacion Automática", "Detalle Renovación Automática", "Ajuste de Honorarios", "Detalle de Ajuste de Honorarios", "Garantía", "Detalle de Garantía", "Forma de Pago", "Resolución Anticipada", "Detalle de Resolución Anticipada","Penalidades", "Detalle Penalidades" , "Bien Mueble o Inmueble", "Bien Partida Certificada", "Bien Dirección", "Bien Uso", "Bien Descripcion", "Renta Pactada", "Importe de Venta" , "Plazo de Arriendo" 
            ]

            for column in required_columns:
                if column not in headers:
                    return Response({"error": f"Falta la columna requerida: {column}"}, status=status.HTTP_400_BAD_REQUEST)

            solicitudes_creadas = []
            factory = APIRequestFactory()

            for row in sheet.iter_rows(min_row=2, values_only=True): 
                row_data = dict(zip(headers, row)) 

                with connection.cursor() as cursor:
                    select_queryTS = """
                        SELECT int_idTipoSolicitud  
                        FROM tm_tiposolicitud 
                        WHERE str_Nombre = %s AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryTS, [row_data.get("Nom_tipoSol"), str_idSuscriptor])
                    idTipoSol = cursor.fetchone()
                    if not idTipoSol:
                        return Response({"error": f"No se encontró el tipo de solicitud: {row_data.get('Nom_tipoSol')}"}, status=status.HTTP_400_BAD_REQUEST)

                    select_queryEM = """
                        SELECT int_idEmpresa 
                        FROM tc_empresas 
                        WHERE str_NombreEmpresa = %s AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryEM, [row_data.get("Nom_empresa"), str_idSuscriptor])
                    idEmpresa = cursor.fetchone()
                    if not idEmpresa:
                        return Response({"error": f"No se encontró la empresa: {row_data.get('Nom_empresa')}"}, status=status.HTTP_400_BAD_REQUEST)

                    select_queryUN = """
                        SELECT int_idUnidadesNegocio 
                        FROM tm_unidadesnegocios 
                        WHERE str_Descripcion = %s AND str_idSuscripcion = %s AND int_idEmpresa = %s
                    """
                    cursor.execute(select_queryUN, [row_data.get("Nom_unidadNegocio"), str_idSuscriptor, idEmpresa[0]])
                    idUnidadNegocio = cursor.fetchone()
                    if not idUnidadNegocio:
                        return Response({"error": f"No se encontró la unidad de negocio: {row_data.get('Nom_unidadNegocio')}"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    select_queryCA = """
                        SELECT int_idInterlocutor  
                        FROM tm_interlocutores 
                        WHERE str_Documento = %s AND str_idSuscripcion = %s 
                    """
                    cursor.execute(select_queryCA, [row_data.get("Documento Cliente Asociado"), str_idSuscriptor ])
                    ClienteAsociado = cursor.fetchone()
                    if not ClienteAsociado:
                        serializer_dataCA = {
                            "str_idSuscripcion": str_idSuscriptor,
                            "str_Documento": row_data.get("Documento Cliente Asociado"),
                            "str_Interlocutor": row_data.get("Nombres Cliente Asociado") + " " + row_data.get("Apellidos Cliente Asociado")
                        }

                        simulated_requestCA = factory.post(
                            'api/interlocutores/', 
                            data=serializer_dataCA, 
                            format='json'
                        )
                        solicitud_create_view = InterlocutoresList.as_view()
                        responseCA = solicitud_create_view(simulated_requestCA)
                        if responseCA.status_code == status.HTTP_201_CREATED:
                            ClienteAsociado = responseCA.data.get('int_idInterlocutor')
                        else:
                            return Response({
                                "error": "Error al crear al interlocutor.",
                                "detalle": response.data
                            }, status=status.HTTP_400_BAD_REQUEST)
                        return Response({"error": f"No se encontró la unidad de negocio: {row_data.get('Nom_unidadNegocio')}"}, status=status.HTTP_400_BAD_REQUEST)
                serializer_data = {
                    "int_idTipoSol": idTipoSol[0],
                    "str_idSuscriptor": str_idSuscriptor,
                    "int_idUsuarioCreacion": idUsuario,
                    "int_idSolicitante": idUsuario,
                    "int_idEmpresa": idEmpresa[0],
                    "db_Honorarios": row_data.get("Honorarios"),
                    "int_idUnidadNegocio": idUnidadNegocio[0],
                    "int_SolicitudGuardada": 0,
                    "str_DeTerceros": row_data.get("De Terceros"),
                    "dt_FechaEsperada": row_data.get("Fecha Esperada").strftime("%Y-%m-%d %H:%M:%S"),
                    "int_idClienteAsociado ": ClienteAsociado
                }

                simulated_request = factory.post(
                    '/api/solicitudes/create', 
                    data=serializer_data, 
                    format='json'
                )

                solicitud_create_view = SolicitudesCreate.as_view()
                response = solicitud_create_view(simulated_request)

                if response.status_code == status.HTTP_201_CREATED:
                    print(response.data.get('int_idSolicitudes'))
                    idSolicitudCreada = response.data.get('int_idSolicitudes')
                    solicitudes_creadas.append(response.data)
                else:
                    return Response({
                        "error": "Error al crear una solicitud.",
                        "detalle": response.data
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                
                
                serializer_dataContenido = {
                     "int_idSolicitudes": idSolicitudCreada,
                     "str_idSuscriptor": str_idSuscriptor,
                     "str_Moneda": row_data.get("Moneda"),
                     "str_ObjetivoContrato": row_data.get("Objeto del Contrato"),
                     "db_Presupuesto" : row_data.get("Presupuesto"),
                     "str_Margen": row_data.get("Margen"),
                     "str_PlazoSolicitud": row_data.get("Plazo de Solicitud"),
                     "str_TipoServicio": row_data.get("Tipo de Servicio"),
                     "str_InfoAdicional": row_data.get("Informacion Adicional"),
                     "str_CondicionPago": row_data.get("Condición de Pago"),
                     "str_ConsultorAsignado": row_data.get("Consulto Asignado"),
                     "str_RenovacionAuto": row_data.get("Renovacion Automática"),
                     "str_DetalleRenovAuto": row_data.get("Detalle Renovación Automática"),
                     "str_AjusteHonorarios": row_data.get("Ajuste de Honorarios"),
                     "str_DetalleAjusteHonorarios": row_data.get("Detalle de Ajuste de Honorarios"),
                     "str_Garantia": row_data.get("Garantía"),
                     "str_DetalleGarantia": row_data.get("Detalle de Garantía"),
                     "str_FormaPago": row_data.get("Forma de Pago"),
                     "str_ResolucionAnticipada": row_data.get("Resolución Anticipada"),
                     "str_DetalleResolucionAnticipada": row_data.get("Detalle de Resolución Anticipada"),
                     "str_Penalidades": row_data.get("Penalidades"),
                     "str_DetallePenalidades": row_data.get("Detalle Penalidades"),
                     "str_BienMuebleInmueble": row_data.get("Bien Mueble o Inmueble"),
                     "str_BienPartidaCertificada": row_data.get("Bien Partida Certificada"),
                     "str_BienDireccion": row_data.get("Bien Dirección"),
                     "str_BienUso": row_data.get("Bien Uso"),
                     "str_BienDescripcion": row_data.get("Bien Descripcion"),
                     "str_RentaPactada": row_data.get("Renta Pactada"),
                     "str_ImporteVenta": row_data.get("Importe de Venta"),
                     "str_PlazoArriendo": row_data.get("Plazo de Arriendo"),

                 }
                print(serializer_dataContenido)
                simulated_requestContenido = factory.post(
                    'api/ContenidoSolicitud/crear/', 
                    data=serializer_dataContenido, 
                    format='json'
                )

                solicitud_create_view = ContenidoIngresar.as_view()
                responseContenido = solicitud_create_view(simulated_requestContenido)

                if response.status_code == status.HTTP_201_CREATED:
                    print(responseContenido.data.get('int_idSolicitudes'))
                else:
                    return Response({
                        "error": "Error al crear una solicitud.",
                        "detalle": responseContenido.data
                    }, status=status.HTTP_400_BAD_REQUEST)
            os.remove(temp_file_path)

            return Response({
                "message": "Archivo procesado exitosamente.",
                "solicitudes_creadas": solicitudes_creadas
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
class SolicitudesCreateHistoricos(APIView):
    def post(self, request):
        serializer = SolicitudSerializerHistorico(data=request.data)
        if serializer.is_valid():
            str_TipoContrato = serializer.validated_data['str_TipoContrato']
            str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
            int_idUsuarioCreacion=serializer.validated_data['int_idUsuarioCreacion'],
            anio = datetime.now().year   
            fecha_actual = datetime.now().strftime("%Y%m")  
            try:
                with connection.cursor() as cursor:
                    select_queryTS = """
                    SELECT str_CodTipoSol , int_idTipoSolicitud
                    FROM tm_tiposolicitud 
                    WHERE str_Nombre = %s AND str_idSuscripcion = %s 
                    """
                    cursor.execute(select_queryTS, [str_TipoContrato, str_idSuscriptor])
                    resultadoTS = cursor.fetchone() 
                    
                    if not resultadoTS:
                        return Response({"error": "Tipo de solicitud no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    codTipoSolicitud = resultadoTS[0]
                    int_idTipoSolicitud = resultadoTS[1]
                    select_query = """
                    SELECT str_CorrelativoTipoSolicitud 
                    FROM tr_correlativoHistoricos 
                    WHERE int_idTipoSolicitud = %s AND str_idSuscriptor = %s  AND int_anio = %s
                    """
                    cursor.execute(select_query, [int_idTipoSolicitud, str_idSuscriptor,anio])
                    resultado = cursor.fetchone()
                    print(resultado)
                    if resultado:
                        correlativo_actual = int(resultado[0])
                        nuevo_correlativo = correlativo_actual + 1
                    else:
                        nuevo_correlativo = 1

                    nuevo_correlativo_str = f"{nuevo_correlativo:04d}" 
                    print(nuevo_correlativo_str)
                    if resultado:
                        update_query = """
                        UPDATE tr_correlativoHistoricos 
                        SET str_CorrelativoTipoSolicitud = %s , dt_FechaModificacion = NOW() , int_idUsuarioModificacion =%s
                        WHERE int_idTipoSolicitud = %s AND str_idSuscriptor = %s  AND int_anio = %s 
                        """
                        cursor.execute(update_query, [nuevo_correlativo_str,int_idUsuarioCreacion, int_idTipoSolicitud, str_idSuscriptor,anio])
                    else:
                        insert_query = """
                        INSERT INTO tr_correlativoHistoricos (int_idCorrelativo, str_idSuscriptor, int_idTipoSolicitud, str_CorrelativoTipoSolicitud, dt_FechaCreacion, int_anio) 
                        VALUES (DEFAULT, %s, %s, %s, NOW(), %s)
                        """
                        cursor.execute(insert_query, [str_idSuscriptor, int_idTipoSolicitud, nuevo_correlativo_str,anio])
                    
                    str_CodSolicitudes = f"CH-{codTipoSolicitud}-{fecha_actual}{nuevo_correlativo_str}"
                    select_queryES = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_Nombre = "Nuevo" AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryES, [str_idSuscriptor])
                    resultadoES = cursor.fetchone()
                    if not resultadoES:
                        return Response({"error": "Estado no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    Id_estado = resultadoES[0]
                    insert_solicitud_query = """
                    INSERT INTO tr_solicitudes 
                    (str_CodSolicitudes, int_idSolicitante, str_idSuscriptor, int_idEmpresa ,db_Honorarios, int_idEstado, int_idUnidadNegocio,int_SolicitudGuardada , 
                    str_DeTerceros, str_Visible, int_idTipoSol, dt_FechaRegistro, dt_FechaEsperada, dt_FechaCreacion, int_idUsuarioCreacion,int_idClienteAsociado) 
                    VALUES (%s, %s, %s, %s, %s, %s,%s, %s,%s, %s, %s,NOW(),  %s, NOW(), %s, %s)
                    """
                    int_idSolicitante = serializer.validated_data.get('int_idSolicitante', None)
                    int_idEmpresa = serializer.validated_data.get('int_idEmpresa', None)
                    db_Honorarios = serializer.validated_data.get('db_Honorarios', None)
                    int_idUnidadNegocio = serializer.validated_data.get('int_idUnidadNegocio', None)
                    int_SolicitudGuardada = serializer.validated_data.get('int_SolicitudGuardada', "no")
                    str_DeTerceros = serializer.validated_data.get('str_DeTerceros', None)
                    dt_FechaEsperada = serializer.validated_data.get('dt_FechaEsperada', None)
                    int_idClienteAsociado  = serializer.validated_data.get('int_idClienteAsociado', None)

                    cursor.execute(insert_solicitud_query, [
                        str_CodSolicitudes,
                        int_idSolicitante,
                        str_idSuscriptor,
                        int_idEmpresa,
                        db_Honorarios,
                        Id_estado,
                        int_idUnidadNegocio,
                        int_SolicitudGuardada,
                        str_DeTerceros,
                        "si",
                        int_idTipoSolicitud,
                        dt_FechaEsperada,
                        int_idUsuarioCreacion,
                        int_idClienteAsociado
                        
                    ])
                    solicitud_id = cursor.lastrowid
                    
                return Response({
                    "data": serializer.data,
                    "int_idSolicitudes": solicitud_id,
                    "str_CodSolicitudes": str_CodSolicitudes
                }, status=status.HTTP_201_CREATED)
            except DatabaseError as e:
                return Response({"error": "Error de base de datos: " + str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SolicitudesCreateAdenda(APIView):
    def post(self, request):
        serializer = SolicitudSerializerAdenda(data=request.data)
        if serializer.is_valid():
            int_idTipoSolicitud = serializer.validated_data['int_idTipoSol']
            str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
            int_idUsuarioCreacion = serializer.validated_data['int_idUsuarioCreacion']
            str_CodSolicitudes = serializer.validated_data['str_CodSolicitudes']
            fecha_actual = datetime.now().strftime("%Y%m")  

            try:
                with connection.cursor() as cursor:
                    # Buscar si ya existe un código similar
                    if '-A' in str_CodSolicitudes:
                      str_CodSolicitudes = str_CodSolicitudes.rsplit('-', 1)[0]

                    base_cod_solicitud = f"{str_CodSolicitudes}-A"
                    select_query = """
                    SELECT str_CodSolicitudes 
                    FROM tr_solicitudes 
                    WHERE str_CodSolicitudes LIKE %s
                    """
                    cursor.execute(select_query, [f"{base_cod_solicitud}%"])
                    resultados = cursor.fetchall()

                    # Calcular el siguiente sufijo -A1, -A2, etc.
                    if resultados:
                        numeros_existentes = []
                        for resultado in resultados:
                            cod_existente = resultado[0]
                            try:
                                # Extraer el número al final del código, por ejemplo: "-A2"
                                sufijo_numero = cod_existente.split("-A")[1]
                                numeros_existentes.append(int(sufijo_numero))
                            except (IndexError, ValueError):
                                continue
                        # Asignar el siguiente número disponible
                        if numeros_existentes:
                            next_number = max(numeros_existentes) + 1
                        else:
                            next_number = 1
                    else:
                        next_number = 1

                    # Formar el nuevo código con el número
                    str_CodSolicitudesAdenda = f"{base_cod_solicitud}{next_number}"

                    # Obtener el estado
                    select_queryES = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_Nombre = "Nuevo" AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryES, [str_idSuscriptor])
                    resultadoES = cursor.fetchone()
                    if not resultadoES:
                        return Response({"error": "Estado no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    Id_estado = resultadoES[0]

                    # Insertar la nueva solicitud
                    insert_solicitud_query = """
                    INSERT INTO tr_solicitudes 
                    (str_CodSolicitudes, int_idSolicitante, str_idSuscriptor, int_idEmpresa ,db_Honorarios, int_idEstado, int_idUnidadNegocio, int_SolicitudGuardada, 
                    str_DeTerceros, str_Visible, int_idTipoSol, dt_FechaRegistro, dt_FechaEsperada, dt_FechaCreacion, int_idUsuarioCreacion, int_idClienteAsociado) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, NOW(), %s, %s)
                    """
                    int_idSolicitante = serializer.validated_data.get('int_idSolicitante', None)
                    int_idEmpresa = serializer.validated_data.get('int_idEmpresa', None)
                    db_Honorarios = serializer.validated_data.get('db_Honorarios', None)
                    int_idUnidadNegocio = serializer.validated_data.get('int_idUnidadNegocio', None)
                    int_SolicitudGuardada = serializer.validated_data.get('int_SolicitudGuardada', "no")
                    str_DeTerceros = serializer.validated_data.get('str_DeTerceros', None)
                    dt_FechaEsperada = serializer.validated_data.get('dt_FechaEsperada', None)
                    int_idClienteAsociado = serializer.validated_data.get('int_idClienteAsociado', None)

                    cursor.execute(insert_solicitud_query, [
                        str_CodSolicitudesAdenda,
                        int_idSolicitante,
                        str_idSuscriptor,
                        int_idEmpresa,
                        db_Honorarios,
                        Id_estado,
                        int_idUnidadNegocio,
                        int_SolicitudGuardada,
                        str_DeTerceros,
                        "si",
                        int_idTipoSolicitud,
                        dt_FechaEsperada,
                        int_idUsuarioCreacion,
                        int_idClienteAsociado
                    ])
                    solicitud_id = cursor.lastrowid
                    
                return Response({
                    "data": serializer.data,
                    "int_idSolicitudes": solicitud_id,
                    "str_CodSolicitudes": str_CodSolicitudesAdenda
                }, status=status.HTTP_201_CREATED)
            except DatabaseError as e:
                return Response({"error": "Error de base de datos: " + str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SolicitudesCreateExtraJudiciales(APIView):
    def post(self, request):
        serializer = SolicitudSerializerExtraJudicial(data=request.data)
        if serializer.is_valid():
            int_idTipoSolicitud = serializer.validated_data['int_idTipoSol']
            str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
            int_idUsuarioCreacion = serializer.validated_data['int_idUsuarioCreacion']
            str_CodSolicitudes = serializer.validated_data['str_CodSolicitudes']
            fecha_actual = datetime.now().strftime("%Y%m")  

            try:
                with connection.cursor() as cursor:
                    # Buscar si ya existe un código similar
                    if '-A' in str_CodSolicitudes:
                      str_CodSolicitudes = str_CodSolicitudes.rsplit('-', 1)[0]

                    base_cod_solicitud = f"{str_CodSolicitudes}-EJ"
                    select_query = """
                    SELECT str_CodSolicitudes 
                    FROM tr_solicitudes 
                    WHERE str_CodSolicitudes LIKE %s
                    """
                    cursor.execute(select_query, [f"{base_cod_solicitud}%"])
                    resultados = cursor.fetchall()

                    # Calcular el siguiente sufijo -A1, -A2, etc.
                    if resultados:
                        numeros_existentes = []
                        for resultado in resultados:
                            cod_existente = resultado[0]
                            try:
                                # Extraer el número al final del código, por ejemplo: "-A2"
                                sufijo_numero = cod_existente.split("-EJ")[1]
                                numeros_existentes.append(int(sufijo_numero))
                            except (IndexError, ValueError):
                                continue
                        # Asignar el siguiente número disponible
                        if numeros_existentes:
                            next_number = max(numeros_existentes) + 1
                        else:
                            next_number = 1
                    else:
                        next_number = 1

                    # Formar el nuevo código con el número
                    str_CodSolicitudesEJ = f"{base_cod_solicitud}{next_number}"

                    # Obtener el estado
                    select_queryES = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_Nombre = "Nuevo" AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryES, [str_idSuscriptor])
                    resultadoES = cursor.fetchone()
                    if not resultadoES:
                        return Response({"error": "Estado no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    Id_estado = resultadoES[0]

                    # Insertar la nueva solicitud
                    insert_solicitud_query = """
                    INSERT INTO tr_solicitudes 
                    (str_CodSolicitudes, int_idSolicitante, str_idSuscriptor, int_idEmpresa ,db_Honorarios, int_idEstado, int_idUnidadNegocio, int_SolicitudGuardada, 
                    str_DeTerceros, str_Visible, int_idTipoSol, dt_FechaRegistro, dt_FechaCreacion, int_idUsuarioCreacion, int_idClienteAsociado,str_DetalleAcuerdo,dt_FechaFirmaEJ,
                    str_documentoFirmante1,str_documentoFirmante2,str_Firmante1,str_Firmante2) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    int_idSolicitante = serializer.validated_data.get('int_idSolicitante', None)
                    int_idEmpresa = serializer.validated_data.get('int_idEmpresa', None)
                    db_Honorarios = serializer.validated_data.get('db_Honorarios', None)
                    int_idUnidadNegocio = serializer.validated_data.get('int_idUnidadNegocio', None)
                    int_SolicitudGuardada = serializer.validated_data.get('int_SolicitudGuardada', "no")
                    str_DeTerceros = serializer.validated_data.get('str_DeTerceros', None)
                    int_idClienteAsociado = serializer.validated_data.get('int_idClienteAsociado', None)
                    str_DetalleAcuerdo = serializer.validated_data.get('str_DetalleAcuerdo', None)
                    dt_FechaFirmaEJ = serializer.validated_data.get('dt_FechaFirmaEJ', None)
                    str_documentoFirmante1 = serializer.validated_data.get('str_documentoFirmante1', None)
                    str_documentoFirmante2 = serializer.validated_data.get('str_documentoFirmante2', None)
                    str_Firmante1 = serializer.validated_data.get('str_Firmante1', None)
                    str_Firmante2 = serializer.validated_data.get('str_Firmante2', None)
                    cursor.execute(insert_solicitud_query, [
                        str_CodSolicitudesEJ,
                        int_idSolicitante,
                        str_idSuscriptor,
                        int_idEmpresa,
                        db_Honorarios,
                        Id_estado,
                        int_idUnidadNegocio,
                        int_SolicitudGuardada,
                        str_DeTerceros,
                        "si",
                        int_idTipoSolicitud,
                        int_idUsuarioCreacion,
                        int_idClienteAsociado,
                        str_DetalleAcuerdo,
                        dt_FechaFirmaEJ,
                        str_documentoFirmante1,
                        str_documentoFirmante2,
                        str_Firmante1,
                        str_Firmante2
                    ])
                    solicitud_id = cursor.lastrowid
                    
                return Response({
                    "data": serializer.data,
                    "int_idSolicitudes": solicitud_id,
                    "str_CodSolicitudes": str_CodSolicitudesEJ
                }, status=status.HTTP_201_CREATED)
            except DatabaseError as e:
                return Response({"error": "Error de base de datos: " + str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SolicitudDetailAPIView(APIView):
    def get_object(self, pk):
        with connection.cursor() as cursor:
            cursor.execute("""
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos) AS nombre_gestor,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MAX(sc.dt_FechaRenAut) AS dt_FechaRenAut
            FROM 
                tr_solicitudes s
            INNER JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            INNER JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            INNER JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            INNER JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont sc ON sc.int_idSolicitudes = s.int_idSolicitudes
            WHERE  
                s.int_idSolicitudes = %s 

            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
            """, [pk])
            row = cursor.fetchone()
            if row:
                return {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_DeTerceros': row[8],
                    'str_Visible': row[9],
                    'int_idTipoSol': row[10],
                    'nombre_TipoSolicitud': row[11],
                    'dt_FechaRegistro': row[12],
                    'dt_FechaEsperada': row[13],
                    'int_idGestor': row[14],
                    'int_idClienteAsociado': row[15],
                    'str_NombreEmpresa': row[16],
                    'str_RazonSocial': row[17],
                    'str_Ruc': row[18],
                    'tags': row[19],
                    'nombre_gestor': row[20],
                    'int_SolicitudGuardada':row[21],
                    'str_CodTipoSol':row[22],
                    'dt_FechaRenAut':row[23],
                }
            return None


    def get(self, request, pk):
        solicitud = self.get_object(pk)
        if solicitud:
            return Response(solicitud)
        return Response(
            {"error": "Solicitud no encontrada."},
            status=status.HTTP_404_NOT_FOUND
        )
    def put(self, request, pk):
        # Instanciar el serializer con los datos del request
        serializer = SolicitudUpdateSerializer(data=request.data)
        
        # Validar los datos recibidos
        if serializer.is_valid():
            data = serializer.validated_data
            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE tr_solicitudes
                    SET 
                        int_idEmpresa = %s, 
                        int_idUnidadNegocio = %s, 
                        str_DeTerceros = %s, 
                        str_Visible = 'si', 
                        int_idTipoSol = %s, 
                        dt_FechaEsperada = %s, 
                        int_idClienteAsociado = %s,
                        int_SolicitudGuardada = %s,
                        dt_FechaModificacion= NOW(),
                        int_idUsuarioModificacion =%s,
                        db_Honorarios =%s
                    WHERE 
                        int_idSolicitudes = %s
                """, [
                    data['int_idEmpresa'],
                    data['int_idUnidadNegocio'],
                    data['str_DeTerceros'],
                    data['int_idTipoSol'],
                    data['dt_FechaEsperada'],
                    data.get('int_idClienteAsociado'),
                    data['int_SolicitudGuardada'],
                    data['int_idUsuarioModificacion'],
                    data['db_Honorarios'],
                    pk

                ])
                
                return Response({'message': 'Solicitud actualizada correctamente'}, status=status.HTTP_200_OK)
        else:
            # Si los datos no son válidos, devolver errores de validación
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tr_solicitudes WHERE int_idSolicitudes = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)
class InsertarAprobadorView(APIView):
    def post(self, request, format=None):
        # Obtener la lista de aprobadores
        aprobadores_data = request.data.get('aprobadores', [])
        
        if not aprobadores_data:
            return Response({'error': 'No se ha enviado la lista de aprobadores'}, status=status.HTTP_400_BAD_REQUEST)

        # Consulta para obtener los datos del usuario
        sql_query_user = '''
            SELECT str_Nombres, str_Apellidos, str_Correo 
            FROM tm_usuarios 
            WHERE int_idUsuarios = %s
        '''
        
        try:
            # Obtener el siguiente número de bloque
            sql_query_bloque = '''
                SELECT COALESCE(MAX(int_NumBloque), 0) 
                FROM tr_aprobadores 
                WHERE int_idSolicitudes = %s
            '''
            with connection.cursor() as cursor:
                cursor.execute(sql_query_bloque, [aprobadores_data[0]['int_idSolicitudes']])
                max_bloque = cursor.fetchone()[0]
                siguiente_bloque = max_bloque + 1

        except Exception as e:
            return Response({'error': f'Error al calcular el número de bloque: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        sql_query_solicitud = '''
            SELECT 
            s.str_CodSolicitudes,
            MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
            MAX(un.str_Descripcion) AS str_Descripcion,
            MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
            MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
            MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor
            FROM 
            tr_solicitudes s
            LEFT JOIN 
            tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
            tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
            tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
            tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
            tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
            LEFT JOIN 
            tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes
            LEFT JOIN 
            tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes  
            LEFT JOIN 
            tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
            LEFT JOIN 
            tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
            LEFT JOIN 
            tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
            LEFT JOIN 
            tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
            LEFT JOIN 
            tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
            WHERE  
            s.int_idSolicitudes= %s
        '''
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql_query_solicitud, [aprobadores_data[0]['int_idSolicitudes']])
                solicitud_data = cursor.fetchone()

            if not solicitud_data:
                return Response({'error': 'Solicitud no encontrada'}, status=status.HTTP_404_NOT_FOUND)

            cod_solicitud, str_NombreEmpresa, str_Descripcion, nombre_TipoSolicitud, str_ObjetivoContrato, nombre_gestor = solicitud_data

            # Crear fila de la tabla para todos los aprobadores
            table_rows = f"""
                <tr>
                    <td style='text-align: center;'>{cod_solicitud}</td>
                    <td style='text-align: center;'>{str_NombreEmpresa}</td>
                    <td style='text-align: center;'>{str_Descripcion}</td>
                    <td style='text-align: center;'>{nombre_TipoSolicitud}</td>
                    <td style='text-align: center;'>{str_ObjetivoContrato}</td>
                    <td style='text-align: center;'>{nombre_gestor}</td>
                </tr>
            """
        except Exception as e:
            return Response({'error': f'Error al obtener datos de la solicitud: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        # Enviar los datos para cada usuario (aprobador)
        for aprobador_data in aprobadores_data:
            usuario_id = aprobador_data['int_idUsuario']

            try:
                # Consulta para obtener los datos del usuario
                with connection.cursor() as cursor:
                    cursor.execute(sql_query_user, [usuario_id])
                    user_data = cursor.fetchone()

                if not user_data:
                    return Response({'error': f'Usuario con ID {usuario_id} no encontrado'}, status=status.HTTP_404_NOT_FOUND)

                user_name = user_data[0]
                user_last_name = user_data[1]
                user_email = user_data[2]
                full_name = f'{user_name} {user_last_name}'
                
                # Consulta para insertar el aprobador
                sql_query_insert = '''
                    INSERT INTO tr_aprobadores (
                        str_idSuscripcion,
                        int_idUsuario,
                        int_idSolicitudes,
                        int_OrdenAprobacion,
                        int_NumBloque,
                        int_EstadoAprobacion,
                        dt_FechaCreacion,
                        int_idUsuarioCreacion
                    ) VALUES (%s, %s, %s, %s, %s, %s, NOW(), %s)
                '''
                values = (
                    aprobador_data['str_idSuscripcion'],
                    usuario_id,  # Inserta el usuario de la lista
                    aprobador_data['int_idSolicitudes'],
                    aprobador_data['int_OrdenAprobacion'],
                    siguiente_bloque,  # Mantiene el mismo número de bloque para todos
                    0,  # Estado inicial
                    aprobador_data['int_idUsuarioCreacion']
                )

                with connection.cursor() as cursor:
                    cursor.execute(sql_query_insert, values)

                # Contenido del correo
                html_content = f"""
                  <html>
                  <body>
                      <p>Hola {full_name}, tienes una nueva solicitud pendiente de aprobar y tu orden de aprobación es el N°{aprobador_data['int_OrdenAprobacion']}:</p>
                      <table border="3" style="border-collapse: collapse; width: 100%; background-color:#fff; border: 1px solid #ddd; border-radius: 10px; box-shadow: 0 4px 4px #00000040;">
                          <thead style="background-color: #f5f5f5; color: #000;"> 
                              <tr>
                                  <th style="padding: 1rem;">Código de Contrato</th>
                                  <th style="padding: 1rem;">Empresa</th>
                                  <th style="padding: 1rem;">Unidad Negocio</th>
                                  <th style="padding: 1rem;">Tipo Solicitud</th>
                                  <th style="padding: 1rem;">Objetivo</th>
                                  <th style="padding: 1rem;">Gestor</th>
                              </tr>
                          </thead>
                          <tbody>
                              {table_rows}
                          </tbody>
                      </table>
                  </body>
                  </html>
                """

                # Enviar correo con SendGrid
                message = Mail(
                      from_email=('<EMAIL>', 'Soporte - Prisma Legal'),
                      to_emails=user_email,
                      subject='Prisma Legal - Solicitud Pendiente de Aprobar',
                      html_content=html_content
                  )
                sg = SendGridAPIClient('*********************************************************************') 
                response = sg.send(message)

                # Log de depuración
                print(f"SendGrid response: {response.status_code}, {response.body}, {response.headers}")

            except Exception as e:
                return Response({'error': f'Error al insertar aprobador o enviar correo para usuario {usuario_id}: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        return Response({'message': 'Aprobadores insertados correctamente'}, status=status.HTTP_201_CREATED)
        
class ListarAprobadoresView(APIView):

    def get(self, request, str_idSuscripcion, int_idSolicitudes, format=None):
        try:
            # Consulta SQL para obtener los aprobadores según suscripción y solicitud
            sql_query = '''
                SELECT 
                    a.int_idAprobador,
                    a.str_idSuscripcion,
                    a.int_idUsuario,
                    u.str_Nombres,
                    u.str_Apellidos,
                    a.int_OrdenAprobacion,
                    a.int_EstadoAprobacion,
                    a.dt_FechaCreacion,
                    a.dt_FechaAceptacion,
                    a.int_NumBloque
                FROM 
                    tr_aprobadores a
                JOIN 
                    tm_usuarios u ON a.int_idUsuario = u.int_idUsuarios
                WHERE 
                    a.str_idSuscripcion = %s
                    AND a.int_idSolicitudes = %s
                ORDER BY 
                    a.int_NumBloque ASC , a.int_OrdenAprobacion ASC;
            '''

            # Parámetros de la consulta
            values = (str_idSuscripcion, int_idSolicitudes)

            # Ejecutar la consulta SQL
            with connection.cursor() as cursor:
                cursor.execute(sql_query, values)
                aprobadores = cursor.fetchall()

            # Estructurar los datos para la respuesta
            aprobadores_list = [
                {
                    'int_idAprobador': aprobador[0],
                    'str_idSuscripcion': aprobador[1],
                    'int_idUsuario': aprobador[2],
                    'str_Nombres': aprobador[3],
                    'str_Apellidos': aprobador[4],
                    'int_OrdenAprobacion': aprobador[5],
                    'int_EstadoAprobacion': aprobador[6],
                    'dt_FechaCreacion': aprobador[7].strftime('%Y-%m-%d %H:%M:%S'),
                    'dt_FechaAceptacion': aprobador[8],
                    'int_NumBloque': aprobador[9],

                }
                for aprobador in aprobadores
            ]

            # Devolver la lista de aprobadores
            return Response(aprobadores_list, status=status.HTTP_200_OK)

        except Exception as e:
            # Manejo de errores
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
class ActualizarAprobadorView(APIView):

    def put(self, request, int_idSolicitudes, int_idUsuario, suscriptor, format=None):
        try:
            sql_max_bloque_query = '''
                SELECT MAX(int_NumBloque) 
                FROM tr_aprobadores
                WHERE int_idSolicitudes = %s
            '''
            with connection.cursor() as cursor:
                cursor.execute(sql_max_bloque_query, [int_idSolicitudes])
                int_NumBloque = cursor.fetchone()[0]

            # Verificar si hay un bloque asociado
            if int_NumBloque is None:
                return Response({'message': 'No hay bloques asociados a esta solicitud'}, status=status.HTTP_404_NOT_FOUND)
            # Consultar el orden del aprobador actual
            sql_orden_query = '''
                SELECT int_OrdenAprobacion 
                FROM tr_aprobadores
                WHERE int_idSolicitudes = %s AND int_idUsuario = %s AND int_NumBloque = %s
            '''
            values_orden = (int_idSolicitudes, int_idUsuario, int_NumBloque)
            
            with connection.cursor() as cursor:
                cursor.execute(sql_orden_query, values_orden)
                result = cursor.fetchone()

            # Si el aprobador no existe
            if not result:
                return Response({'message': 'Aprobador no encontrado'}, status=status.HTTP_404_NOT_FOUND)

            # Obtener el orden del aprobador actual
            orden_aprobador = result[0]

            # Validar que los aprobadores anteriores hayan aprobado si el orden es mayor a 1
            if orden_aprobador > 1:
                sql_verificar_anterior = '''
                    SELECT int_OrdenAprobacion, int_EstadoAprobacion
                    FROM tr_aprobadores
                    WHERE int_idSolicitudes = %s AND int_NumBloque = %s AND int_OrdenAprobacion < %s
                '''
                
                # Verificar desde el primer aprobador hasta el anterior al actual
                with connection.cursor() as cursor:
                    cursor.execute(sql_verificar_anterior, (int_idSolicitudes,int_NumBloque, orden_aprobador))
                    aprobadores_anteriores = cursor.fetchall()

                    # Comprobar si algún aprobador anterior no ha aprobado
                    for aprobacion_anterior in aprobadores_anteriores:
                        if aprobacion_anterior[1] != 1:  # índice 1 es el int_EstadoAprobacion
                            return Response({
                                'message': f'El aprobador en la posición {aprobacion_anterior[0]} debe aprobar antes de continuar. Estado actual: {aprobacion_anterior[1]}'
                            }, status=status.HTTP_400_BAD_REQUEST)

            # Actualizar el estado del aprobador actual
            nuevo_estado = 1  # Suponiendo que 1 es el estado de "Aprobado"

            sql_update_query = '''
                UPDATE 
                    tr_aprobadores
                SET 
                    int_EstadoAprobacion = %s,
                    dt_FechaAceptacion = NOW()
                WHERE 
                    int_idSolicitudes = %s
                    AND int_idUsuario = %s
                    AND int_NumBloque = %s
            '''

            update_values = (nuevo_estado, int_idSolicitudes, int_idUsuario,int_NumBloque)

            with connection.cursor() as cursor:
                cursor.execute(sql_update_query, update_values)

            # Comprobar si se actualizó algún registro
            if cursor.rowcount > 0:
                # Obtener el último orden de aprobación
                sql_max_orden_query = '''
                    SELECT MAX(int_OrdenAprobacion) 
                    FROM tr_aprobadores
                    WHERE int_idSolicitudes = %s AND int_NumBloque = %s
                '''
                with connection.cursor() as cursor:
                    cursor.execute(sql_max_orden_query, (int_idSolicitudes,int_NumBloque
                    ))
                    max_orden_result = cursor.fetchone()

                if not max_orden_result or max_orden_result[0] is None:
                    return Response({'message': 'No se encontraron aprobadores para actualizar.'}, status=status.HTTP_404_NOT_FOUND)

                ultimo_orden = max_orden_result[0]

                # Si el aprobador actual es el último, cambiar el estado de la solicitud a "Aprobado"
                if orden_aprobador == ultimo_orden:
                    response = self.cambiar_estado_a_aprobado(int_idSolicitudes, int_idUsuario, suscriptor)

                    # Verificar la respuesta de la vista
                    if response.status_code != 200:
                        return Response({
                            'message': 'Error al intentar cambiar el estado de la solicitud a Aprobado',
                            'details': response.data
                        })

                return Response({'message': 'Aprobador actualizado correctamente'}, status=status.HTTP_200_OK)
            else:
                return Response({'message': 'No se encontró ningún registro para actualizar'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            # Manejo de errores
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def cambiar_estado_a_aprobado(self, int_idSolicitudes, int_idUsuario, suscriptor):
        # Cambiar el estado de la solicitud a "Aprobado"
        request_data = {
            'int_idSolicitudes': int_idSolicitudes,
            'nombre_estado': 'Aprobado', 
            'int_idUsuarioCreacion': int_idUsuario,
            'str_idSuscriptor': suscriptor, 
        }

        # Serializamos los datos a JSON
        json_data = JSONRenderer().render(request_data)
        
        # Creamos una petición utilizando el tipo de contenido adecuado
        factory = RequestFactory()
        request = factory.put(
            'http://localhost:8001/api/solicitudes/editar-estado/', 
            data=json_data,
            content_type='application/json'  # Definimos el tipo de contenido como JSON
        )
        
        # Obtenemos la vista y ejecutamos la solicitud
        view = EditarEstadoSolicitud.as_view()
        response = view(request)

        return response
class RechazarSolicitud(APIView):

    def put(self, request, int_idSolicitudes, int_idUsuario, format=None):
        try:
            sql_max_bloque_query = '''
                SELECT MAX(int_NumBloque) 
                FROM tr_aprobadores
                WHERE int_idSolicitudes = %s
            '''
            with connection.cursor() as cursor:
                cursor.execute(sql_max_bloque_query, [int_idSolicitudes])
                int_NumBloque = cursor.fetchone()[0]

            # Verificar si hay un bloque asociado
            if int_NumBloque is None:
                return Response({'message': 'No hay bloques asociados a esta solicitud'}, status=status.HTTP_404_NOT_FOUND)
                
            # Consultar el orden del aprobador actual
            sql_orden_query = '''
                SELECT int_OrdenAprobacion 
                FROM tr_aprobadores
                WHERE int_idSolicitudes = %s AND int_idUsuario = %s  AND int_NumBloque = %s
            '''
            values_orden = (int_idSolicitudes, int_idUsuario,int_NumBloque)
            
            with connection.cursor() as cursor:
                cursor.execute(sql_orden_query, values_orden)
                result = cursor.fetchone()

            # Si el aprobador no existe
            if not result:
                return Response({'message': 'Aprobador no encontrado'}, status=status.HTTP_404_NOT_FOUND)

            # Obtener el orden del aprobador actual
            orden_aprobador = result[0]

            # Validar que los aprobadores anteriores hayan aprobado si el orden es mayor a 1
            if orden_aprobador > 1:
                sql_verificar_anterior = '''
                    SELECT int_OrdenAprobacion, int_EstadoAprobacion
                    FROM tr_aprobadores
                    WHERE int_idSolicitudes = %s AND int_OrdenAprobacion < %s AND int_NumBloque = %s
                '''
                
                # Verificar desde el primer aprobador hasta el anterior al actual
                with connection.cursor() as cursor:
                    cursor.execute(sql_verificar_anterior, (int_idSolicitudes, orden_aprobador,int_NumBloque))
                    aprobadores_anteriores = cursor.fetchall()

                    # Comprobar si algún aprobador anterior no ha aprobado
                    for aprobacion_anterior in aprobadores_anteriores:
                        if aprobacion_anterior[1] != 1:  # índice 1 es el int_EstadoAprobacion
                            return Response({
                                'message': f'El aprobador en la posición {aprobacion_anterior[0]} debe aprobar antes de continuar. Estado actual: {aprobacion_anterior[1]}'
                            }, status=status.HTTP_400_BAD_REQUEST)

            # Actualizar el estado del aprobador actual a "rechazado" (0)
            nuevo_estado = 0  # Cambiado a 0 para representar el estado de "Rechazado"

            sql_update_query = '''
                UPDATE 
                    tr_aprobadores
                SET 
                    int_EstadoAprobacion = %s,
                    dt_FechaAceptacion = NOW()
                WHERE 
                    int_idSolicitudes = %s
                    AND int_idUsuario = %s
                    AND int_NumBloque = %s
            '''

            update_values = (nuevo_estado, int_idSolicitudes, int_idUsuario,int_NumBloque)

            with connection.cursor() as cursor:
                cursor.execute(sql_update_query, update_values)

            # Comprobar si se actualizó algún registro
            if cursor.rowcount > 0:
                return Response({'message': 'Aprobador rechazado correctamente'}, status=status.HTTP_200_OK)
            else:
                return Response({'message': 'No se encontró ningún registro para actualizar'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            # Manejo de errores
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        

class BusquedaSolicitudxCod(APIView):

    def get(self, request, str_CodSolicitudes,str_idSuscriptor, format=None):
        try:
            # Consulta SQL con alias explícitos
            sql_query = '''
                SELECT 
                    s.*,                    
                    es.str_Nombre AS estado_str_Nombre,
                    ts.str_Nombre AS tipoSol_str_Nombre,
                    ts.str_CodTipoSol AS tipoSol_str_CodTipoSol,
                    em.str_NombreEmpresa AS Empresa_nombre,
                    un.str_Descripcion AS UN_nombre
                                     
                FROM 
                    tr_solicitudes s
                INNER JOIN 
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                INNER JOIN 
                    tm_tiposolicitud ts ON s.int_idTipoSol  = ts.int_idTipoSolicitud 
                LEFT JOIN 
                    tc_empresas em ON s.int_idEmpresa   = em.int_idEmpresa 
                LEFT JOIN 
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio  = un.int_idUnidadesNegocio 
                WHERE 
                    s.str_CodSolicitudes = %s AND s.str_idSuscriptor = %s
            '''

            # Ejecutar la consulta SQL
            with connection.cursor() as cursor:
                cursor.execute(sql_query, [str_CodSolicitudes,str_idSuscriptor])
                resultado = cursor.fetchone()  # Devuelve una sola fila o None
                columnas = [col[0] for col in cursor.description]  # Obtener nombres de columnas

            # Si no hay resultados, devolver un error
            if not resultado:
                return Response(
                    {'error': 'No se encontró ninguna solicitud con el código proporcionado.'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Combinar nombres de columnas con los valores
            datos = dict(zip(columnas, resultado))

            # Devolver los datos tal cual, ya con prefijos en los campos del estado
            return Response(datos, status=status.HTTP_200_OK)

        except Exception as e:
            # Manejo de errores generales
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
#REGISTROS

class BusquedaRegistros(APIView):
    def get(self, request):
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        anio_fecha_Firma = request.query_params.get('anio_fecha_Firma')
        anio_fecha_fin = request.query_params.get('anio_fecha_fin')
        int_idGestor = request.query_params.get('int_idGestor')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        nombre_asociado = request.query_params.get('nombre_asociado')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')
        str_idEmpresa = request.query_params.get('str_idEmpresa')
        str_documentoCliente = request.query_params.get('str_documentoCliente')
        query = """
            SELECT 
                s.str_CodSolicitudes, 
                MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
                MAX(sc.db_Presupuesto) AS db_Presupuesto,
                MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
                MAX(un.str_Descripcion) AS str_Descripcion,
                MAX(es.str_Nombre) AS estado_nombre,
                MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
                MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
                MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
                MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
                MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
                MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
                MAX(sc.str_Garantia) AS str_Garantia,
                MAX(s.db_Honorarios) AS db_Honorarios,
                MAX(sc.str_Margen) AS str_Margen,
                MAX(sc.str_FormaPago) AS str_FormaPago,
                MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
                MAX(s.int_idSolicitudes) AS int_idSolicitudes,
                MAX(s.dt_FechaFin) AS dt_FechaFin,
                MAX(
                        CASE 
                            WHEN sc.str_Moneda = 'dolares' THEN 'Dolares'
                            ELSE e.str_Moneda
                        END
                    ) AS str_Moneda,
                MAX(sc.str_Penalidades) AS str_Penalidades,
                MAX(CASE 
                    WHEN clale.str_Nombre = 'Clausula de no competencia' THEN 'Sí'
                    ELSE NULL
                END) AS Clausula,
                MAX(e.int_idEmpresa) AS int_idEmpresa
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
            LEFT JOIN 
                tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes  
            LEFT JOIN 
                tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
            LEFT JOIN 
                tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor    
            LEFT JOIN 
                    tm_interlocutores ti ON ti.int_idInterlocutor  = s.int_idClienteAsociado     
            LEFT JOIN 
                tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
            LEFT JOIN 
                tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
            LEFT JOIN 
                tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
            WHERE  
                s.str_idSuscriptor = %s AND es.str_Nombre = "Firmado"
            
        """
        params = [str_idSuscriptor]

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if anio_fecha_Firma:
            query += " AND YEAR(s.dt_FirmaContrato) = %s"
            params.append(anio_fecha_Firma)

        if anio_fecha_fin:
            query += " AND YEAR(s.dt_FechaFin) = %s"
            params.append(anio_fecha_fin)

        if int_idGestor:
            query += " AND s.int_idGestor = %s"
            params.append(int_idGestor)
            
        if nombre_asociado:
            query += " AND u.str_Interlocutor = %s"
            params.append(nombre_asociado)

        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)
            
        if str_idEmpresa:
            query += " AND s.int_idEmpresa = %s"
            params.append(str_idEmpresa)
            
        if str_documentoCliente:
            query += " AND ca.str_Documento = %s"
            params.append(str_documentoCliente)
            
        if str_CodSolicitudes:
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"{str_CodSolicitudes}%")
            
        if str_descripcion:
            query += " AND (t.str_descripcion IN %s OR t.str_descripcion IS NULL)"
            params.append(tuple(str_descripcion))

        query += " GROUP BY  s.str_CodSolicitudes, s.dt_FechaRegistro ORDER BY  s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                     'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'int_idSolicitudes': row[17],
                    'Fecha Fin': row[18],
                    'Moneda': row[19],
                    'Penalidades': row[20],
                    'Clausula de no competencia': row[21],
                    'idEmpresa': row[22]
        
                }
                for row in rows
            ]
        return Response(solicitudes, status=status.HTTP_200_OK)

class BusquedaRegistroComprimir(APIView):
    def get(self, request):
        try:
            anio_fecha_registro = request.query_params.get('anio_fecha_registro')
            anio_fecha_Firma = request.query_params.get('anio_fecha_Firma')
            anio_fecha_fin = request.query_params.get('anio_fecha_fin')
            int_idGestor = request.query_params.get('int_idGestor')
            str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
            str_idSuscriptor = request.query_params.get('str_idSuscriptor')
            str_descripcion = request.query_params.getlist('str_descripcion')
            nombre_asociado = request.query_params.get('nombre_asociado')

            if not str_idSuscriptor:
                return Response(
                    {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                    status=status.HTTP_400_BAD_REQUEST
                )

            query = """
                SELECT
                    s.str_CodSolicitudes
                FROM
                    tr_solicitudes s
                LEFT JOIN
                    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
                LEFT JOIN
                    tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud
                LEFT JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                LEFT JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                LEFT JOIN
                    tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios
                LEFT JOIN
                    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes
                LEFT JOIN
                    tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes
                LEFT JOIN
                    tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
                LEFT JOIN
                    tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor
                LEFT JOIN
                    tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes
                LEFT JOIN
                    tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
                LEFT JOIN
                    tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales
                WHERE
                    s.str_idSuscriptor = %s AND es.str_Nombre = "Firmado"
            """
            params = [str_idSuscriptor]

            if anio_fecha_registro:
                query += " AND YEAR(s.dt_FechaRegistro) = %s"
                params.append(anio_fecha_registro)

            if anio_fecha_Firma:
                query += " AND YEAR(s.dt_FirmaContrato) = %s"
                params.append(anio_fecha_Firma)

            if anio_fecha_fin:
                query += " AND YEAR(s.dt_FechaFin) = %s"
                params.append(anio_fecha_fin)

            if int_idGestor:
                query += " AND s.int_idGestor = %s"
                params.append(int_idGestor)

            if nombre_asociado:
                query += " AND u.str_Interlocutor = %s"
                params.append(nombre_asociado)

            if str_NombreTipoSolicitud:
                query += " AND ts.str_Nombre = %s"
                params.append(str_NombreTipoSolicitud)

            if str_descripcion and len(str_descripcion) > 0:
                query += " AND (t.str_descripcion IN %s OR t.str_descripcion IS NULL)"
                params.append(tuple(str_descripcion))

            query += " GROUP BY s.str_CodSolicitudes ORDER BY MAX(s.dt_FechaRegistro) DESC;"

            with connection.cursor() as cursor:
                cursor.execute(query, params)
                rows = cursor.fetchall()

                if not rows:
                    return Response(
                        {"error": "No se encontraron registros para los parámetros proporcionados."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            # Crear directorio temporal para los archivos
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            import tempfile
            temp_files_dir = tempfile.mkdtemp(dir=temp_dir)

            # Crear nombre de archivo seguro
            safe_id = str_idSuscriptor.replace('$', '_').replace('/', '_').replace('\\', '_')
            periodo = ""
            if anio_fecha_fin:
                periodo = f"_{anio_fecha_fin}"
            elif anio_fecha_Firma:
                periodo = f"_{anio_fecha_Firma}"
            elif anio_fecha_registro:
                periodo = f"_{anio_fecha_registro}"

            zip_filename = f"archivos_firmados_{safe_id}{periodo}.zip"
            zip_path = os.path.join(temp_dir, zip_filename)

            # Eliminar archivo existente
            if os.path.exists(zip_path):
                os.remove(zip_path)

            archivos_agregados = 0

            # Crear el archivo ZIP directamente sin copiar archivos
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for row in rows:
                    str_CodSolicitudes = row[0]
                    cofi_dir = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, "COFI")

                    if os.path.exists(cofi_dir) and os.path.isdir(cofi_dir):
                        archivos = []
                        for f in os.listdir(cofi_dir):
                            file_path = os.path.join(cofi_dir, f)
                            if os.path.isfile(file_path):
                                archivos.append(file_path)

                        if archivos:
                            archivo_mas_reciente = max(archivos, key=os.path.getmtime)
                            try:
                                if os.path.exists(archivo_mas_reciente) and os.access(archivo_mas_reciente, os.R_OK):
                                    nombre_archivo = f"{str_CodSolicitudes}_{os.path.basename(archivo_mas_reciente)}"
                                    # Agregar directamente al ZIP sin copiar
                                    zip_file.write(archivo_mas_reciente, nombre_archivo)
                                    archivos_agregados += 1
                            except Exception as e:
                                print(f"Error al agregar archivo {archivo_mas_reciente}: {str(e)}")

            # Verificar si se agregaron archivos
            if archivos_agregados == 0:
                if os.path.exists(zip_path):
                    os.remove(zip_path)
                return Response(
                    {"error": "No se encontraron archivos válidos para comprimir."},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Verificar que el ZIP se creó correctamente
            if not os.path.exists(zip_path) or os.path.getsize(zip_path) == 0:
                return Response(
                    {"error": "Error al crear el archivo ZIP."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            # Enviar el archivo como respuesta
            with open(zip_path, 'rb') as f:
                response = HttpResponse(
                    f.read(),
                    content_type='application/zip'
                )
                response['Content-Disposition'] = f'attachment; filename="{zip_filename}"'
                response['Content-Length'] = os.path.getsize(zip_path)

            # Eliminar el archivo después de enviarlo
            os.remove(zip_path)

            return response

        except Exception as e:
            import traceback
            print(traceback.format_exc())
            return Response(
                {"error": "Ocurrió un error al procesar la solicitud.", "detalle": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class SolicitudesPorFinalizar(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
             SELECT 
                s.str_CodSolicitudes, 
                MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
                MAX(sc.db_Presupuesto) AS db_Presupuesto,
                MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
                MAX(un.str_Descripcion) AS str_Descripcion,
                MAX(es.str_Nombre) AS estado_nombre,
                MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
                MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
                MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
                MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
                MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
                MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
                MAX(sc.str_Garantia) AS str_Garantia,
                MAX(s.db_Honorarios) AS db_Honorarios,
                MAX(sc.str_Margen) AS str_Margen,
                MAX(sc.str_FormaPago) AS str_FormaPago,
                MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
                MAX(s.int_idSolicitudes) AS int_idSolicitudes,
                MAX(s.dt_FechaFin) AS dt_FechaFin,
                MAX(
                        CASE 
                            WHEN sc.str_Moneda = 'dolares' THEN 'Dolares'
                            ELSE e.str_Moneda
                        END
                    ) AS str_Moneda,
                MAX(sc.str_Penalidades) AS str_Penalidades,
                MAX(CASE 
                    WHEN clale.str_Nombre = 'Clausula de no competencia' THEN 'Sí'
                    ELSE NULL
                END) AS Clausula,
                MAX(e.int_idEmpresa) AS int_idEmpresa
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
            LEFT JOIN 
                tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
            LEFT JOIN 
                tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
            LEFT JOIN 
                tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
            LEFT JOIN 
                tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
            LEFT JOIN 
                tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
            LEFT JOIN 
                tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
            WHERE  
                s.str_idSuscriptor = %s
                AND s.dt_FechaFin >= CURDATE()  
                AND YEAR(s.dt_FechaFin) = %s 
            GROUP BY s.str_CodSolicitudes, s.dt_FechaRegistro
            ORDER BY s.dt_FechaRegistro DESC;
        """
        params = [ str_idSuscriptor,año_filtro]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'int_idSolicitudes': row[17],
                    'Fecha Fin': row[18],
                    'Moneda': row[19],
                    'Penalidades': row[20],
                    'Clausula de no competencia': row[21],
                    'idEmpresa': row[22]
                }
                for row in rows
            ]
        return Response(solicitudes)
    
class SolicitudesVencidas(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
             SELECT 
                s.str_CodSolicitudes, 
                MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
                MAX(sc.db_Presupuesto) AS db_Presupuesto,
                MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
                MAX(un.str_Descripcion) AS str_Descripcion,
                MAX(es.str_Nombre) AS estado_nombre,
                MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
                MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
                MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
                MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
                MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
                MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
                MAX(sc.str_Garantia) AS str_Garantia,
                MAX(s.db_Honorarios) AS db_Honorarios,
                MAX(sc.str_Margen) AS str_Margen,
                MAX(sc.str_FormaPago) AS str_FormaPago,
                MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
                MAX(s.int_idSolicitudes) AS int_idSolicitudes,
                MAX(s.dt_FechaFin) AS dt_FechaFin,
                MAX(
                        CASE 
                            WHEN sc.str_Moneda = 'dolares' THEN 'Dolares'
                            ELSE e.str_Moneda
                        END
                    ) AS str_Moneda,
                MAX(sc.str_Penalidades) AS str_Penalidades,
                MAX(CASE 
                    WHEN clale.str_Nombre = 'Clausula de no competencia' THEN 'Sí'
                    ELSE NULL
                END) AS Clausula,
                MAX(e.int_idEmpresa) AS int_idEmpresa
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
            LEFT JOIN 
                tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
            LEFT JOIN 
                tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
            LEFT JOIN 
                tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
            LEFT JOIN 
                tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
            LEFT JOIN 
                tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
            LEFT JOIN 
                tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
            WHERE  
                s.str_idSuscriptor = %s
                AND s.dt_FechaFin < CURDATE()  
                AND YEAR(s.dt_FechaFin) = %s 
            GROUP BY s.str_CodSolicitudes, s.dt_FechaRegistro
            ORDER BY s.dt_FechaRegistro DESC;
        """
        params = [ str_idSuscriptor,año_filtro]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'int_idSolicitudes': row[17],
                    'Fecha Fin': row[18],
                    'Moneda': row[19],
                    'Penalidades': row[20],
                    'Clausula de no competencia': row[21],
                    'idEmpresa': row[22]
                }
                for row in rows
            ]
        return Response(solicitudes)
class SolicitudesArchivosComprimir(APIView):
    def get(self, request):
        try:
            # Obtener parámetros de la solicitud
            str_idSuscriptor = request.query_params.get('str_idSuscriptor')
            año_filtro = request.query_params.get('year')
            tipoFiltro  = request.query_params.get('tipoFiltro')
            if not str_idSuscriptor:
                return Response(
                    {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                    status=status.HTTP_400_BAD_REQUEST
                )
            if tipoFiltro == '<':
                operador = '<'
            elif tipoFiltro == '>':
                operador = '>'
            else:
                return Response(
                    {"error": "El parámetro 'tipoFiltro' debe ser 'vencidas' o 'vigentes'."},
                    status=status.HTTP_400_BAD_REQUEST
            )
            # Consulta SQL
            query = f"""
                SELECT
                    s.str_CodSolicitudes
                FROM
                    tr_solicitudes s
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.dt_FechaFin {operador} CURDATE()
                    AND YEAR(s.dt_FechaFin) = %s
                GROUP BY
                    s.str_CodSolicitudes
                ORDER BY
                    MAX(s.dt_FechaRegistro) DESC;
            """
            params = [str_idSuscriptor, año_filtro]

            with connection.cursor() as cursor:
                cursor.execute(query, params)
                rows = cursor.fetchall()

            if not rows:
                return Response(
                    {"error": "No se encontraron solicitudes vencidas para los parámetros proporcionados."},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Crear directorio temporal para los archivos
            temp_dir = os.path.join(settings.MEDIA_ROOT, 'temp')
            os.makedirs(temp_dir, exist_ok=True)

            # Crear un directorio temporal para los archivos a comprimir
            import tempfile
            temp_files_dir = tempfile.mkdtemp(dir=temp_dir)

            # Crear un archivo ZIP con nombre seguro (sin caracteres especiales)
            safe_id = str_idSuscriptor.replace('$', '_').replace('/', '_').replace('\\', '_')
            zip_filename = f"archivos_firmados_{safe_id}_{año_filtro}.zip"
            zip_path = os.path.join(temp_dir, zip_filename)

            # Eliminar el archivo si ya existe
            if os.path.exists(zip_path):
                os.remove(zip_path)

            archivos_agregados = 0
            archivos_copiados = []

            # Primero, copiar los archivos a comprimir al directorio temporal
            for row in rows:
                str_CodSolicitudes = row[0]

                # Construir la ruta de la carpeta COFI
                cofi_dir = os.path.join(settings.MEDIA_ROOT, str_idSuscriptor, str_CodSolicitudes, "COFI")

                if os.path.exists(cofi_dir) and os.path.isdir(cofi_dir):
                    # Obtener todos los archivos en la carpeta COFI
                    archivos = []
                    for f in os.listdir(cofi_dir):
                        file_path = os.path.join(cofi_dir, f)
                        if os.path.isfile(file_path):
                            archivos.append(file_path)

                    if archivos:
                        # Obtener el archivo más reciente
                        archivo_mas_reciente = max(archivos, key=os.path.getmtime)

                        try:
                            # Verificar que el archivo existe y es accesible
                            if os.path.exists(archivo_mas_reciente) and os.access(archivo_mas_reciente, os.R_OK):
                                # Nombre seguro para el archivo
                                nombre_archivo = f"{str_CodSolicitudes}_{os.path.basename(archivo_mas_reciente)}"
                                # Copiar el archivo al directorio temporal
                                destino = os.path.join(temp_files_dir, nombre_archivo)
                                import shutil
                                shutil.copy2(archivo_mas_reciente, destino)
                                archivos_copiados.append(destino)
                                archivos_agregados += 1
                                print(f"Archivo copiado: {nombre_archivo}")
                        except Exception as e:
                            print(f"Error al copiar archivo {archivo_mas_reciente}: {str(e)}")
                    else:
                        print(f"No se encontraron archivos en la carpeta: {cofi_dir}")
                else:
                    print(f"La carpeta no existe o no es válida: {cofi_dir}")

            # Verificar si se copiaron archivos
            if archivos_agregados == 0:
                # Limpiar directorio temporal
                import shutil
                shutil.rmtree(temp_files_dir, ignore_errors=True)
                return Response(
                    {"error": "No se encontraron archivos válidos para comprimir."},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Crear el archivo ZIP
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for archivo in archivos_copiados:
                    nombre_archivo = os.path.basename(archivo)
                    zip_file.write(archivo, nombre_archivo)

            # Limpiar directorio temporal de archivos copiados
            import shutil
            shutil.rmtree(temp_files_dir, ignore_errors=True)

            # Verificar que el ZIP se creó correctamente
            if not os.path.exists(zip_path) or os.path.getsize(zip_path) == 0:
                return Response(
                    {"error": "Error al crear el archivo ZIP."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            # Enviar el archivo como respuesta
            response = FileResponse(
                open(zip_path, 'rb'),
                content_type='application/zip'
            )
            response['Content-Disposition'] = f'attachment; filename="{zip_filename}"'
            response['Content-Length'] = os.path.getsize(zip_path)

            return response

        except Exception as e:
            # Capturar cualquier excepción y devolver un mensaje de error
            import traceback
            print(traceback.format_exc())
            return Response(
                {"error": "Ocurrió un error al procesar la solicitud.", "detalle": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
class SolicitudesPorFinalizarGestor(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        fecha_actual = datetime.now()
        fecha_limite = (fecha_actual + timedelta(days=90)).replace(day=1)
        ultimo_dia = (fecha_limite + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        query = """
            SELECT
                s.str_CodSolicitudes,
                MAX(s.dt_FechaFin) AS dt_FechaFin,
                MAX(sc.dt_FechaRenAut) AS dt_FechaRenAut
            FROM
                tr_solicitudes s
           LEFT JOIN
                tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes
            WHERE
                s.str_idSuscriptor = %s
                AND s.dt_FechaFin BETWEEN CURDATE() AND %s
                AND s.int_idGestor = %s
            GROUP BY s.str_CodSolicitudes
            ORDER BY MAX(s.dt_FechaFin) ASC;
        """
        params = [str_idSuscriptor, ultimo_dia.strftime('%Y-%m-%d'), int_idGestor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

        # Verificar si hay solicitudes
        if not rows:
            return Response(
                {"message": "No hay solicitudes por vencer en los próximos tres meses."},
                status=status.HTTP_200_OK
            )

        table_rows = ""
        for row in rows:
            cod_solicitud = row[0]
            fecha_fin_obj = datetime.strptime(str(row[1]).split(" ")[0], "%Y-%m-%d")
            fecha_fin = format_date_es(fecha_fin_obj)
            download_url = f"https://ransa.prisma-legal.pe/Apis/Contratos/api/archivos/descargar-archivo/?str_idSuscriptor={str_idSuscriptor}&str_CodSolicitudes={cod_solicitud}&str_CodTipoDocumento=COFI"
            if row[2]:
                dt_FechaRenAut_obj = datetime.strptime(str(row[2]).split(" ")[0], "%Y-%m-%d")
                dt_FechaRenAut = format_date_es(dt_FechaRenAut_obj)
            else:
                dt_FechaRenAut = "No tiene renovación automática"
            table_rows += f"""
                <tr>
                    <td style="text-align: center;">{cod_solicitud}</td>
                    <td style="text-align: center;">{fecha_fin}</td>
                    <td style="text-align: center;">{dt_FechaRenAut}</td>
                    <td style="text-align: center; padding: 1rem;">
                        <a href="{download_url}" target="_blank"
                        style="background-color: #414d75; color: #fff; padding: 8px 12px; text-decoration: none;
                                border-radius: 4px; display: inline-block; text-align: center; border: none;">
                            <span style="font-size: 10px; font-family: Arial, sans-serif;">Descargar</span>
                        </a>
                    </td>
                </tr>
            """

        html_content = f"""
            <html>
            <body>
                <p>Hola Rodolfo Rebagliati, tienes los siguientes contratos por vencer en los siguientes 3 meses:</p>
                <table border="3" style="border-collapse: collapse; width: 80%; background-color:#fff; border: 0.0625rem solid #ddd; border-radius: 0.625rem; box-shadow:0 4px 4px #00000040;">
                    <thead style="background-color: #f5f5f5; color: #000;">
                        <tr>
                            <th style="padding:1rem;">Código de Contrato</th>
                            <th style="padding:1rem;">Fecha de Vencimiento</th>
                            <th style="padding:1rem;">Fecha de Vencimiento tras renovación automática</th>
                            <th style="padding:1rem;">Descargar Contrato</th>
                        </tr>
                    </thead>
                    <tbody>
                        {table_rows}
                    </tbody>
                </table>
            </body>
            </html>
            """

        try:
            # Enviar correo con tabla
            message = Mail(
                from_email=('<EMAIL>', 'Soporte - Prisma Legal'),
                to_emails='<EMAIL>',
                subject='Prisma Legal - Solicitud Pendiente de Aprobar',
                html_content=html_content
            )
            sg = SendGridAPIClient('*********************************************************************')
            response = sg.send(message)

            print(f"SendGrid response: {response.status_code}, {response.body}, {response.headers}")
        except Exception as e:
            return Response({"error": f"Error al enviar el correo: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Devolver los datos al cliente
        solicitudes = [{"CodSolicitud": row[0], "FechaFin": row[1]} for row in rows]
        return Response(solicitudes, status=status.HTTP_200_OK)
        
class DataBot(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')
        año_filtro = request.query_params.get('year')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
SELECT 
                    s.str_CodSolicitudes, 
                    MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
                     MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(sc.db_Presupuesto, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(sc.db_Presupuesto, 2))
    END
    ) AS db_Presupuesto,
                    MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
                    MAX(un.str_Descripcion) AS str_Descripcion,
                    MAX(es.str_Nombre) AS estado_nombre,
                    MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
                    MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
                    MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
                    MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
                    MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
                    MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
                    MAX(sc.str_Garantia) AS str_Garantia,
                    MAX(
    CASE 
        WHEN sc.str_Moneda = 'dolares' THEN CONCAT('$ ', FORMAT(s.db_Honorarios, 2))
        ELSE CONCAT(e.str_SimboloMoneda, ' ', FORMAT(s.db_Honorarios, 2))
    END
    ) AS db_Honorarios,
                    MAX(sc.str_Margen) AS str_Margen,
                    MAX(sc.str_FormaPago) AS str_FormaPago,
                    MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
                    MAX(s.int_HorasTrabajadas) AS int_HorasTrabajadas,
                    MAX(s.int_idSolicitudes) AS int_idSolicitudes,
                    MAX(s.dt_FechaFin) AS dt_FechaFin,
                    MAX(
                        CASE 
                            WHEN sc.str_Moneda = 'dolares' THEN 'Dolares'
                            ELSE e.str_Moneda
                        END
                    ) AS str_Moneda,
                    MAX(sc.str_Penalidades) AS str_Penalidades,
                    MAX(CASE 
                        WHEN clale.str_Nombre = 'Clausula de no competencia' THEN 'Sí'
                        ELSE NULL
                    END) AS Clausula
                FROM 
                    tr_solicitudes s
                LEFT JOIN 
                    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
                LEFT JOIN 
                    tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
                LEFT JOIN 
                    tm_estados es ON s.int_idEstado = es.int_idEstado 
                LEFT JOIN 
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
                LEFT JOIN 
                    tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
                LEFT JOIN 
                    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
                LEFT JOIN 
                    tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
                LEFT JOIN 
                    tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
                LEFT JOIN 
                    tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
                LEFT JOIN 
                    tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
                LEFT JOIN 
                    tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales               
WHERE  
    (s.int_idGestor = %s OR s.int_idSolicitante = %s) 
    AND s.str_idSuscriptor = %s
GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro,s.int_idSolicitudes  
ORDER BY s.dt_FechaRegistro DESC ;

"""
        

        params = [int_idGestor,int_idGestor, str_idSuscriptor]

 


        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                    'Moneda': row[20],
                    'Penalidades': row[21],
                    'Clausula de no competencia': row[22]
                }
                for row in rows
            ]
        return Response(solicitudes)
