from rest_framework import serializers

class TipSolicitudSerializer(serializers.Serializer):
    str_CodTipoSol  = serializers.CharField(max_length=4)
    str_idSuscripcion = serializers.CharField(max_length=50)
    str_Nombre = serializers.CharField(max_length=150)
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()

class TipSolicitudUpdateSerializer(serializers.Serializer):
    str_CodTipoSol  = serializers.CharField(max_length=4)
    str_Nombre = serializers.CharField(max_length=150)
    int_idUsuarioModificacion = serializers.IntegerField()