from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import ConsorcioSerializer,AsignacionSerializer,ConsorcioEditarSerializer

class ConsorciosCreate(APIView):
    def post(self, request):
        serializer = ConsorcioSerializer(data=request.data)

        if serializer.is_valid():
            int_idInterlocutor = serializer.validated_data.get('int_idInterlocutor')
            str_Obligacion = serializer.validated_data.get('str_Obligacion')
            int_idUsuarioCreacion = serializer.validated_data.get('int_idUsuarioCreacion')
            str_ValorAporte = serializer.validated_data.get('str_ValorAporte', None)
            str_PorcentajeAporte = serializer.validated_data.get('str_PorcentajeAporte', None)
            str_ValorServicios = serializer.validated_data.get('str_ValorServicios', None)
            str_ValorHonorarios = serializer.validated_data.get('str_ValorHonorarios', None)


            with connection.cursor() as cursor:
                query = """
                INSERT INTO tr_consorcio 
                (int_idInterlocutor, str_Obligacion, dt_FechaCreacion, int_idUsuarioCreacion,
                str_ValorAporte, str_PorcentajeAporte, str_ValorServicios, str_ValorHonorarios)
                VALUES (%s, %s, NOW(), %s, %s, %s, %s, %s)
                """
                cursor.execute(query, [
                    int_idInterlocutor,
                    str_Obligacion,
                    int_idUsuarioCreacion,
                    str_ValorAporte,
                    str_PorcentajeAporte,
                    str_ValorServicios,
                    str_ValorHonorarios
                ])

                consorcio_id = cursor.lastrowid  

            return Response({'id': consorcio_id, **serializer.data}, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ConsorcioEditar(APIView):
    def put(self, request):
        serializer = ConsorcioEditarSerializer(data=request.data)

        if serializer.is_valid():
            int_idInterlocutor = serializer.validated_data['int_idInterlocutor']
            int_idSolicitudCont = serializer.validated_data['int_idSolicitudCont']  
            
            with connection.cursor() as cursor:
                query = """
                UPDATE tr_consorcio AS c
                INNER JOIN tr_consorcioporsolicitud AS cs ON c.int_idConsorcio  = cs.int_idConsorcio 
                SET c.str_ValorAporte=%s, c.str_PorcentajeAporte=%s, c.str_ValorServicios=%s, c.str_ValorHonorarios=%s, c.dt_FechaModificacion=NOW(), c.int_idUsuarioModificacion=%s
                WHERE c.int_idInterlocutor = %s AND cs.int_idSolicitudCont  = %s
                """
                cursor.execute(query, [
                    serializer.validated_data['str_ValorAporte'],
                    serializer.validated_data['str_PorcentajeAporte'],
                    serializer.validated_data['str_ValorServicios'],
                    serializer.validated_data['str_ValorHonorarios'],
                    serializer.validated_data['int_idUsuarioModificacion'],
                    int_idInterlocutor,
                    int_idSolicitudCont
                ])

            return Response({'id': int_idInterlocutor, **serializer.data}, status=status.HTTP_200_OK)  

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
class ConsorcioEliminar(APIView):
    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tr_consorcio WHERE int_idInterlocutor  = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class ListarConsorcioporSolicitud(APIView):
    def get(self, request):
        int_idSolicitudCont = request.query_params.get('int_idSolicitudCont')

        if not int_idSolicitudCont:
            return Response(
                {"error": "El parámetro 'int_idSolicitudCont' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT cps.int_idConsorcioPorSolicitud, cps.int_idSolicitudCont, 
                   cps.dt_FechaCreacion, cps.int_idUsuarioCreacion, 
                   c.int_idConsorcio, c.str_NombreConsorcio, c.str_Obligacion,
                   i.int_idInterlocutor, i.str_RazonSocial, i.str_TipoDoc, i.str_RepLegal,
                   i.str_Correo, i.str_Documento, i.str_Domicilio, i.int_RLPartida, i.str_obligaciones,
                   c.str_ValorAporte, c.str_PorcentajeAporte, c.str_ValorServicios, c.str_ValorHonorarios
            FROM tr_consorcioporsolicitud cps
            JOIN tr_consorcio c ON cps.int_idConsorcio = c.int_idConsorcio
            JOIN tm_interlocutores i ON c.int_idInterlocutor = i.int_idInterlocutor
            WHERE cps.int_idSolicitudCont = %s
        """
        params = [int_idSolicitudCont]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            consorcios_por_solicitud = [
                {
                    'int_idConsorcioPorSolicitud': row[0],
                    'int_idSolicitudCont': row[1],
                    'dt_FechaCreacion': row[2],
                    'int_idUsuarioCreacion': row[3],
                    'int_idConsorcio': row[4],
                    'str_NombreConsorcio': row[5],
                    'str_Obligacion': row[6],
                    'int_idInterlocutor': row[7],
                    'str_RazonSocial': row[8],
                    'str_TipoDoc': row[9],
                    'str_RepLegal': row[10],
                    'str_Correo': row[11],
                    'str_Documento': row[12],
                    'str_Domicilio': row[13],
                    'int_RLPartida': row[14],
                    'str_obligaciones': row[15],
                    'str_ValorAporte': row[16],
                    'str_PorcentajeAporte': row[17],
                    'str_ValorServicios': row[18],
                    'str_ValorHonorarios': row[19]
                }
                for row in rows
            ]
        
        return Response(consorcios_por_solicitud, status=status.HTTP_200_OK)

class ConsorciosSolicitud(APIView):
    def post(self, request):
        serializer = AsignacionSerializer(data=request.data)

        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tr_consorcioporsolicitud 
                (int_idConsorcio ,int_idSolicitudCont,dt_FechaCreacion,	int_idUsuarioCreacion)
                VALUES ( %s,%s,NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['int_idConsorcio'],
                    serializer.validated_data['int_idSolicitudCont'],
                    serializer.validated_data['int_idUsuarioCreacion']
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class EliminarAsociado(APIView):
    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tr_consorcioporsolicitud WHERE int_idSolicitudCont   = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)