-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 18-09-2024 a las 23:08:17
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `prisma_desarrollo`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tc_empresas`
--

CREATE TABLE `tc_empresas` (
  `int_idEmpresa` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tc_empresas`
--

INSERT INTO `tc_empresas` (`int_idEmpresa`, `str_idSuscripcion`, `str_NombreEmpresa`, `str_RazonSocial`, `str_Ruc`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, '1346798255', 'MERCADO VIRTUAL PERU S.A.C.', 'Empresa de tegnología', '104569874561', NULL, NULL, NULL, NULL),
(3, '1346798255', 'Lauren David', 'Empresa de prueba', '102354798121', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_clausulaslegales`
--

CREATE TABLE `tm_clausulaslegales` (
  `int_idClausulasLegales` int NOT NULL,
  `str_CodClausulasLegales` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_clausulaslegales`
--

INSERT INTO `tm_clausulaslegales` (`int_idClausulasLegales`, `str_CodClausulasLegales`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, 'CLA2', '1346798255', 'Cláusula de indemnidad', '2024-09-12 17:26:26', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_especialidades`
--

CREATE TABLE `tm_especialidades` (
  `int_idEspecialidades` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodigoEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_estados`
--

CREATE TABLE `tm_estados` (
  `int_idEstado` int NOT NULL,
  `str_codEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_estados`
--

INSERT INTO `tm_estados` (`int_idEstado`, `str_codEstado`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'E001', '1346798255', 'Nuevo', '2024-09-06 17:15:43', '2024-09-06 17:18:51', 1, 1),
(2, 'E002', '1346798255', 'Asignado', '2024-09-18 12:14:10', NULL, 1, NULL),
(3, 'E003', '1346798255', 'En Proceso', '2024-09-13 16:06:24', NULL, 1, NULL),
(4, 'E004', '1346798255', 'En Validacion', '2024-09-18 12:14:10', NULL, 1, NULL),
(5, 'E005', '1346798255', 'Aceptado', '2024-09-18 12:15:08', NULL, 1, NULL),
(6, 'E006', '1346798255', 'En Aprobacion', '2024-09-18 12:18:15', NULL, 1, NULL),
(7, 'E007', '1346798255', 'Aprobado', '2024-09-18 12:18:15', NULL, 1, NULL),
(8, 'E008', '1346798255', 'Firmado', '2024-09-18 12:19:04', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_interlocutores`
--

CREATE TABLE `tm_interlocutores` (
  `int_idInterlocutor` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_TipoDoc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Documento` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Domicilio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Correo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_obligaciones` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_ValorAporte` int DEFAULT NULL,
  `int_PorcentajeAporte` float DEFAULT NULL,
  `str_ValorServicios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ValorHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RepLegal` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_RLPartida` int DEFAULT NULL,
  `str_RLTipoDocumento` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RLDocumento` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_interlocutores`
--

INSERT INTO `tm_interlocutores` (`int_idInterlocutor`, `str_idSuscripcion`, `str_RazonSocial`, `str_TipoDoc`, `str_Documento`, `str_Domicilio`, `str_Correo`, `str_obligaciones`, `int_ValorAporte`, `int_PorcentajeAporte`, `str_ValorServicios`, `str_ValorHonorarios`, `str_RepLegal`, `int_RLPartida`, `str_RLTipoDocumento`, `str_RLDocumento`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, '1346798255', 'Empresa de Ejemplo S.A.', 'RUC', '10456789012', 'Av. Ejemplo 123, Lima, Perú', '<EMAIL>', 'Pago de impuestos', 10000, 29.5, 'Consultoría', '5000', 'Juan Pérez', 456789, NULL, NULL, '2024-09-11 16:45:52', '2024-09-11 17:19:20', 1, NULL),
(3, '1346798255', 'Empresa de Ejemplo S.A.', 'RUC', '10456789012', 'Av. Ejemplo 123, Lima, Perú', '<EMAIL>', 'Pago de impuestos', 10000, 25.5, 'Consultoría', '5000', 'Juan Pérez', 456789, NULL, NULL, '2024-09-11 16:56:18', NULL, 1, NULL),
(4, '1346798255', 'Empresa de Ejemplo S.A.', 'RUC', '10456789012', 'Av. Ejemplo 123, Lima, Perú', '<EMAIL>', 'Pago de impuestos', 10000, 25.5, 'Consultoría', '5000', 'Juan Pérez', 456789, NULL, NULL, '2024-09-11 16:56:19', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_suscripcion`
--

CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_suscripcion`
--

INSERT INTO `tm_suscripcion` (`str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
('1346798255', 'Suscripción Prueba\r\n', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipodocumento`
--

CREATE TABLE `tm_tipodocumento` (
  `int_idTipoDocumentos` int NOT NULL,
  `str_CodTipoDocumento` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tipodocumento`
--

INSERT INTO `tm_tipodocumento` (`int_idTipoDocumentos`, `str_CodTipoDocumento`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, 'TPOD', '1346798255', 'Tipo documento 1', '2024-09-11 09:11:11', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tiposolicitud`
--

CREATE TABLE `tm_tiposolicitud` (
  `int_idTipoSolicitud` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodTipoSol` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tiposolicitud`
--

INSERT INTO `tm_tiposolicitud` (`int_idTipoSolicitud`, `str_idSuscripcion`, `str_CodTipoSol`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, '1346798255', 'CPRS', 'Contrato de prestación de servicios', '2024-09-11 17:30:34', NULL, 1, NULL),
(2, '1346798255', 'CLOS', 'Contrato de locación de servicios', '2024-09-17 15:07:42', NULL, 1, NULL),
(3, '1346798255', 'CPSP', 'Contrato de prestación de servicios profesionales', '2024-09-17 15:07:42', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_usuarios`
--

CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Documento` int NOT NULL,
  `str_UnidadNegocio` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idEspecialidad` int DEFAULT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_Estado` int DEFAULT '1',
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_usuarios`
--

INSERT INTO `tm_usuarios` (`int_idUsuarios`, `str_Nombres`, `str_Apellidos`, `str_Correo`, `int_Documento`, `str_UnidadNegocio`, `int_idEspecialidad`, `str_Clave`, `int_Estado`, `str_Codigo`, `dt_FechaCreacion`, `dt_FechaModificacion`, `str_idUsuarioCreacion`, `str_idUsuarioModificacion`) VALUES
(1, 'Jose Maria', 'Torres Chirinos', '<EMAIL>', 2, 'wfwfw', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(24, 'Lauren David', 'Arica guerrero', '<EMAIL>', 12346785, '<EMAIL>', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL),
(26, 'Nicole Alexa', 'Alva ', '<EMAIL>', 14789632, 'Unidad de Negocio', NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_aprobadores`
--

CREATE TABLE `tr_aprobadores` (
  `int_idAprobador` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaAceptacion` datetime NOT NULL,
  `int_OrdenAprobacion` tinyint(1) NOT NULL,
  `int_EstadoAprobacion` tinyint(1) NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_archivosdocumentos`
--

CREATE TABLE `tr_archivosdocumentos` (
  `int_idArchivos` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idTipoDocumento` int NOT NULL,
  `str_RutaArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ExtencionArchivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_TamañoArchivo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_clausulasactivas`
--

CREATE TABLE `tr_clausulasactivas` (
  `int_idClausulasActivas` int NOT NULL,
  `str_idSuscripcion` int NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `int_idClausulaLegal` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_clausulasincluidas`
--

CREATE TABLE `tr_clausulasincluidas` (
  `int_idClausulasIncluidas` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `int_idClausulaLegal` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_clausulasincluidas`
--

INSERT INTO `tr_clausulasincluidas` (`int_idClausulasIncluidas`, `str_idSuscripcion`, `int_idTipoSolicitud`, `int_idClausulaLegal`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, '1346798255', 1, 2, '2024-09-12 17:27:45', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_consorcio`
--

CREATE TABLE `tr_consorcio` (
  `int_idConsorcio` int NOT NULL,
  `str_idCorrelativo` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idInterlocutor` int DEFAULT NULL,
  `str_NombreConsorcio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Oblicacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_CantidadObligacion` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime NOT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_consorcioporsolicitud`
--

CREATE TABLE `tr_consorcioporsolicitud` (
  `int_idConsorcioPorSolicitud` int NOT NULL,
  `int_idConsorcio` int NOT NULL,
  `int_idSolicitudCont` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_correlativosolicitud`
--

CREATE TABLE `tr_correlativosolicitud` (
  `int_idCorrelativo` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idTipoSolicitud` int NOT NULL,
  `str_CorrelativoTipoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_estadoregistros`
--

CREATE TABLE `tr_estadoregistros` (
  `int_idEstadoRegistros` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idEstado` int NOT NULL,
  `dt_FechaCambio` datetime NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_estadoregistros`
--

INSERT INTO `tr_estadoregistros` (`int_idEstadoRegistros`, `str_idSuscriptor`, `int_idSolicitudes`, `int_idEstado`, `dt_FechaCambio`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, '1346798255', 1, 1, '2024-09-11 17:40:03', '2024-09-11 17:40:03', NULL, 1, NULL),
(2, '1346798255', 1, 3, '2024-09-12 16:39:36', '2024-09-12 16:39:36', NULL, 1, NULL),
(3, '1346798255', 1, 1, '2024-09-18 14:32:35', '2024-09-18 14:32:35', NULL, 1, NULL),
(4, '1346798255', 1, 2, '2024-09-18 14:50:04', '2024-09-18 14:50:04', NULL, 1, NULL),
(5, '1346798255', 1, 2, '2024-09-18 14:51:07', '2024-09-18 14:51:07', NULL, 1, NULL),
(6, '1346798255', 4, 1, '2024-09-18 15:22:41', '2024-09-18 15:22:41', '2024-09-18 15:22:41', 1, NULL),
(7, '1346798255', 4, 2, '2024-09-18 20:26:33', '2024-09-18 15:26:33', NULL, 1, NULL),
(8, '1346798255', 4, 5, '2024-09-18 16:26:47', '2024-09-18 16:26:47', NULL, 1, NULL),
(9, '1346798255', 1, 2, '2024-09-18 16:37:00', '2024-09-18 16:37:00', NULL, 1, NULL),
(10, '1346798255', 4, 5, '2024-09-18 16:37:15', '2024-09-18 16:37:15', NULL, 1, NULL),
(11, '1346798255', 4, 5, '2024-09-18 16:38:03', '2024-09-18 16:38:03', NULL, 1, NULL),
(12, '1346798255', 1, 2, '2024-09-18 16:40:41', '2024-09-18 16:40:41', NULL, 1, NULL),
(13, '1346798255', 4, 5, '2024-09-18 16:40:50', '2024-09-18 16:40:50', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_solicitudcont`
--

CREATE TABLE `tr_solicitudcont` (
  `int_idSolicitudCont` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `int_idInterlocutor` int NOT NULL,
  `int_idInterlocutorComprador` int NOT NULL,
  `int_NumAsociados` int NOT NULL,
  `int_Honorarios` double NOT NULL,
  `str_Moneda` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Presupuesto` double NOT NULL,
  `str_Margen` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_PlazoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_TIpoServicio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_InfoAdicional` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DocAdjuntos` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ConsultorAsignado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RenovacionAuto` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleRenovAuto` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_AjusteHonorarios` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DetalleAjusteHonorarios` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Garantia` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DetalleGarantia` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ResolucionAnticipada` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetalleResolucionAnticipada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Penalidades` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_DetallePenalidades` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienMuebleInmueble` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienPartidaCertificada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDireccion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienUso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_BienDescripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObjetoContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_MonedaContrato` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_RentaPactada` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ImporteVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_FormaPago` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_PlazoArriendo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaArriendo` datetime DEFAULT NULL,
  `dt_FechaVenta` datetime DEFAULT NULL,
  `str_InteresRetraso` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ObligacionesConjuntas` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_GarantiaVenta` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DetalleGarantiaVenta` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_InfoCompartida` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_solicitudes`
--

CREATE TABLE `tr_solicitudes` (
  `int_idSolicitudes` int NOT NULL,
  `str_CodSolicitudes` varchar(17) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idSolicitante` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idEmpresa` int NOT NULL,
  `int_idEstado` int NOT NULL,
  `int_idTipoSol` int NOT NULL,
  `str_UnidadNegocio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DeTerceros` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `db_Honorarios` double DEFAULT NULL,
  `str_Visible` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaRegistro` datetime NOT NULL,
  `dt_FechaEsperada` datetime NOT NULL,
  `int_idGestor` int DEFAULT NULL,
  `int_idClienteAsociado` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_solicitudes`
--

INSERT INTO `tr_solicitudes` (`int_idSolicitudes`, `str_CodSolicitudes`, `int_idSolicitante`, `str_idSuscriptor`, `int_idEmpresa`, `int_idEstado`, `int_idTipoSol`, `str_UnidadNegocio`, `str_DeTerceros`, `db_Honorarios`, `str_Visible`, `dt_FechaRegistro`, `dt_FechaEsperada`, `int_idGestor`, `int_idClienteAsociado`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'COD1-12345678', 1, '1346798255', 2, 2, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', 26, 3, '2024-09-11 17:38:45', '2024-09-18 16:40:41', 1, 1),
(2, 'COD1-12345678', 1, '1346798255', 2, 2, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', 24, 3, '2024-09-11 17:38:45', '2024-09-12 16:39:36', 1, 1),
(3, 'COD1-12345678', 1, '1346798255', 2, 3, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', 24, 3, '2024-09-11 17:38:45', '2024-09-12 16:39:36', 1, 1),
(4, 'COD1-12345678', 1, '1346798255', 2, 5, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', 24, 3, '2024-09-11 17:38:45', '2024-09-18 16:40:50', 1, 1),
(5, 'COD1-12345678', 24, '1346798255', 2, 5, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', 24, 3, '2024-09-11 17:38:45', '2024-09-12 16:39:36', 1, 1),
(6, 'COD1-12345678', 1, '1346798255', 2, 6, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', 24, 3, '2024-09-11 17:38:45', '2024-09-12 16:39:36', 1, 1),
(7, 'COD1-12345678', 1, '1346798255', 2, 7, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', NULL, 3, '2024-09-11 17:38:45', '2024-09-12 16:39:36', 1, 1),
(8, 'COD1-12345678', 1, '1346798255', 2, 8, 1, 'kkkkk', '', 2500.5, 'si', '2024-09-11 17:38:45', '2024-09-11 22:38:44', 24, 3, '2024-09-11 17:38:45', '2024-09-12 16:39:36', 1, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_tags`
--

CREATE TABLE `tr_tags` (
  `int_idTags` int NOT NULL,
  `int_idSolicitudes` int NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_tags`
--

INSERT INTO `tr_tags` (`int_idTags`, `int_idSolicitudes`, `str_descripcion`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(2, 1, 'admin', '2024-09-11 14:58:23', '2024-09-12 19:58:23', 1, 1),
(3, 1, 'efff', '2024-09-12 20:17:27', '2024-09-12 20:17:27', 1, 1),
(4, 1, 'tag', '2024-09-13 08:56:45', NULL, 1, NULL);

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD PRIMARY KEY (`int_idEmpresa`),
  ADD KEY `empresas_suscriptor_FK` (`str_idSuscripcion`),
  ADD KEY `empresas_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `empresas_usuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  ADD PRIMARY KEY (`int_idClausulasLegales`),
  ADD KEY `clausulasLegales_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `clausulasLegales_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `clausulas_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD PRIMARY KEY (`int_idEspecialidades`),
  ADD KEY `Especialidad_Suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD PRIMARY KEY (`int_idEstado`),
  ADD KEY `Estados_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Estados_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Estados_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD PRIMARY KEY (`int_idInterlocutor`),
  ADD KEY `Interlocutor_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Interlocutor_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Interlocutor_Suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD PRIMARY KEY (`str_idSuscripcion`),
  ADD KEY `Suscripciones_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Suscripciones_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD PRIMARY KEY (`int_idTipoDocumentos`),
  ADD KEY `TipoDocumentos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoDocumentos_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  ADD PRIMARY KEY (`int_idTipoSolicitud`),
  ADD UNIQUE KEY `codigo_tipoSolicitud` (`int_idTipoSolicitud`),
  ADD KEY `TipoSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `TipoSolicitud_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  ADD PRIMARY KEY (`int_idUsuarios`) USING BTREE,
  ADD UNIQUE KEY `correo` (`str_Correo`);

--
-- Indices de la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  ADD PRIMARY KEY (`int_idAprobador`),
  ADD KEY `aprobador_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `aprobador_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `aprobador_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `aprobador_solicitud_FK` (`int_idSolicitudes`);

--
-- Indices de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD PRIMARY KEY (`int_idArchivos`),
  ADD KEY `archivos_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `archivos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `archivos_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `archivos_tipoDocumentos_FK` (`int_idTipoDocumento`),
  ADD KEY `archivos_solicitud_FK` (`int_idSolicitudes`);

--
-- Indices de la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  ADD PRIMARY KEY (`int_idClausulasActivas`),
  ADD KEY `ClausulasActivas_tipoSol_FK` (`int_idTipoSolicitud`),
  ADD KEY `ClausulasActivas_Clausula_FK` (`int_idClausulaLegal`),
  ADD KEY `ClausulasActivas_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ClausulasActivas_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `ClausulasActivas_Solicitudes_FK` (`int_idSolicitudes`),
  ADD KEY `ClausulasActivas_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  ADD PRIMARY KEY (`int_idClausulasIncluidas`),
  ADD KEY `ClausulasIncluida_tipoSol_FK` (`int_idTipoSolicitud`),
  ADD KEY `ClausulasIncluida_Clausula_FK` (`int_idClausulaLegal`),
  ADD KEY `ClausulasIncluida_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ClausulasIncluida_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `ClausulasIncluidas_solicitud_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  ADD PRIMARY KEY (`int_idConsorcio`),
  ADD KEY `Consorcio_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Consorcio_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Consorcio_Interlocutor_FK` (`int_idInterlocutor`);

--
-- Indices de la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  ADD PRIMARY KEY (`int_idConsorcioPorSolicitud`),
  ADD KEY `Consorio_SolicitudCont_FK` (`int_idConsorcio`),
  ADD KEY `SolicitudCont_Consorio_FK` (`int_idSolicitudCont`),
  ADD KEY `ConsorcioSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `ConsorcioSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  ADD PRIMARY KEY (`int_idCorrelativo`),
  ADD KEY `correlativo_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `correlativo_tipoSolicitud_FK` (`int_idTipoSolicitud`),
  ADD KEY `correlativo_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `correlativo_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  ADD PRIMARY KEY (`int_idEstadoRegistros`),
  ADD KEY `EstadoRegistros_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `EstadoRegistros_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `EstadoRegistros_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `EstadoRegistros_Estado_FK` (`int_idEstado`),
  ADD KEY `EstadoRegistros_Solicitudes_FK` (`int_idSolicitudes`);

--
-- Indices de la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  ADD PRIMARY KEY (`int_idSolicitudCont`),
  ADD KEY `SolicitudCont_Solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `SolicitudCont_Suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `SolicitudCont_Interlocutor_FK` (`int_idInterlocutor`),
  ADD KEY ` SolicitudCont_InterlocutorComprador_FK` (`int_idInterlocutorComprador`);

--
-- Indices de la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  ADD PRIMARY KEY (`int_idSolicitudes`),
  ADD KEY `solicitud_usuario_FK` (`int_idSolicitante`),
  ADD KEY `solicitud_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `solicitud_empresa_FK` (`int_idEmpresa`),
  ADD KEY `solicitud_usuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `solicitud_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `solicitud_Gestor_FK` (`int_idGestor`),
  ADD KEY `solicitud_tipoSolicitud_FK` (`int_idTipoSol`),
  ADD KEY `solicitud_clienteAsociado_FK` (`int_idClienteAsociado`),
  ADD KEY `solicitud_estados_FK` (`int_idEstado`);

--
-- Indices de la tabla `tr_tags`
--
ALTER TABLE `tr_tags`
  ADD PRIMARY KEY (`int_idTags`),
  ADD KEY `tags_solicitud_FK` (`int_idSolicitudes`),
  ADD KEY `tags_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `tags_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  MODIFY `int_idEmpresa` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  MODIFY `int_idClausulasLegales` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  MODIFY `int_idEspecialidades` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  MODIFY `int_idEstado` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  MODIFY `int_idInterlocutor` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  MODIFY `int_idTipoDocumentos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  MODIFY `int_idTipoSolicitud` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  MODIFY `int_idUsuarios` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT de la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  MODIFY `int_idAprobador` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  MODIFY `int_idArchivos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT de la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  MODIFY `int_idClausulasActivas` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  MODIFY `int_idClausulasIncluidas` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  MODIFY `int_idConsorcio` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  MODIFY `int_idConsorcioPorSolicitud` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  MODIFY `int_idCorrelativo` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  MODIFY `int_idEstadoRegistros` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT de la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  MODIFY `int_idSolicitudCont` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  MODIFY `int_idSolicitudes` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `tr_tags`
--
ALTER TABLE `tr_tags`
  MODIFY `int_idTags` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD CONSTRAINT `empresas_suscriptor_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_clausulaslegales`
--
ALTER TABLE `tm_clausulaslegales`
  ADD CONSTRAINT `clausulas_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `clausulasLegales_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `clausulasLegales_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD CONSTRAINT `Especialidad_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD CONSTRAINT `Estados_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD CONSTRAINT `Interlocutor_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Interlocutor_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Interlocutor_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD CONSTRAINT `Suscripciones_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Suscripciones_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD CONSTRAINT `TipoDocumentos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoDocumentos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tiposolicitud`
--
ALTER TABLE `tm_tiposolicitud`
  ADD CONSTRAINT `TipoSolicitud_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoSolicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoSolicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_aprobadores`
--
ALTER TABLE `tr_aprobadores`
  ADD CONSTRAINT `aprobador_solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `aprobador_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `aprobador_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `aprobador_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD CONSTRAINT `archivos_solicitud_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_tipoDocumentos_FK` FOREIGN KEY (`int_idTipoDocumento`) REFERENCES `tm_tipodocumento` (`int_idTipoDocumentos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_clausulasactivas`
--
ALTER TABLE `tr_clausulasactivas`
  ADD CONSTRAINT `ClausulasActivas_Clausulas_FK` FOREIGN KEY (`int_idClausulaLegal`) REFERENCES `tm_clausulaslegales` (`int_idClausulasLegales`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_Solicitudes_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasActivas_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_clausulasincluidas`
--
ALTER TABLE `tr_clausulasincluidas`
  ADD CONSTRAINT `ClausulasIncluida_Clausulas_FK` FOREIGN KEY (`int_idClausulaLegal`) REFERENCES `tm_clausulaslegales` (`int_idClausulasLegales`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluida_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluida_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluidas_solicitud_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ClausulasIncluidas_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_consorcio`
--
ALTER TABLE `tr_consorcio`
  ADD CONSTRAINT `Consorcio_Interlocutor_FK` FOREIGN KEY (`int_idInterlocutor`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Consorcio_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Consorcio_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_consorcioporsolicitud`
--
ALTER TABLE `tr_consorcioporsolicitud`
  ADD CONSTRAINT `ConsorcioSolicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `ConsorcioSolicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Consorio_SolicitudCont_FK` FOREIGN KEY (`int_idConsorcio`) REFERENCES `tr_consorcio` (`int_idConsorcio`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `SolicitudCont_Consorio_FK` FOREIGN KEY (`int_idSolicitudCont`) REFERENCES `tr_solicitudcont` (`int_idSolicitudCont`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  ADD CONSTRAINT `correlativo_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSolicitud`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  ADD CONSTRAINT `EstadoRegistros_Estado_FK` FOREIGN KEY (`int_idEstado`) REFERENCES `tm_estados` (`int_idEstado`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_Solicitudes_FK` FOREIGN KEY (`int_idSolicitudes`) REFERENCES `tr_solicitudes` (`int_idSolicitudes`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_solicitudcont`
--
ALTER TABLE `tr_solicitudcont`
  ADD CONSTRAINT ` SolicitudCont_InterlocutorComprador_FK` FOREIGN KEY (`int_idInterlocutorComprador`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `SolicitudCont_Interlocutor_FK` FOREIGN KEY (`int_idInterlocutor`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `SolicitudCont_Suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_solicitudes`
--
ALTER TABLE `tr_solicitudes`
  ADD CONSTRAINT `Solicitud_ClienteAsociado_FK` FOREIGN KEY (`int_idClienteAsociado`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Empresa_FK` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Estado_FK` FOREIGN KEY (`int_idEstado`) REFERENCES `tm_estados` (`int_idEstado`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Gestor_FK` FOREIGN KEY (`int_idGestor`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Solicitante_FK` FOREIGN KEY (`int_idSolicitante`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_Suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_TipoSolicitud_FK` FOREIGN KEY (`int_idTipoSol`) REFERENCES `tm_tiposolicitud` (`int_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Solicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
