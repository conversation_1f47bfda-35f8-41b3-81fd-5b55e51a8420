
-- MySQL dump 10.13  Distrib 8.0.39, for Linux (x86_64)
--
-- Host: localhost    Database: db_prisma_procesos
-- ------------------------------------------------------
-- Server version       8.0.39-0ubuntu0.24.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `auth_group`
--

DROP TABLE IF EXISTS `auth_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_group_permissions`
--

DROP TABLE IF EXISTS `auth_group_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`) USING BTREE,
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`) USING BTREE,
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_permission`
--

DROP TABLE IF EXISTS `auth_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content_type_id` int NOT NULL,
  `codename` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`) USING BTREE,
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_user`
--

DROP TABLE IF EXISTS `auth_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `first_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `last_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `username` (`username`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_user_groups`
--

DROP TABLE IF EXISTS `auth_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user_groups` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `group_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_user_groups_user_id_group_id_94350c0c_uniq` (`user_id`,`group_id`) USING BTREE,
  KEY `auth_user_groups_group_id_97559544_fk_auth_group_id` (`group_id`) USING BTREE,
  CONSTRAINT `auth_user_groups_group_id_97559544_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `auth_user_groups_user_id_6a12ed8b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_user_user_permissions`
--

DROP TABLE IF EXISTS `auth_user_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user_user_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_user_user_permissions_user_id_permission_id_14a6b632_uniq` (`user_id`,`permission_id`) USING BTREE,
  KEY `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` (`permission_id`) USING BTREE,
  CONSTRAINT `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `django_content_type`
--

DROP TABLE IF EXISTS `django_content_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_content_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `django_migrations`
--

DROP TABLE IF EXISTS `django_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_migrations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tc_empresas`
--

DROP TABLE IF EXISTS `tc_empresas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tc_empresas` (
  `int_idEmpresa` int NOT NULL,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Moneda` varchar(50) DEFAULT NULL,
  `str_Pais` varchar(50) DEFAULT NULL,
  `str_SimboloMoneda` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`int_idEmpresa`) USING BTREE,
  KEY `tc_empresas_str_idSuscripcion_e003eee8_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tc_empresas_str_idSuscripcion_e003eee8_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_CredentialBot`
--

DROP TABLE IF EXISTS `tm_CredentialBot`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_CredentialBot` (
  `int_idCredentialBot` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(15) NOT NULL,
  `str_claveApi` varchar(255) NOT NULL,
  `dt_fechaCambio` datetime DEFAULT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `bool_estado` tinyint NOT NULL,
  PRIMARY KEY (`int_idCredentialBot`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_estados`
--

DROP TABLE IF EXISTS `tm_estados`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_estados` (
  `int_idEstado` int NOT NULL AUTO_INCREMENT,
  `str_codEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`int_idEstado`) USING BTREE,
  KEY `tm_estados_str_idSuscripcion_20480e01_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_estados_str_idSuscripcion_20480e01_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_interlocutores`
--

DROP TABLE IF EXISTS `tm_interlocutores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_interlocutores` (
  `int_idInterlocutor` int NOT NULL AUTO_INCREMENT,
  `str_Documento` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Domicilio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_idTipoDocumento` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_IdSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`int_idInterlocutor`) USING BTREE,
  KEY `tm_interlocutores_str_IdSuscripcion_28bb2e54_fk_tm_suscri` (`str_IdSuscripcion`) USING BTREE,
  CONSTRAINT `tm_interlocutores_str_IdSuscripcion_28bb2e54_fk_tm_suscri` FOREIGN KEY (`str_IdSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_materias`
--

DROP TABLE IF EXISTS `tm_materias`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_materias` (
  `int_idMateria` int NOT NULL AUTO_INCREMENT,
  `str_idMateria` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `dt_FechaCreacion` datetime(6) NOT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idTipoProceso` int NOT NULL,
  `str_IdSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`int_idMateria`) USING BTREE,
  KEY `tm_materias_int_idTipoProceso_91ef7cfa_fk_tm_tipopr` (`int_idTipoProceso`) USING BTREE,
  KEY `tm_materias_str_IdSuscripcion_e6600942_fk_tm_suscri` (`str_IdSuscripcion`) USING BTREE,
  CONSTRAINT `tm_materias_int_idTipoProceso_91ef7cfa_fk_tm_tipopr` FOREIGN KEY (`int_idTipoProceso`) REFERENCES `tm_tipoprocesos` (`int_idTipoProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tm_materias_str_IdSuscripcion_e6600942_fk_tm_suscri` FOREIGN KEY (`str_IdSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_probabilidadexito`
--

DROP TABLE IF EXISTS `tm_probabilidadexito`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_probabilidadexito` (
  `int_idProbabilidadExito` int NOT NULL AUTO_INCREMENT,
  `str_idProbabilidadExito` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`int_idProbabilidadExito`) USING BTREE,
  KEY `tm_probabilidadexito_str_idSuscripcion_c1fe6e73_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_probabilidadexito_str_idSuscripcion_c1fe6e73_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_suscripcion`
--

DROP TABLE IF EXISTS `tm_suscripcion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  PRIMARY KEY (`str_idSuscripcion`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_tipocambio`
--

DROP TABLE IF EXISTS `tm_tipocambio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_tipocambio` (
  `int_idTipoCambio` int NOT NULL AUTO_INCREMENT,
  `str_valorCambio` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `int_idEmpresa` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `bool_estado` tinyint(1) NOT NULL,
  `dt_FechaCreacion` datetime(6) NOT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `dt_FechaCambio` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`int_idTipoCambio`) USING BTREE,
  KEY `tm_tipocambio_int_idEmpresa_c7c16911_fk_tc_empres` (`int_idEmpresa`) USING BTREE,
  KEY `tm_tipocambio_str_idSuscripcion_8ad4856a_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_tipocambio_int_idEmpresa_c7c16911_fk_tc_empres` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tm_tipocambio_str_idSuscripcion_8ad4856a_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_tipodocumento`
--

DROP TABLE IF EXISTS `tm_tipodocumento`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_tipodocumento` (
  `int_idTipoDocumentos` int NOT NULL AUTO_INCREMENT,
  `str_CodTipoDocumento` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) NOT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`int_idTipoDocumentos`) USING BTREE,
  KEY `tm_tipodocumento_str_idSuscripcion_aba0065d_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_tipodocumento_str_idSuscripcion_aba0065d_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_tipoprocesos`
--

DROP TABLE IF EXISTS `tm_tipoprocesos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_tipoprocesos` (
  `int_idTipoProceso` int NOT NULL AUTO_INCREMENT,
  `str_CodTipoSol` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) NOT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`int_idTipoProceso`) USING BTREE,
  KEY `tm_tipoprocesos_str_idSuscripcion_87b4867a_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_tipoprocesos_str_idSuscripcion_87b4867a_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_unidadesnegocios`
--

DROP TABLE IF EXISTS `tm_unidadesnegocios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_unidadesnegocios` (
  `int_idUnidadesNegocio` int NOT NULL AUTO_INCREMENT,
  `str_Descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_fechaCreacion` datetime(6) NOT NULL,
  `dt_fechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreador` int DEFAULT NULL,
  `int_idUsuarioModificador` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `int_idEmpresa` int NOT NULL,
  PRIMARY KEY (`int_idUnidadesNegocio`) USING BTREE,
  KEY `tm_unidadesnegocios_int_idUsuarioCreador_7b6015f9_fk_tm_usuari` (`int_idUsuarioCreador`) USING BTREE,
  KEY `tm_unidadesnegocios_int_idUsuarioModific_69bc8cfb_fk_tm_usuari` (`int_idUsuarioModificador`) USING BTREE,
  KEY `tm_unidadesnegocios_str_idSuscripcion_edc54284_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_unidadesnegocios_int_idUsuarioCreador_7b6015f9_fk_tm_usuari` FOREIGN KEY (`int_idUsuarioCreador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tm_unidadesnegocios_int_idUsuarioModific_69bc8cfb_fk_tm_usuari` FOREIGN KEY (`int_idUsuarioModificador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tm_unidadesnegocios_str_idSuscripcion_edc54284_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_usuarios`
--

DROP TABLE IF EXISTS `tm_usuarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL AUTO_INCREMENT,
  `str_Nombres` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Documento` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_UnidadNegocio` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `int_idEspecialidad` int DEFAULT NULL,
  `int_Estado` int DEFAULT NULL,
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`int_idUsuarios`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_archivosdocumentos`
--

DROP TABLE IF EXISTS `tr_archivosdocumentos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_archivosdocumentos` (
  `int_idArchivos` int NOT NULL AUTO_INCREMENT,
  `str_RutaArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_NombreArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_ExtensionArchivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `str_TamañoArchivo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idTipoDocumento` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `int_idProceso` int NOT NULL,
  PRIMARY KEY (`int_idArchivos`) USING BTREE,
  KEY `tr_archivosdocumento_int_idTipoDocumento_c1212671_fk_tm_tipodo` (`int_idTipoDocumento`) USING BTREE,
  KEY `tr_archivosdocumento_str_idSuscripcion_1eb222ed_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  KEY `tr_archivosdocumento_int_idProceso_4883f4e5_fk_tr_proces` (`int_idProceso`) USING BTREE,
  CONSTRAINT `tr_archivosdocumento_int_idProceso_4883f4e5_fk_tr_proces` FOREIGN KEY (`int_idProceso`) REFERENCES `tr_procesos` (`int_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_archivosdocumento_int_idTipoDocumento_c1212671_fk_tm_tipodo` FOREIGN KEY (`int_idTipoDocumento`) REFERENCES `tm_tipodocumento` (`int_idTipoDocumentos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_archivosdocumento_str_idSuscripcion_1eb222ed_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_correlativoprocesos`
--

DROP TABLE IF EXISTS `tr_correlativoprocesos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_correlativoprocesos` (
  `int_idCorrelativo` int NOT NULL AUTO_INCREMENT,
  `str_CorrelativoTipoProceso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `int_idProceso` int NOT NULL,
  PRIMARY KEY (`int_idCorrelativo`) USING BTREE,
  KEY `tr_correlativoproces_str_idSuscripcion_b26b49f3_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  KEY `tr_correlativoproces_int_idProceso_e6f67106_fk_tr_proces` (`int_idProceso`) USING BTREE,
  CONSTRAINT `tr_correlativoproces_int_idProceso_e6f67106_fk_tr_proces` FOREIGN KEY (`int_idProceso`) REFERENCES `tr_procesos` (`int_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_correlativoproces_str_idSuscripcion_b26b49f3_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_eventos`
--

DROP TABLE IF EXISTS `tr_eventos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_eventos` (
  `int_idEstadoRegistros` int NOT NULL AUTO_INCREMENT,
  `str_nombreEvento` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_fechaEvento` datetime(6) NOT NULL,
  `str_tiempoEmpleado` varchar(255) DEFAULT NULL,
  `str_Observaciones` varchar(5000) DEFAULT NULL,
  `dt_FechaCambio` datetime(6) NOT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `int_idProceso` int NOT NULL,
  PRIMARY KEY (`int_idEstadoRegistros`) USING BTREE,
  KEY `tr_eventos_str_idSuscripcion_25e499a5_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  KEY `tr_eventos_int_idProceso_0619c20c_fk_tr_procesos_int_idProceso` (`int_idProceso`) USING BTREE,
  CONSTRAINT `tr_eventos_int_idProceso_0619c20c_fk_tr_procesos_int_idProceso` FOREIGN KEY (`int_idProceso`) REFERENCES `tr_procesos` (`int_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_eventos_str_idSuscripcion_25e499a5_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_procesocontenido`
--

DROP TABLE IF EXISTS `tr_procesocontenido`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_procesocontenido` (
  `int_idProcesoContenido` int NOT NULL AUTO_INCREMENT,
  `str_Entidad` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_Instancia` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_Fuero` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_demandaObjeto` text,
  `str_DemandaValor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_ContingenciaMoneda` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_ContingenciaProvision` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_ContingenciaValor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_Observaciones` text,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_IdUsuarioCreador` int NOT NULL,
  `int_IdUsuarioModificador` int DEFAULT NULL,
  `str_ProvisionValor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_ProvisionMoneda` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `int_idDemandado` int DEFAULT NULL,
  `int_idDemandante` int DEFAULT NULL,
  `int_idRepresentanteDemandado` int DEFAULT NULL,
  `int_idRepresentanteDemandante` int DEFAULT NULL,
  `int_idProceso_id` int NOT NULL,
  `dt_FechaFirma` datetime(6) DEFAULT NULL,
  `str_Firmante1` varchar(255) DEFAULT NULL,
  `str_Firmante2` varchar(255) DEFAULT NULL,
  `str_documentoFirmante1` varchar(255) DEFAULT NULL,
  `str_documentoFirmante2` varchar(255) DEFAULT NULL,
  `str_DetalleAcuerdo` text,
  PRIMARY KEY (`int_idProcesoContenido`) USING BTREE,
  KEY `tr_procesocontenido_int_idDemandado_fc2c65b0_fk_tm_interl` (`int_idDemandado`) USING BTREE,
  KEY `tr_procesocontenido_int_idDemandante_f530d3d7_fk_tm_interl` (`int_idDemandante`) USING BTREE,
  KEY `tr_procesocontenido_int_idRepresentanteD_c5d36672_fk_tm_interl` (`int_idRepresentanteDemandado`) USING BTREE,
  KEY `tr_procesocontenido_int_idRepresentanteD_85bdbbe3_fk_tm_interl` (`int_idRepresentanteDemandante`) USING BTREE,
  KEY `tr_procesocontenido_int_idProceso_id_72280a98_fk_tr_proces` (`int_idProceso_id`) USING BTREE,
  CONSTRAINT `tr_procesocontenido_int_idDemandado_fc2c65b0_fk_tm_interl` FOREIGN KEY (`int_idDemandado`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesocontenido_int_idDemandante_f530d3d7_fk_tm_interl` FOREIGN KEY (`int_idDemandante`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesocontenido_int_idProceso_id_72280a98_fk_tr_proces` FOREIGN KEY (`int_idProceso_id`) REFERENCES `tr_procesos` (`int_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesocontenido_int_idRepresentanteD_85bdbbe3_fk_tm_interl` FOREIGN KEY (`int_idRepresentanteDemandante`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesocontenido_int_idRepresentanteD_c5d36672_fk_tm_interl` FOREIGN KEY (`int_idRepresentanteDemandado`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_procesos`
--

DROP TABLE IF EXISTS `tr_procesos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_procesos` (
  `int_idProceso` int NOT NULL AUTO_INCREMENT,
  `str_idProceso` varchar(30) DEFAULT NULL,
  `dt_FechaRegistro` datetime(6) DEFAULT NULL,
  `str_EstudioExterno` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_HonorarioEstudioExterno` double DEFAULT NULL,
  `str_MonedaHoronariosEE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_clienteAsociado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_CARazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_CARUC` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_CAContrato` int DEFAULT NULL,
  `str_DocumentosAdjuntos` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `str_Resultado` varchar(21) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `int_idEmpresa` int NOT NULL,
  `int_idEstado` int DEFAULT NULL,
  `int_idMateria` int DEFAULT NULL,
  `int_idProbabilidadExito` int DEFAULT NULL,
  `int_idTipoProceso` int NOT NULL,
  `int_idUnidadesNegocio` int NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `bool_esGuardado` tinyint(1) NOT NULL,
  `str_estadoProceso` text,
  `str_tiempoTotal` varchar(255) DEFAULT NULL,
  `dt_fechaCierre` datetime(6) DEFAULT NULL,
  `str_Expediente` varchar(30) DEFAULT NULL,
  PRIMARY KEY (`int_idProceso`) USING BTREE,
  KEY `tr_procesos_int_idEstado_f4e093fb_fk_tm_estados_int_idEstado` (`int_idEstado`) USING BTREE,
  KEY `tr_procesos_int_idMateria_75f7900e_fk_tm_materias_int_idMateria` (`int_idMateria`) USING BTREE,
  KEY `tr_procesos_int_idProbabilidadEx_6f3bb070_fk_tm_probab` (`int_idProbabilidadExito`) USING BTREE,
  KEY `tr_procesos_str_idSuscripcion_a3bfeb1f_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  KEY `tr_procesos_int_idEmpresa_6aaeb790_fk_tc_empresas_int_idEmpresa` (`int_idEmpresa`) USING BTREE,
  KEY `tr_procesos_int_idTipoProceso_40b05e59_fk_tm_tipopr` (`int_idTipoProceso`) USING BTREE,
  KEY `tr_procesos_int_idUnidadesNegoci_96e95b62_fk_tm_unidad` (`int_idUnidadesNegocio`) USING BTREE,
  KEY `tr_procesos_int_idUsuario_102f65b0_fk_tm_usuarios_int_idUsuarios` (`int_idUsuario`) USING BTREE,
  CONSTRAINT `tr_procesos_int_idEmpresa_6aaeb790_fk_tc_empresas_int_idEmpresa` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesos_int_idEstado_f4e093fb_fk_tm_estados_int_idEstado` FOREIGN KEY (`int_idEstado`) REFERENCES `tm_estados` (`int_idEstado`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesos_int_idMateria_75f7900e_fk_tm_materias_int_idMateria` FOREIGN KEY (`int_idMateria`) REFERENCES `tm_materias` (`int_idMateria`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesos_int_idProbabilidadEx_6f3bb070_fk_tm_probab` FOREIGN KEY (`int_idProbabilidadExito`) REFERENCES `tm_probabilidadexito` (`int_idProbabilidadExito`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesos_int_idTipoProceso_40b05e59_fk_tm_tipopr` FOREIGN KEY (`int_idTipoProceso`) REFERENCES `tm_tipoprocesos` (`int_idTipoProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesos_int_idUnidadesNegoci_96e95b62_fk_tm_unidad` FOREIGN KEY (`int_idUnidadesNegocio`) REFERENCES `tm_unidadesnegocios` (`int_idUnidadesNegocio`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_procesos_int_idUsuario_102f65b0_fk_tm_usuarios_int_idUsuarios` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=96 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_tareas`
--

DROP TABLE IF EXISTS `tr_tareas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_tareas` (
  `int_idTarea` int NOT NULL AUTO_INCREMENT,
  `str_nombreTarea` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dt_fechaInicioTarea` datetime(6) NOT NULL,
  `dt_FechaFinTarea` datetime(6) NOT NULL,
  `str_tiempoEmpleado` varchar(255) DEFAULT NULL,
  `str_Observaciones` varchar(5000) DEFAULT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idProceso` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `bool_esCerrado` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`int_idTarea`) USING BTREE,
  KEY `tr_tareas_int_idProceso_706d7dfd_fk_tr_procesos_int_idProceso` (`int_idProceso`) USING BTREE,
  KEY `tr_tareas_str_idSuscripcion_23190137_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tr_tareas_int_idProceso_706d7dfd_fk_tr_procesos_int_idProceso` FOREIGN KEY (`int_idProceso`) REFERENCES `tr_procesos` (`int_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_tareas_str_idSuscripcion_23190137_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-02-09 17:27:39
