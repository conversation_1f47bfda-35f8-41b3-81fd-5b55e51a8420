-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 23-10-2024 a las 20:50:37
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `greta_seguridad`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tc_empresas`
--

CREATE TABLE `tc_empresas` (
  `int_idEmpresa` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tc_empresas`
--

INSERT INTO `tc_empresas` (`int_idEmpresa`, `str_idSuscripcion`, `str_NombreEmpresa`, `str_RazonSocial`, `str_Ruc`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(4, 'GTLBS-0001', 'Greta Labs', '----', '201234567890', '2024-10-10 22:35:53', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `th_historialusuarios`
--

CREATE TABLE `th_historialusuarios` (
  `int_idHistorialUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaEliminacion` datetime NOT NULL,
  `str_CorreoEliminacion` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `th_historialusuarios`
--

INSERT INTO `th_historialusuarios` (`int_idHistorialUsuario`, `str_idSuscripcion`, `str_Nombres`, `str_Apellidos`, `str_Correo`, `dt_FechaEliminacion`, `str_CorreoEliminacion`) VALUES
(19, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:04:00', '<EMAIL>'),
(20, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:13:11', '<EMAIL>'),
(21, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:14:20', '<EMAIL>'),
(22, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:15:13', '<EMAIL>'),
(23, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:15:51', '<EMAIL>'),
(24, '1346798255', 'Lauren David', 'Arica guerrero', '<EMAIL>', '2024-09-05 10:58:47', '<EMAIL>'),
(25, '1346798255', 'Vicenta del rosario', 'jimenes lucumi', '<EMAIL>', '2024-09-04 17:26:17', '<EMAIL>'),
(26, '1346798255', 'Nicole Alexa', 'Alva', '<EMAIL>', '2024-09-28 15:16:25', '<EMAIL>'),
(30, '1346798255', 'Jose Torres', 'Aprobador 3', '<EMAIL>', '2024-09-28 17:32:53', '<EMAIL>'),
(34, '1346798255', 'José', 'Torres Chirinos', '<EMAIL>', '2024-10-03 15:46:19', '<EMAIL>');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `th_sesiones`
--

CREATE TABLE `th_sesiones` (
  `int_idSesiones` int NOT NULL,
  `str_idUsuario` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaInicio` datetime DEFAULT NULL,
  `dt_FechaCierre` datetime DEFAULT NULL,
  `str_Estado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ipAddress` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `th_sesiones`
--

INSERT INTO `th_sesiones` (`int_idSesiones`, `str_idUsuario`, `dt_FechaInicio`, `dt_FechaCierre`, `str_Estado`, `str_ipAddress`) VALUES
(226, '<EMAIL>', '2024-10-10 22:29:18', NULL, 'Autenticacion 1 Fallida', '**************'),
(227, '<EMAIL>', '2024-10-10 22:29:30', NULL, 'Autenticacion 1 Fallida', '**************'),
(228, '<EMAIL>', '2024-10-10 22:29:30', NULL, 'Autenticacion 1 Fallida', '**************'),
(229, '<EMAIL>', '2024-10-10 22:29:31', NULL, 'Autenticacion 1 Fallida', '**************'),
(230, '<EMAIL>', '2024-10-10 22:29:31', NULL, 'Autenticacion 1 Fallida', '**************'),
(231, '<EMAIL>', '2024-10-10 22:30:04', NULL, 'Autenticacion 1 Fallida', '**************'),
(232, '<EMAIL>', '2024-10-10 22:30:08', NULL, 'Autenticacion 1 Fallida', '**************'),
(233, '<EMAIL>', '2024-10-10 22:30:13', NULL, 'Autenticacion 1 Fallida', '**************'),
(234, '<EMAIL>', '2024-10-10 22:30:17', NULL, 'Autenticacion 1 Fallida', '**************'),
(235, '<EMAIL>', '2024-10-10 22:33:04', NULL, 'Autenticacion 1 Exitoso', '**************'),
(236, '<EMAIL>', '2024-10-10 22:33:09', NULL, 'Ingreso Exitoso', '**************'),
(237, '<EMAIL>', '2024-10-10 22:44:31', NULL, 'Autenticacion 1 Exitoso', '**************'),
(238, '<EMAIL>', '2024-10-10 22:44:37', NULL, 'Ingreso Exitoso', '**************'),
(239, '<EMAIL>', '2024-10-10 22:45:05', NULL, 'Autenticacion 1 Exitoso', '**************'),
(240, '<EMAIL>', '2024-10-10 22:45:14', NULL, 'Ingreso Exitoso', '**************'),
(241, '<EMAIL>', '2024-10-10 22:46:01', NULL, 'Autenticacion 1 Exitoso', '**************'),
(242, '<EMAIL>', '2024-10-10 22:46:07', NULL, 'Ingreso Exitoso', '**************'),
(243, '<EMAIL>', '2024-10-10 22:55:53', NULL, 'Autenticacion 1 Exitoso', '**************'),
(244, '<EMAIL>', '2024-10-10 22:56:06', NULL, 'Ingreso Exitoso', '**************'),
(245, '<EMAIL>', '2024-10-11 08:41:13', NULL, 'Autenticacion 1 Exitoso', '*************'),
(246, '<EMAIL>', '2024-10-11 08:41:22', '2024-10-11 08:43:23', 'Ingreso Exitoso', '*************'),
(247, '<EMAIL>', '2024-10-11 08:43:40', NULL, 'Autenticacion 1 Exitoso', '*************'),
(248, '<EMAIL>', '2024-10-11 08:43:46', '2024-10-11 08:45:23', 'Ingreso Exitoso', '*************'),
(249, '<EMAIL>', '2024-10-11 08:45:28', NULL, 'Autenticacion 1 Exitoso', '*************'),
(250, '<EMAIL>', '2024-10-11 08:45:35', NULL, 'Ingreso Exitoso', '*************'),
(251, '<EMAIL>', '2024-10-11 08:47:17', NULL, 'Autenticacion 1 Exitoso', '*************'),
(252, '<EMAIL>', '2024-10-11 08:47:25', '2024-10-11 08:51:10', 'Ingreso Exitoso', '*************'),
(253, '<EMAIL>', '2024-10-11 08:51:13', NULL, 'Autenticacion 1 Exitoso', '*************'),
(254, '<EMAIL>', '2024-10-11 08:51:20', NULL, 'Ingreso Exitoso', '*************'),
(255, '<EMAIL>', '2024-10-11 08:59:03', NULL, 'Autenticacion 1 Exitoso', '*************'),
(256, '<EMAIL>', '2024-10-11 08:59:10', NULL, 'Ingreso Exitoso', '*************'),
(257, '<EMAIL>', '2024-10-11 09:05:03', NULL, 'Autenticacion 1 Exitoso', '*************'),
(258, '<EMAIL>', '2024-10-11 09:05:09', NULL, 'Ingreso Exitoso', '*************'),
(259, '<EMAIL>', '2024-10-11 09:21:18', NULL, 'Autenticacion 1 Exitoso', '*************'),
(260, '<EMAIL>', '2024-10-11 09:21:24', NULL, 'Credenciales Incorrectas', '*************'),
(261, '<EMAIL>', '2024-10-11 09:21:28', '2024-10-11 09:22:45', 'Ingreso Exitoso', '*************'),
(262, '<EMAIL>', '2024-10-11 09:23:19', NULL, 'Autenticacion 1 Exitoso', '*************'),
(263, '<EMAIL>', '2024-10-11 09:23:29', '2024-10-11 09:27:34', 'Ingreso Exitoso', '*************'),
(264, '<EMAIL>', '2024-10-11 09:28:57', NULL, 'Autenticacion 1 Exitoso', '*************'),
(265, '<EMAIL>', '2024-10-11 09:29:04', '2024-10-11 09:41:10', 'Ingreso Exitoso', '*************'),
(266, '<EMAIL>', '2024-10-11 09:41:14', NULL, 'Autenticacion 1 Exitoso', '*************'),
(267, '<EMAIL>', '2024-10-11 09:41:22', '2024-10-11 10:36:33', 'Ingreso Exitoso', '*************'),
(268, '<EMAIL>', '2024-10-11 10:36:37', NULL, 'Autenticacion 1 Exitoso', '*************'),
(269, '<EMAIL>', '2024-10-11 10:36:44', '2024-10-11 10:45:05', 'Ingreso Exitoso', '*************'),
(270, '<EMAIL>', '2024-10-11 10:45:12', NULL, 'Autenticacion 1 Exitoso', '*************'),
(271, '<EMAIL>', '2024-10-11 10:45:17', '2024-10-11 10:45:37', 'Ingreso Exitoso', '*************'),
(272, '<EMAIL>', '2024-10-11 10:45:44', NULL, 'Autenticacion 1 Exitoso', '*************'),
(273, '<EMAIL>', '2024-10-11 10:45:51', '2024-10-11 10:46:10', 'Ingreso Exitoso', '*************'),
(274, '<EMAIL>', '2024-10-11 10:46:22', NULL, 'Autenticacion 1 Exitoso', '*************'),
(275, '<EMAIL>', '2024-10-11 10:46:26', '2024-10-11 11:03:08', 'Ingreso Exitoso', '*************'),
(276, '<EMAIL>', '2024-10-11 11:03:10', NULL, 'Autenticacion 1 Exitoso', '*************'),
(277, '<EMAIL>', '2024-10-11 11:03:16', '2024-10-11 13:53:28', 'Ingreso Exitoso', '*************'),
(278, '<EMAIL>', '2024-10-11 13:53:46', NULL, 'Autenticacion 1 Exitoso', '*************'),
(279, '<EMAIL>', '2024-10-11 13:54:18', '2024-10-11 14:01:04', 'Ingreso Exitoso', '*************'),
(280, '<EMAIL>', '2024-10-11 14:01:19', NULL, 'Autenticacion 1 Exitoso', '*************'),
(281, '<EMAIL>', '2024-10-11 14:01:46', '2024-10-11 14:04:28', 'Ingreso Exitoso', '*************'),
(282, '<EMAIL>', '2024-10-11 14:10:28', NULL, 'Autenticacion 1 Exitoso', '*************'),
(283, '<EMAIL>', '2024-10-11 14:10:51', '2024-10-11 14:33:05', 'Ingreso Exitoso', '*************'),
(284, '<EMAIL>', '2024-10-11 14:33:17', NULL, 'Autenticacion 1 Exitoso', '*************'),
(285, '<EMAIL>', '2024-10-11 14:33:22', '2024-10-11 14:38:51', 'Ingreso Exitoso', '*************'),
(286, '<EMAIL>', '2024-10-11 14:38:54', NULL, 'Autenticacion 1 Exitoso', '*************'),
(287, '<EMAIL>', '2024-10-11 14:38:59', '2024-10-11 14:43:53', 'Ingreso Exitoso', '*************'),
(288, '<EMAIL>', '2024-10-11 14:44:07', NULL, 'Autenticacion 1 Exitoso', '*************'),
(289, '<EMAIL>', '2024-10-11 14:44:12', '2024-10-11 14:50:37', 'Ingreso Exitoso', '*************'),
(290, '<EMAIL>', '2024-10-11 14:50:43', NULL, 'Autenticacion 1 Exitoso', '*************'),
(291, '<EMAIL>', '2024-10-11 14:50:47', '2024-10-11 14:54:40', 'Ingreso Exitoso', '*************'),
(292, '<EMAIL>', '2024-10-11 14:59:17', NULL, 'Autenticacion 1 Exitoso', '*************'),
(293, '<EMAIL>', '2024-10-11 14:59:25', '2024-10-11 15:33:13', 'Ingreso Exitoso', '*************'),
(294, '<EMAIL>', '2024-10-11 15:47:00', NULL, 'Autenticacion 1 Exitoso', '*************'),
(295, '<EMAIL>', '2024-10-11 15:47:07', '2024-10-11 15:47:11', 'Ingreso Exitoso', '*************'),
(296, '<EMAIL>', '2024-10-11 15:47:24', NULL, 'Autenticacion 1 Exitoso', '*************'),
(297, '<EMAIL>', '2024-10-11 15:47:31', '2024-10-11 15:58:39', 'Ingreso Exitoso', '*************'),
(298, '<EMAIL>', '2024-10-11 15:58:44', NULL, 'Autenticacion 1 Exitoso', '*************'),
(299, '<EMAIL>', '2024-10-11 15:58:52', '2024-10-11 16:09:56', 'Ingreso Exitoso', '*************'),
(300, '<EMAIL>', '2024-10-11 16:14:00', NULL, 'Autenticacion 1 Exitoso', '*************'),
(301, '<EMAIL>', '2024-10-11 16:14:10', '2024-10-11 16:14:45', 'Ingreso Exitoso', '*************'),
(302, '<EMAIL>', '2024-10-11 16:15:38', NULL, 'Autenticacion 1 Exitoso', '*************'),
(303, '<EMAIL>', '2024-10-11 16:15:45', '2024-10-11 16:19:32', 'Ingreso Exitoso', '*************'),
(304, '<EMAIL>', '2024-10-11 16:19:41', NULL, 'Autenticacion 1 Exitoso', '*************'),
(305, '<EMAIL>', '2024-10-11 16:19:46', '2024-10-11 16:22:01', 'Ingreso Exitoso', '*************'),
(306, '<EMAIL>', '2024-10-11 16:22:08', NULL, 'Autenticacion 1 Exitoso', '*************'),
(307, '<EMAIL>', '2024-10-11 16:22:16', '2024-10-11 16:22:40', 'Ingreso Exitoso', '*************'),
(308, '<EMAIL>', '2024-10-11 16:23:11', NULL, 'Autenticacion 1 Exitoso', '*************'),
(309, '<EMAIL>', '2024-10-11 16:23:18', '2024-10-11 16:24:54', 'Ingreso Exitoso', '*************'),
(310, '<EMAIL>', '2024-10-11 16:26:48', NULL, 'Autenticacion 1 Exitoso', '*************'),
(311, '<EMAIL>', '2024-10-11 16:26:54', '2024-10-11 16:29:36', 'Ingreso Exitoso', '*************'),
(312, '<EMAIL>', '2024-10-11 16:29:51', NULL, 'Autenticacion 1 Exitoso', '*************'),
(313, '<EMAIL>', '2024-10-11 16:29:57', '2024-10-11 16:32:47', 'Ingreso Exitoso', '*************'),
(314, '<EMAIL>', '2024-10-11 16:32:48', NULL, 'Autenticacion 1 Exitoso', '*************'),
(315, '<EMAIL>', '2024-10-11 16:32:52', '2024-10-11 16:33:22', 'Ingreso Exitoso', '*************'),
(316, '<EMAIL>', '2024-10-11 16:33:42', NULL, 'Autenticacion 1 Exitoso', '*************'),
(317, '<EMAIL>', '2024-10-11 16:33:56', '2024-10-11 16:36:58', 'Ingreso Exitoso', '*************'),
(318, '<EMAIL>', '2024-10-11 16:37:04', NULL, 'Autenticacion 1 Exitoso', '*************'),
(319, '<EMAIL>', '2024-10-11 16:37:13', '2024-10-11 16:37:41', 'Ingreso Exitoso', '*************'),
(320, '<EMAIL>', '2024-10-11 16:37:50', NULL, 'Autenticacion 1 Exitoso', '*************'),
(321, '<EMAIL>', '2024-10-11 16:37:56', '2024-10-11 16:38:40', 'Ingreso Exitoso', '*************'),
(322, '<EMAIL>', '2024-10-11 16:39:01', NULL, 'Autenticacion 1 Exitoso', '*************'),
(323, '<EMAIL>', '2024-10-11 16:39:06', '2024-10-11 16:39:47', 'Ingreso Exitoso', '*************'),
(324, '<EMAIL>', '2024-10-11 16:39:54', NULL, 'Autenticacion 1 Exitoso', '*************'),
(325, '<EMAIL>', '2024-10-11 16:39:58', '2024-10-11 16:40:28', 'Ingreso Exitoso', '*************'),
(326, '<EMAIL>', '2024-10-11 16:40:33', NULL, 'Autenticacion 1 Exitoso', '*************'),
(327, '<EMAIL>', '2024-10-11 16:40:44', NULL, 'Autenticacion 1 Exitoso', '*************'),
(328, '<EMAIL>', '2024-10-11 16:40:48', '2024-10-11 16:41:18', 'Ingreso Exitoso', '*************'),
(329, '<EMAIL>', '2024-10-11 16:41:24', NULL, 'Autenticacion 1 Exitoso', '*************'),
(330, '<EMAIL>', '2024-10-11 16:41:28', '2024-10-11 16:42:20', 'Ingreso Exitoso', '*************'),
(331, '<EMAIL>', '2024-10-11 16:42:26', NULL, 'Autenticacion 1 Exitoso', '*************'),
(332, '<EMAIL>', '2024-10-11 16:42:31', '2024-10-11 16:42:53', 'Ingreso Exitoso', '*************'),
(333, '<EMAIL>', '2024-10-11 16:43:00', NULL, 'Autenticacion 1 Exitoso', '*************'),
(334, '<EMAIL>', '2024-10-11 16:43:04', '2024-10-11 16:50:42', 'Ingreso Exitoso', '*************'),
(335, '<EMAIL>', '2024-10-11 16:50:50', NULL, 'Autenticacion 1 Exitoso', '*************'),
(336, '<EMAIL>', '2024-10-11 16:51:13', '2024-10-11 16:52:31', 'Ingreso Exitoso', '*************'),
(337, '<EMAIL>', '2024-10-11 16:52:37', NULL, 'Autenticacion 1 Exitoso', '*************'),
(338, '<EMAIL>', '2024-10-11 16:52:43', '2024-10-11 16:55:45', 'Ingreso Exitoso', '*************'),
(339, '<EMAIL>', '2024-10-11 16:56:11', NULL, 'Autenticacion 1 Exitoso', '*************'),
(340, '<EMAIL>', '2024-10-11 16:56:21', '2024-10-11 16:57:04', 'Ingreso Exitoso', '*************'),
(341, '<EMAIL>', '2024-10-11 16:57:11', NULL, 'Autenticacion 1 Exitoso', '*************'),
(342, '<EMAIL>', '2024-10-11 16:57:16', '2024-10-11 17:01:10', 'Ingreso Exitoso', '*************'),
(343, '<EMAIL>', '2024-10-11 17:01:31', NULL, 'Autenticacion 1 Exitoso', '*************'),
(344, '<EMAIL>', '2024-10-11 17:01:41', '2024-10-11 17:02:32', 'Ingreso Exitoso', '*************'),
(345, '<EMAIL>', '2024-10-11 17:03:01', NULL, 'Autenticacion 1 Exitoso', '*************'),
(346, '<EMAIL>', '2024-10-11 17:03:13', '2024-10-11 17:06:09', 'Ingreso Exitoso', '*************'),
(347, '<EMAIL>', '2024-10-11 17:06:10', NULL, 'Autenticacion 1 Exitoso', '*************'),
(348, '<EMAIL>', '2024-10-11 17:06:20', NULL, 'Ingreso Exitoso', '*************'),
(349, '<EMAIL>', '2024-10-14 08:17:23', NULL, 'Autenticacion 1 Exitoso', '*************'),
(350, '<EMAIL>', '2024-10-14 08:17:32', NULL, 'Ingreso Exitoso', '*************');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_aplicaciones`
--

CREATE TABLE `tm_aplicaciones` (
  `int_idAplicacion` int NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `fl_avatar` blob NOT NULL,
  `fl_logo` blob,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_aplicaciones`
--

INSERT INTO `tm_aplicaciones` (`int_idAplicacion`, `str_Nombre`, `str_Url`, `fl_avatar`, `fl_logo`, `str_descripcion`) VALUES
(1, 'Prisma Contratos', 'www.prisma.pe/solicitudes', 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, 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, '....................');
INSERT INTO `tm_aplicaciones` (`int_idAplicacion`, `str_Nombre`, `str_Url`, `fl_avatar`, `fl_logo`, `str_descripcion`) VALUES
(2, 'Prisma Procesos', 'www.prisma.pe/procesos', 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, 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, 'procesos\r\n');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_especialidades`
--

CREATE TABLE `tm_especialidades` (
  `int_idEspecialidades` int NOT NULL,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodigoEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuarioCreador` int DEFAULT NULL,
  `int_idUsuarioModificador` int DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_especialidades`
--

INSERT INTO `tm_especialidades` (`int_idEspecialidades`, `str_idSuscripcion`, `str_CodigoEspecialidad`, `str_NombreEspecialidad`, `int_idUsuarioCreador`, `int_idUsuarioModificador`, `dt_FechaCreacion`, `dt_FechaModificacion`) VALUES
(4, 'GTLBS-0001', 'JURI', 'Juridica', 1, NULL, '2024-10-10 22:34:47', NULL),
(5, 'GTLBS-0001', 'FINA', 'Financiera', 1, NULL, '2024-10-10 22:34:59', NULL),
(6, 'GTLBS-0001', 'LEGA', 'Legal', 1, NULL, '2024-10-10 22:35:17', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_perfiles`
--

CREATE TABLE `tm_perfiles` (
  `int_idPerfil` int NOT NULL,
  `str_Nombre` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_perfiles`
--

INSERT INTO `tm_perfiles` (`int_idPerfil`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(8, 'Administrador', '2024-09-18 09:57:51', NULL, 1, NULL),
(9, 'Owner', '2024-09-04 09:57:51', NULL, 1, NULL),
(10, 'Solicitante', '2024-09-17 09:21:29', NULL, 1, NULL),
(11, 'Gestor', '2024-09-18 09:38:48', NULL, 1, NULL),
(12, 'Aprobador', '2024-09-28 17:16:52', NULL, 1, NULL),
(13, 'Controller', '2024-09-30 10:18:00', NULL, 1, NULL),
(14, 'Aprobador Gerente', '2024-09-30 23:41:34', NULL, 1, NULL),
(15, 'Gestor Administrador', '2024-10-02 16:02:50', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_suscripcion`
--

CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_suscripcion`
--

INSERT INTO `tm_suscripcion` (`str_idSuscripcion`, `str_Nombre`) VALUES
('GTLBS-0001', 'Suscripcion Greta');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipoacceso`
--

CREATE TABLE `tm_tipoacceso` (
  `str_idTipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DescripcionTipoAcceso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tipoacceso`
--

INSERT INTO `tm_tipoacceso` (`str_idTipoAcceso`, `str_DescripcionTipoAcceso`) VALUES
('BOTN', 'Boton'),
('CREA', 'Crear'),
('DELE', 'Borrar'),
('EDIT', 'Editar'),
('PANT', 'Pantalla'),
('VISI', 'Visivilizar');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_usuarios`
--

CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Documento` int NOT NULL,
  `int_idEspecialidad` int DEFAULT NULL,
  `str_UnidadNegocio` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Estado` int DEFAULT '1',
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_usuarios`
--

INSERT INTO `tm_usuarios` (`int_idUsuarios`, `str_Nombres`, `str_Apellidos`, `str_Correo`, `int_Documento`, `int_idEspecialidad`, `str_UnidadNegocio`, `str_Clave`, `int_Estado`, `str_Codigo`, `dt_FechaCreacion`, `dt_FechaModificacion`, `str_idUsuarioCreacion`, `str_idUsuarioModificacion`) VALUES
(1, 'Administrador ', 'Greta Labs', '<EMAIL>', 12345678, 4, 'Unidad De negocio', '$2b$12$iuDvLY.hKLsnIxeWnf5ec.ajaU7hHeBDK8sWoAX18uDEJa/9lEUIG', 1, NULL, NULL, '2024-10-10 22:35:23', NULL, '1'),
(35, 'José Carlos', 'Rodríguez Gonzales', '<EMAIL>', 98745632, 6, 'Unidad de Negocio', '$2b$12$aNuujxWQqAiEA8wN3Haqy.r19GgYcDjoueqXAUnJYxnYptvijm/26', 1, NULL, '2024-10-10 22:38:30', NULL, '1', NULL),
(36, 'Marcos Alfredo', 'Flores Mora', '<EMAIL>', 14789632, 6, 'Unidad de Negocio', '$2b$12$cvUIBbmY/Sz04TvvhBh8xekWiX8ExaYhOPCNsW/hCuGhZqy7CEWfe', 1, NULL, '2024-10-10 22:39:49', NULL, '1', NULL),
(37, 'Julio Piero', 'Guerrero Farfan', '<EMAIL>', 36987412, 5, 'Unidad de Negocio', '$2b$12$DUUifMDh5hrc7eicHWTn0.Yf/8vE0bS2l0fbPc9GAGF3H3scIoQvu', 1, NULL, '2024-10-10 22:41:10', NULL, '1', NULL),
(38, 'Pedro Antonio', 'Gallese Lima', '<EMAIL>', 25874136, 4, 'Unidad de Negocio', '$2b$12$3HykcIC0sj.77SPpifotZeERVQVoCLpxZ9uV8xO.WPyzcPoLHlUrG', 1, NULL, '2024-10-10 22:41:49', NULL, '1', NULL),
(39, 'Miguel Renzo', 'García Jimenez', '<EMAIL>', 14563298, 5, 'Unidad de Negocio', '$2b$12$MlbIrwMrojwurtlxqeZLouinN8XvxqS4BWVYlFOYvg6TH3HKKMhke', 1, NULL, '2024-10-10 22:56:27', NULL, '1', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_accesos`
--

CREATE TABLE `tr_accesos` (
  `int_idAccesos` int NOT NULL,
  `int_idPerfil` int NOT NULL,
  `str_idTipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Valor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idAplicacion` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_accesos`
--

INSERT INTO `tr_accesos` (`int_idAccesos`, `int_idPerfil`, `str_idTipoAcceso`, `str_Valor`, `int_idAplicacion`) VALUES
(13, 8, 'VISI', 'Pantalla 500', 2),
(14, 8, 'DELE', 'usuarios', 2),
(15, 8, 'CREA', 'usuarios', 2),
(16, 9, 'CREA', 'crear archivos', 2),
(17, 10, 'VISI', 'Pantalla Solicitante', 1),
(18, 10, 'CREA', 'Solicitudes', 1),
(19, 11, 'PANT', 'Pantalla 100', 1),
(20, 11, 'EDIT', 'Editar Solicitudes', 1),
(21, 12, 'VISI', 'Pantalla Aprobar\r\n', 1),
(22, 13, 'VISI', 'Solicitudes Nuevas', 1),
(23, 14, 'VISI', '500 y Aprobar', 1),
(24, 15, 'VISI', 'Todo ', 1),
(25, 15, 'EDIT', 'Todo', 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_accesousuario`
--

CREATE TABLE `tr_accesousuario` (
  `int_idAccesoUsuario` int NOT NULL,
  `int_idAcceso` int NOT NULL,
  `int_idPerfil` int NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_fechaCreación` datetime NOT NULL,
  `dt_fechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_accesousuario`
--

INSERT INTO `tr_accesousuario` (`int_idAccesoUsuario`, `int_idAcceso`, `int_idPerfil`, `int_idAplicacion`, `int_idUsuario`, `str_idSuscripcion`, `dt_fechaCreación`, `dt_fechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(129, 17, 10, 1, 1, 'GTLBS-0001', '2024-10-10 22:34:15', NULL, 1, NULL),
(130, 18, 10, 1, 1, 'GTLBS-0001', '2024-10-10 22:34:15', NULL, 1, NULL),
(132, 21, 12, 1, 38, 'GTLBS-0001', '2024-10-10 22:42:06', NULL, 1, NULL),
(135, 19, 11, 1, 35, 'GTLBS-0001', '2024-10-11 08:47:33', NULL, 1, NULL),
(136, 20, 11, 1, 35, 'GTLBS-0001', '2024-10-11 08:47:33', NULL, 1, NULL),
(137, 21, 12, 1, 36, 'GTLBS-0001', '2024-10-11 08:47:41', NULL, 1, NULL),
(138, 21, 12, 1, 37, 'GTLBS-0001', '2024-10-11 08:47:48', NULL, 1, NULL),
(139, 24, 15, 1, 39, 'GTLBS-0001', '2024-10-11 08:47:59', NULL, 1, NULL),
(140, 25, 15, 1, 39, 'GTLBS-0001', '2024-10-11 08:47:59', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_asisnacionaplicacion`
--

CREATE TABLE `tr_asisnacionaplicacion` (
  `int_idAsignacion` int NOT NULL,
  `int_idSuscriptor` int NOT NULL,
  `int_idAplicacion` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_asisnacionaplicacion`
--

INSERT INTO `tr_asisnacionaplicacion` (`int_idAsignacion`, `int_idSuscriptor`, `int_idAplicacion`) VALUES
(394, 20, 1),
(395, 21, 1),
(396, 22, 1),
(397, 23, 1),
(398, 19, 1),
(400, 18, 1),
(401, 18, 2);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_empresasuscriptor`
--

CREATE TABLE `tr_empresasuscriptor` (
  `int_idEmpresaSuscriptor` int NOT NULL,
  `int_idSuscriptor` int NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idEmpresa` int NOT NULL,
  `dt_fechaCreacion` datetime NOT NULL,
  `dt_fechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreador` int NOT NULL,
  `int_idUsuarioModificador` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_empresasuscriptor`
--

INSERT INTO `tr_empresasuscriptor` (`int_idEmpresaSuscriptor`, `int_idSuscriptor`, `int_idAplicacion`, `int_idEmpresa`, `dt_fechaCreacion`, `dt_fechaModificacion`, `int_idUsuarioCreador`, `int_idUsuarioModificador`) VALUES
(117, 18, 1, 4, '2024-10-10 22:36:02', NULL, 1, NULL),
(118, 19, 1, 4, '2024-10-10 22:38:51', NULL, 35, NULL),
(119, 20, 1, 4, '2024-10-10 22:40:03', NULL, 36, NULL),
(120, 21, 1, 4, '2024-10-10 22:41:58', NULL, 37, NULL),
(121, 22, 1, 4, '2024-10-10 22:42:02', NULL, 38, NULL),
(122, 23, 1, 4, '2024-10-10 22:56:36', NULL, 39, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_suscriptores`
--

CREATE TABLE `tr_suscriptores` (
  `int_idSuscriptor` int NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RolSuscripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tr_suscriptores`
--

INSERT INTO `tr_suscriptores` (`int_idSuscriptor`, `int_idUsuario`, `str_idSuscripcion`, `str_RolSuscripcion`) VALUES
(18, 1, 'GTLBS-0001', 'Administrador'),
(19, 35, 'GTLBS-0001', 'Suscriptor'),
(20, 36, 'GTLBS-0001', 'Suscriptor'),
(21, 37, 'GTLBS-0001', 'Suscriptor'),
(22, 38, 'GTLBS-0001', 'Suscriptor'),
(23, 39, 'GTLBS-0001', 'Suscriptor');

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD PRIMARY KEY (`int_idEmpresa`),
  ADD KEY `empresas_suscriptor_FK` (`str_idSuscripcion`),
  ADD KEY `empresas_usuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `empresas_usuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `th_historialusuarios`
--
ALTER TABLE `th_historialusuarios`
  ADD PRIMARY KEY (`int_idHistorialUsuario`);

--
-- Indices de la tabla `th_sesiones`
--
ALTER TABLE `th_sesiones`
  ADD PRIMARY KEY (`int_idSesiones`);

--
-- Indices de la tabla `tm_aplicaciones`
--
ALTER TABLE `tm_aplicaciones`
  ADD PRIMARY KEY (`int_idAplicacion`);

--
-- Indices de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD PRIMARY KEY (`int_idEspecialidades`),
  ADD KEY `Especialidad_Suscripcion_FK` (`str_idSuscripcion`),
  ADD KEY `Especialidad_UsuarioCreador_FK` (`int_idUsuarioCreador`),
  ADD KEY `Especialidad_UsuarioModificador_FK` (`int_idUsuarioModificador`);

--
-- Indices de la tabla `tm_perfiles`
--
ALTER TABLE `tm_perfiles`
  ADD PRIMARY KEY (`int_idPerfil`),
  ADD UNIQUE KEY `nombre` (`str_Nombre`),
  ADD KEY `Perfiles_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Perfiles_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD PRIMARY KEY (`str_idSuscripcion`);

--
-- Indices de la tabla `tm_tipoacceso`
--
ALTER TABLE `tm_tipoacceso`
  ADD PRIMARY KEY (`str_idTipoAcceso`);

--
-- Indices de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  ADD PRIMARY KEY (`int_idUsuarios`) USING BTREE,
  ADD KEY `usuario_especialidad_fk` (`int_idEspecialidad`);

--
-- Indices de la tabla `tr_accesos`
--
ALTER TABLE `tr_accesos`
  ADD PRIMARY KEY (`int_idAccesos`),
  ADD KEY `perfil_tabla_FK` (`int_idPerfil`),
  ADD KEY `acceso_Aplicacion_FK` (`int_idAplicacion`),
  ADD KEY `acceso_TipoAcceso_FK` (`str_idTipoAcceso`);

--
-- Indices de la tabla `tr_accesousuario`
--
ALTER TABLE `tr_accesousuario`
  ADD PRIMARY KEY (`int_idAccesoUsuario`),
  ADD KEY `accesos_acceso_FK` (`int_idAcceso`),
  ADD KEY `accesos_perfil_FK` (`int_idPerfil`),
  ADD KEY `accesos_usuario_FK` (`int_idUsuario`),
  ADD KEY `accesos_aplicacion_FK` (`int_idAplicacion`),
  ADD KEY `accesos_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tr_asisnacionaplicacion`
--
ALTER TABLE `tr_asisnacionaplicacion`
  ADD PRIMARY KEY (`int_idAsignacion`),
  ADD KEY `asignacion_aplicacion_FK` (`int_idAplicacion`),
  ADD KEY `asignacion_suscriptor_FK` (`int_idSuscriptor`);

--
-- Indices de la tabla `tr_empresasuscriptor`
--
ALTER TABLE `tr_empresasuscriptor`
  ADD PRIMARY KEY (`int_idEmpresaSuscriptor`),
  ADD KEY `EmpresaSuscriptor_suscriptor_FK` (`int_idSuscriptor`),
  ADD KEY `EmpresaSuscriptor_aplicacion_FK` (`int_idAplicacion`),
  ADD KEY `EmpresaSuscriptor_empresa_FK` (`int_idEmpresa`);

--
-- Indices de la tabla `tr_suscriptores`
--
ALTER TABLE `tr_suscriptores`
  ADD PRIMARY KEY (`int_idSuscriptor`),
  ADD KEY `suscriptor_usuario_FK` (`int_idUsuario`),
  ADD KEY `suscriptor_suscripcion_FK` (`str_idSuscripcion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  MODIFY `int_idEmpresa` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `th_historialusuarios`
--
ALTER TABLE `th_historialusuarios`
  MODIFY `int_idHistorialUsuario` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- AUTO_INCREMENT de la tabla `th_sesiones`
--
ALTER TABLE `th_sesiones`
  MODIFY `int_idSesiones` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=351;

--
-- AUTO_INCREMENT de la tabla `tm_aplicaciones`
--
ALTER TABLE `tm_aplicaciones`
  MODIFY `int_idAplicacion` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  MODIFY `int_idEspecialidades` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `tm_perfiles`
--
ALTER TABLE `tm_perfiles`
  MODIFY `int_idPerfil` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  MODIFY `int_idUsuarios` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT de la tabla `tr_accesos`
--
ALTER TABLE `tr_accesos`
  MODIFY `int_idAccesos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT de la tabla `tr_accesousuario`
--
ALTER TABLE `tr_accesousuario`
  MODIFY `int_idAccesoUsuario` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=141;

--
-- AUTO_INCREMENT de la tabla `tr_asisnacionaplicacion`
--
ALTER TABLE `tr_asisnacionaplicacion`
  MODIFY `int_idAsignacion` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=402;

--
-- AUTO_INCREMENT de la tabla `tr_empresasuscriptor`
--
ALTER TABLE `tr_empresasuscriptor`
  MODIFY `int_idEmpresaSuscriptor` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=123;

--
-- AUTO_INCREMENT de la tabla `tr_suscriptores`
--
ALTER TABLE `tr_suscriptores`
  MODIFY `int_idSuscriptor` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD CONSTRAINT `empresas_suscriptor_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `empresas_usuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_especialidades`
--
ALTER TABLE `tm_especialidades`
  ADD CONSTRAINT `Especialidad_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Especialidad_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Especialidad_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_perfiles`
--
ALTER TABLE `tm_perfiles`
  ADD CONSTRAINT `Perfiles_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Perfiles_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  ADD CONSTRAINT `usuario_especialidad_FK` FOREIGN KEY (`int_idEspecialidad`) REFERENCES `tm_especialidades` (`int_idEspecialidades`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_accesos`
--
ALTER TABLE `tr_accesos`
  ADD CONSTRAINT `acceso_Aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `acceso_perfil_FK` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `acceso_TipoAcceso_FK` FOREIGN KEY (`str_idTipoAcceso`) REFERENCES `tm_tipoacceso` (`str_idTipoAcceso`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_accesousuario`
--
ALTER TABLE `tr_accesousuario`
  ADD CONSTRAINT `accesos_acceso_FK` FOREIGN KEY (`int_idAcceso`) REFERENCES `tr_accesos` (`int_idAccesos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_perfil_FK` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_asisnacionaplicacion`
--
ALTER TABLE `tr_asisnacionaplicacion`
  ADD CONSTRAINT `asignacion_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `asignacion_suscriptor_FK` FOREIGN KEY (`int_idSuscriptor`) REFERENCES `tr_suscriptores` (`int_idSuscriptor`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_empresasuscriptor`
--
ALTER TABLE `tr_empresasuscriptor`
  ADD CONSTRAINT `EmpresaSuscriptor_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EmpresaSuscriptor_empresa_FK` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EmpresaSuscriptor_suscriptor_FK` FOREIGN KEY (`int_idSuscriptor`) REFERENCES `tr_suscriptores` (`int_idSuscriptor`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_suscriptores`
--
ALTER TABLE `tr_suscriptores`
  ADD CONSTRAINT `suscriptor_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `suscriptor_usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
