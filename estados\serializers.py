from rest_framework import serializers

class EstadosSerializer(serializers.Serializer):
    str_codEstado  = serializers.CharField(max_length=4)
    str_idSuscripcion = serializers.CharField(max_length=50)
    str_Nombre = serializers.CharField(max_length=150)
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()

class EstadosUpdateSerializer(serializers.Serializer):
    str_codEstado = serializers.Char<PERSON>ield(max_length=150)
    str_Nombre = serializers.CharField(max_length=150)
    int_idUsuarioModificacion = serializers.IntegerField()