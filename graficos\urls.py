from django.urls import path
from .views import ConteoEstadosPorGeneral,ConteoEstadosPorGestor,TiempoPromedioPorGestor,TiempoPromedioPorSolicitante,TiempoPromedioPreparacionGeneral,TiempoPromedioPorGeneral,TiempoPromedioPreparacion,TiempoPromedioPreparacionSolicitante,TiempoPromedioTotal,TiempoPromedioTotalSolicitante,TiempoPromedioTotalGeneral,SolicitudesTotales,SolicitudesTotalesSolicitante,SolicitudesTotalesGeneral,GraficaUnidadNegocio,GraficaUnidadNegocioGeneral,GraficaPresupuestoUN,GraficaPresupuestoUNGeneral,GraficaEstadoSolicitudesGeneral,GraficaEstadoSolicitudes,GraficaHorasTrabajadasGeneral,GraficaHorasTrabajadas,ContratosFirmadosPorGestor,ContratosFirmadosPorSolicitante,ContratosFirmadosGeneral,TiempoPromedioPorGeneralTotal,TiempoPromedioPorGestorTotal,TiempoPromedioPreparacionGeneralTotal,TiempoPromedioPreparacionTotal,TiempoPromedioTotalGeneralTotal,TiempoPromedioTotalTotal,SolicitudesTotalesTotal,SolicitudesTotalesGeneralTotal,GraficaUnidadNegocioSolicitante,GraficaPresupuestoUNSolicitante,GraficaEstadoSolicitudesSolicitante,GraficaHorasTrabajadasSolicitante,GraficaUnidadNegocioFirmados,GraficaUnidadNegocioSolicitanteFirmados,GraficaUnidadNegocioGeneralFirmados

urlpatterns = [
    path('api/calculos/General/', ConteoEstadosPorGeneral.as_view(), name='conteo-por-gestor'),
    path('api/calculos/Gestor/', ConteoEstadosPorGestor.as_view(), name='conteo-por-gestor'),
    path('api/promedio/General/', TiempoPromedioPorGeneral.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Gestor/', TiempoPromedioPorGestor.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Solicitante/', TiempoPromedioPorSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Preparacion/General/', TiempoPromedioPreparacionGeneral.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Preparacion/', TiempoPromedioPreparacion.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Preparacion/Solicitante/', TiempoPromedioPreparacionSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Total/General/', TiempoPromedioTotalGeneral.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Total/', TiempoPromedioTotal.as_view(), name='conteo-por-gestor'),
    path('api/promedio/Total/Solicitante/', TiempoPromedioTotalSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/contratosFirmados/Total/General/', ContratosFirmadosGeneral.as_view(), name='conteo-por-gestor'),
    path('api/contratosFirmados/Total/', ContratosFirmadosPorGestor.as_view(), name='conteo-por-gestor'),
    path('api/contratosFirmados/Total/Solicitante/', ContratosFirmadosPorSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/calculos/solicitudes/Total/', SolicitudesTotales.as_view(), name='conteo-por-gestor'),
    path('api/calculos/solicitudes/Total/Solicitante/', SolicitudesTotalesSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/calculos/solicitudes/Total/General/', SolicitudesTotalesGeneral.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNFaltantes/', GraficaUnidadNegocio.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNFaltantes/Solicitante/', GraficaUnidadNegocioSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNFaltantes/General/', GraficaUnidadNegocioGeneral.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNFirmados/', GraficaUnidadNegocioFirmados.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNFirmados/Solicitante/', GraficaUnidadNegocioSolicitanteFirmados.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNFirmados/General/', GraficaUnidadNegocioGeneralFirmados.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNPresupuesto/', GraficaPresupuestoUN.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNPresupuesto/Solicitante/', GraficaPresupuestoUNSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/graficos/UNPresupuesto/General/', GraficaPresupuestoUNGeneral.as_view(), name='conteo-por-gestor'),
    path('api/graficos/DiferenciaEstados/General/', GraficaEstadoSolicitudesGeneral.as_view(), name='conteo-por-gestor'),
    path('api/graficos/DiferenciaEstados/', GraficaEstadoSolicitudes.as_view(), name='conteo-por-gestor'),
    path('api/graficos/DiferenciaEstados/Solicitante/', GraficaEstadoSolicitudesSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/graficos/HorasTrabajadas/General/', GraficaHorasTrabajadasGeneral.as_view(), name='conteo-por-gestor'),
    path('api/graficos/HorasTrabajadas/', GraficaHorasTrabajadas.as_view(), name='conteo-por-gestor'),
    path('api/graficos/HorasTrabajadas/Solicitante/', GraficaHorasTrabajadasSolicitante.as_view(), name='conteo-por-gestor'),
    path('api/promedioTotal/General/', TiempoPromedioPorGeneralTotal.as_view(), name='conteo-por-gestor'),
    path('api/promedioTotal/Gestor/', TiempoPromedioPorGestorTotal.as_view(), name='conteo-por-gestor'),
    path('api/promedioTotal/Preparacion/General/', TiempoPromedioPreparacionGeneralTotal.as_view(), name='conteo-por-gestor'),
    path('api/promedioTotal/Preparacion/', TiempoPromedioPreparacionTotal.as_view(), name='conteo-por-gestor'),
    path('api/promedioTotal/Total/General/', TiempoPromedioTotalGeneralTotal.as_view(), name='conteo-por-gestor'),
    path('api/promedioTotal/Total/', TiempoPromedioTotalTotal.as_view(), name='conteo-por-gestor'),
    path('api/calculosTotal/solicitudes/Total/', SolicitudesTotalesTotal.as_view(), name='conteo-por-gestor'),
    path('api/calculosTotal/solicitudes/Total/General/', SolicitudesTotalesGeneralTotal.as_view(), name='conteo-por-gestor'),
]