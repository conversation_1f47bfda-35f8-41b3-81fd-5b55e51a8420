from rest_framework import serializers

class UnidadNegocioSerializer(serializers.Serializer):
    str_Descripcion  = serializers.CharField(max_length=255)
    str_idSuscripcion = serializers.CharField(max_length=50)
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreador = serializers.IntegerField()
    int_idEmpresa = serializers.IntegerField()

class UnidadNegocioUpdateSerializer(serializers.Serializer):
    str_Descripcion  = serializers.Char<PERSON>ield(max_length=255)
    int_idUsuarioModificador = serializers.IntegerField()