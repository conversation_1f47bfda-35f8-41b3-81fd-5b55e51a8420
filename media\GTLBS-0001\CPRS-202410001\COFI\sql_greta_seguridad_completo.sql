/*
 Navicat Premium Data Transfer

 Source Server         : LocalHost
 Source Server Type    : MySQL
 Source Server Version : 80039
 Source Host           : localhost:3306
 Source Schema         : greta_seguridad

 Target Server Type    : MySQL
 Target Server Version : 80039
 File Encoding         : 65001

 Date: 23/10/2024 16:09:43
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tc_empresas
-- ----------------------------
DROP TABLE IF EXISTS `tc_empresas`;
CREATE TABLE `tc_empresas`  (
  `int_idEmpresa` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NULL DEFAULT NULL,
  `dt_FechaModificacion` datetime NULL DEFAULT NULL,
  `int_idUsuarioCreacion` int NULL DEFAULT NULL,
  `int_idUsuarioModificacion` int NULL DEFAULT NULL,
  PRIMARY KEY (`int_idEmpresa`) USING BTREE,
  INDEX `empresas_suscriptor_FK`(`str_idSuscripcion` ASC) USING BTREE,
  INDEX `empresas_usuarioCreador_FK`(`int_idUsuarioCreacion` ASC) USING BTREE,
  INDEX `empresas_usuarioModificador_FK`(`int_idUsuarioModificacion` ASC) USING BTREE,
  CONSTRAINT `empresas_suscriptor_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `empresas_usuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `empresas_usuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tc_empresas
-- ----------------------------
INSERT INTO `tc_empresas` VALUES (4, 'GTLBS-0001', 'Greta Labs', '----', '201234567890', '2024-10-10 22:35:53', NULL, 1, NULL);
INSERT INTO `tc_empresas` VALUES (7, 'GTLBS-0001', 'Rava', 'RAVA S.A.C.', '98765432101', '2024-10-23 09:49:25', NULL, 1, NULL);
INSERT INTO `tc_empresas` VALUES (8, 'GTLBS-0001', 'Eco Cultivo', 'Eco Cultivo S.A.C.', '98765432101', '2024-10-23 09:50:05', NULL, 1, NULL);
INSERT INTO `tc_empresas` VALUES (9, 'GTLBS-0001', 'tes', 'test', '123123123', '2024-10-23 09:56:54', NULL, 1, NULL);
INSERT INTO `tc_empresas` VALUES (10, 'GTLBS-0001', 'tes', 'test', '123123123', '2024-10-23 09:56:56', NULL, 1, NULL);

-- ----------------------------
-- Table structure for th_historialusuarios
-- ----------------------------
DROP TABLE IF EXISTS `th_historialusuarios`;
CREATE TABLE `th_historialusuarios`  (
  `int_idHistorialUsuario` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaEliminacion` datetime NOT NULL,
  `str_CorreoEliminacion` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idHistorialUsuario`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of th_historialusuarios
-- ----------------------------
INSERT INTO `th_historialusuarios` VALUES (1, 'GTLBS-0001', 'Administrador ', 'Greta Labs', '<EMAIL>', '2024-10-22 11:12:38', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (19, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:04:00', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (20, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:13:11', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (21, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:14:20', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (22, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:15:13', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (23, '1346798255', 'Jose Analista', 'Torres Chirinos', '<EMAIL>', '2024-09-04 14:15:51', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (24, '1346798255', 'Lauren David', 'Arica guerrero', '<EMAIL>', '2024-09-05 10:58:47', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (25, '1346798255', 'Vicenta del rosario', 'jimenes lucumi', '<EMAIL>', '2024-09-04 17:26:17', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (26, '1346798255', 'Nicole Alexa', 'Alva', '<EMAIL>', '2024-09-28 15:16:25', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (30, '1346798255', 'Jose Torres', 'Aprobador 3', '<EMAIL>', '2024-09-28 17:32:53', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (34, '1346798255', 'José', 'Torres Chirinos', '<EMAIL>', '2024-10-03 15:46:19', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (40, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-14 14:09:44', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (41, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-14 14:18:22', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (43, 'GTLBS-0001', 'asdadad', 'asdasd', '<EMAIL>', '2024-10-17 14:05:44', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (44, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 11:01:32', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (45, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 11:03:31', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (46, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 11:05:12', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (47, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 11:10:24', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (48, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 11:11:03', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (49, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 13:32:11', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (50, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 12:19:33', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (51, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 13:32:08', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (52, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 13:32:14', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (53, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 13:32:04', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (54, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 13:32:02', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (55, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 13:32:06', '<EMAIL>');
INSERT INTO `th_historialusuarios` VALUES (56, 'GTLBS-0001', 'Renzo', 'Carrasco', '<EMAIL>', '2024-10-22 13:32:00', '<EMAIL>');

-- ----------------------------
-- Table structure for th_sesiones
-- ----------------------------
DROP TABLE IF EXISTS `th_sesiones`;
CREATE TABLE `th_sesiones`  (
  `int_idSesiones` int NOT NULL AUTO_INCREMENT,
  `str_idUsuario` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaInicio` datetime NULL DEFAULT NULL,
  `dt_FechaCierre` datetime NULL DEFAULT NULL,
  `str_Estado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL DEFAULT NULL,
  `str_ipAddress` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL,
  PRIMARY KEY (`int_idSesiones`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 561 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of th_sesiones
-- ----------------------------
INSERT INTO `th_sesiones` VALUES (226, '<EMAIL>', '2024-10-10 22:29:18', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (227, '<EMAIL>', '2024-10-10 22:29:30', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (228, '<EMAIL>', '2024-10-10 22:29:30', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (229, '<EMAIL>', '2024-10-10 22:29:31', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (230, '<EMAIL>', '2024-10-10 22:29:31', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (231, '<EMAIL>', '2024-10-10 22:30:04', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (232, '<EMAIL>', '2024-10-10 22:30:08', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (233, '<EMAIL>', '2024-10-10 22:30:13', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (234, '<EMAIL>', '2024-10-10 22:30:17', NULL, 'Autenticacion 1 Fallida', '**************', '');
INSERT INTO `th_sesiones` VALUES (235, '<EMAIL>', '2024-10-10 22:33:04', NULL, 'Autenticacion 1 Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (236, '<EMAIL>', '2024-10-10 22:33:09', NULL, 'Ingreso Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (237, '<EMAIL>', '2024-10-10 22:44:31', NULL, 'Autenticacion 1 Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (238, '<EMAIL>', '2024-10-10 22:44:37', NULL, 'Ingreso Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (239, '<EMAIL>', '2024-10-10 22:45:05', NULL, 'Autenticacion 1 Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (240, '<EMAIL>', '2024-10-10 22:45:14', NULL, 'Ingreso Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (241, '<EMAIL>', '2024-10-10 22:46:01', NULL, 'Autenticacion 1 Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (242, '<EMAIL>', '2024-10-10 22:46:07', NULL, 'Ingreso Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (243, '<EMAIL>', '2024-10-10 22:55:53', NULL, 'Autenticacion 1 Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (244, '<EMAIL>', '2024-10-10 22:56:06', NULL, 'Ingreso Exitoso', '**************', '');
INSERT INTO `th_sesiones` VALUES (245, '<EMAIL>', '2024-10-11 08:41:13', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (246, '<EMAIL>', '2024-10-11 08:41:22', '2024-10-11 08:43:23', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (247, '<EMAIL>', '2024-10-11 08:43:40', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (248, '<EMAIL>', '2024-10-11 08:43:46', '2024-10-11 08:45:23', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (249, '<EMAIL>', '2024-10-11 08:45:28', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (250, '<EMAIL>', '2024-10-11 08:45:35', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (251, '<EMAIL>', '2024-10-11 08:47:17', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (252, '<EMAIL>', '2024-10-11 08:47:25', '2024-10-11 08:51:10', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (253, '<EMAIL>', '2024-10-11 08:51:13', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (254, '<EMAIL>', '2024-10-11 08:51:20', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (255, '<EMAIL>', '2024-10-11 08:59:03', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (256, '<EMAIL>', '2024-10-11 08:59:10', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (257, '<EMAIL>', '2024-10-11 09:05:03', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (258, '<EMAIL>', '2024-10-11 09:05:09', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (259, '<EMAIL>', '2024-10-11 09:21:18', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (260, '<EMAIL>', '2024-10-11 09:21:24', NULL, 'Credenciales Incorrectas', '*************', '');
INSERT INTO `th_sesiones` VALUES (261, '<EMAIL>', '2024-10-11 09:21:28', '2024-10-11 09:22:45', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (262, '<EMAIL>', '2024-10-11 09:23:19', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (263, '<EMAIL>', '2024-10-11 09:23:29', '2024-10-11 09:27:34', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (264, '<EMAIL>', '2024-10-11 09:28:57', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (265, '<EMAIL>', '2024-10-11 09:29:04', '2024-10-11 09:41:10', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (266, '<EMAIL>', '2024-10-11 09:41:14', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (267, '<EMAIL>', '2024-10-11 09:41:22', '2024-10-11 10:36:33', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (268, '<EMAIL>', '2024-10-11 10:36:37', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (269, '<EMAIL>', '2024-10-11 10:36:44', '2024-10-11 10:45:05', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (270, '<EMAIL>', '2024-10-11 10:45:12', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (271, '<EMAIL>', '2024-10-11 10:45:17', '2024-10-11 10:45:37', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (272, '<EMAIL>', '2024-10-11 10:45:44', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (273, '<EMAIL>', '2024-10-11 10:45:51', '2024-10-11 10:46:10', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (274, '<EMAIL>', '2024-10-11 10:46:22', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (275, '<EMAIL>', '2024-10-11 10:46:26', '2024-10-11 11:03:08', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (276, '<EMAIL>', '2024-10-11 11:03:10', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (277, '<EMAIL>', '2024-10-11 11:03:16', '2024-10-11 13:53:28', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (278, '<EMAIL>', '2024-10-11 13:53:46', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (279, '<EMAIL>', '2024-10-11 13:54:18', '2024-10-11 14:01:04', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (280, '<EMAIL>', '2024-10-11 14:01:19', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (281, '<EMAIL>', '2024-10-11 14:01:46', '2024-10-11 14:04:28', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (282, '<EMAIL>', '2024-10-11 14:10:28', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (283, '<EMAIL>', '2024-10-11 14:10:51', '2024-10-11 14:33:05', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (284, '<EMAIL>', '2024-10-11 14:33:17', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (285, '<EMAIL>', '2024-10-11 14:33:22', '2024-10-11 14:38:51', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (286, '<EMAIL>', '2024-10-11 14:38:54', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (287, '<EMAIL>', '2024-10-11 14:38:59', '2024-10-11 14:43:53', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (288, '<EMAIL>', '2024-10-11 14:44:07', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (289, '<EMAIL>', '2024-10-11 14:44:12', '2024-10-11 14:50:37', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (290, '<EMAIL>', '2024-10-11 14:50:43', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (291, '<EMAIL>', '2024-10-11 14:50:47', '2024-10-11 14:54:40', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (292, '<EMAIL>', '2024-10-11 14:59:17', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (293, '<EMAIL>', '2024-10-11 14:59:25', '2024-10-11 15:33:13', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (294, '<EMAIL>', '2024-10-11 15:47:00', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (295, '<EMAIL>', '2024-10-11 15:47:07', '2024-10-11 15:47:11', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (296, '<EMAIL>', '2024-10-11 15:47:24', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (297, '<EMAIL>', '2024-10-11 15:47:31', '2024-10-11 15:58:39', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (298, '<EMAIL>', '2024-10-11 15:58:44', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (299, '<EMAIL>', '2024-10-11 15:58:52', '2024-10-11 16:09:56', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (300, '<EMAIL>', '2024-10-11 16:14:00', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (301, '<EMAIL>', '2024-10-11 16:14:10', '2024-10-11 16:14:45', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (302, '<EMAIL>', '2024-10-11 16:15:38', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (303, '<EMAIL>', '2024-10-11 16:15:45', '2024-10-11 16:19:32', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (304, '<EMAIL>', '2024-10-11 16:19:41', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (305, '<EMAIL>', '2024-10-11 16:19:46', '2024-10-11 16:22:01', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (306, '<EMAIL>', '2024-10-11 16:22:08', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (307, '<EMAIL>', '2024-10-11 16:22:16', '2024-10-11 16:22:40', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (308, '<EMAIL>', '2024-10-11 16:23:11', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (309, '<EMAIL>', '2024-10-11 16:23:18', '2024-10-11 16:24:54', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (310, '<EMAIL>', '2024-10-11 16:26:48', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (311, '<EMAIL>', '2024-10-11 16:26:54', '2024-10-11 16:29:36', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (312, '<EMAIL>', '2024-10-11 16:29:51', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (313, '<EMAIL>', '2024-10-11 16:29:57', '2024-10-11 16:32:47', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (314, '<EMAIL>', '2024-10-11 16:32:48', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (315, '<EMAIL>', '2024-10-11 16:32:52', '2024-10-11 16:33:22', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (316, '<EMAIL>', '2024-10-11 16:33:42', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (317, '<EMAIL>', '2024-10-11 16:33:56', '2024-10-11 16:36:58', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (318, '<EMAIL>', '2024-10-11 16:37:04', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (319, '<EMAIL>', '2024-10-11 16:37:13', '2024-10-11 16:37:41', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (320, '<EMAIL>', '2024-10-11 16:37:50', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (321, '<EMAIL>', '2024-10-11 16:37:56', '2024-10-11 16:38:40', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (322, '<EMAIL>', '2024-10-11 16:39:01', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (323, '<EMAIL>', '2024-10-11 16:39:06', '2024-10-11 16:39:47', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (324, '<EMAIL>', '2024-10-11 16:39:54', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (325, '<EMAIL>', '2024-10-11 16:39:58', '2024-10-11 16:40:28', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (326, '<EMAIL>', '2024-10-11 16:40:33', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (327, '<EMAIL>', '2024-10-11 16:40:44', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (328, '<EMAIL>', '2024-10-11 16:40:48', '2024-10-11 16:41:18', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (329, '<EMAIL>', '2024-10-11 16:41:24', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (330, '<EMAIL>', '2024-10-11 16:41:28', '2024-10-11 16:42:20', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (331, '<EMAIL>', '2024-10-11 16:42:26', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (332, '<EMAIL>', '2024-10-11 16:42:31', '2024-10-11 16:42:53', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (333, '<EMAIL>', '2024-10-11 16:43:00', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (334, '<EMAIL>', '2024-10-11 16:43:04', '2024-10-11 16:50:42', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (335, '<EMAIL>', '2024-10-11 16:50:50', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (336, '<EMAIL>', '2024-10-11 16:51:13', '2024-10-11 16:52:31', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (337, '<EMAIL>', '2024-10-11 16:52:37', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (338, '<EMAIL>', '2024-10-11 16:52:43', '2024-10-11 16:55:45', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (339, '<EMAIL>', '2024-10-11 16:56:11', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (340, '<EMAIL>', '2024-10-11 16:56:21', '2024-10-11 16:57:04', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (341, '<EMAIL>', '2024-10-11 16:57:11', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (342, '<EMAIL>', '2024-10-11 16:57:16', '2024-10-11 17:01:10', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (343, '<EMAIL>', '2024-10-11 17:01:31', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (344, '<EMAIL>', '2024-10-11 17:01:41', '2024-10-11 17:02:32', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (345, '<EMAIL>', '2024-10-11 17:03:01', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (346, '<EMAIL>', '2024-10-11 17:03:13', '2024-10-11 17:06:09', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (347, '<EMAIL>', '2024-10-11 17:06:10', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (348, '<EMAIL>', '2024-10-11 17:06:20', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (349, '<EMAIL>', '2024-10-14 08:17:23', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (350, '<EMAIL>', '2024-10-14 08:17:32', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (351, '<EMAIL>', '2024-10-14 11:29:26', NULL, 'Ingreso Exitoso', '172.175.45.125', '');
INSERT INTO `th_sesiones` VALUES (352, '<EMAIL>', '2024-10-14 11:31:16', NULL, 'Ingreso Exitoso', '172.175.45.125', '');
INSERT INTO `th_sesiones` VALUES (353, '<EMAIL>', '2024-10-14 11:32:01', NULL, 'Ingreso Exitoso', '172.175.45.125', '');
INSERT INTO `th_sesiones` VALUES (354, '<EMAIL>', '2024-10-14 13:58:54', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (355, '<EMAIL>', '2024-10-14 13:59:00', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (356, '<EMAIL>', '2024-10-14 15:04:07', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (357, '<EMAIL>', '2024-10-14 15:04:18', NULL, 'Credenciales Incorrectas', '*************', '');
INSERT INTO `th_sesiones` VALUES (358, '<EMAIL>', '2024-10-14 15:04:39', NULL, 'Credenciales Incorrectas', '*************', '');
INSERT INTO `th_sesiones` VALUES (359, '<EMAIL>', '2024-10-14 15:04:51', NULL, 'Credenciales Incorrectas', '*************', '');
INSERT INTO `th_sesiones` VALUES (360, '<EMAIL>', '2024-10-14 15:05:04', NULL, 'Credenciales Incorrectas', '*************', '');
INSERT INTO `th_sesiones` VALUES (361, '<EMAIL>', '2024-10-14 15:05:08', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (362, '<EMAIL>', '2024-10-15 11:27:15', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (363, '<EMAIL>', '2024-10-15 11:30:07', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (364, '<EMAIL>', '2024-10-15 11:35:35', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (365, '<EMAIL>', '2024-10-15 12:06:55', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (366, '<EMAIL>', '2024-10-15 12:19:31', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (367, '<EMAIL>', '2024-10-15 13:19:51', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (368, '<EMAIL>', '2024-10-15 13:19:56', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (369, '<EMAIL>', '2024-10-15 13:20:02', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (370, '<EMAIL>', '2024-10-15 13:20:28', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (371, '<EMAIL>', '2024-10-15 13:41:36', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (372, '<EMAIL>', '2024-10-15 13:43:08', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (373, '<EMAIL>', '2024-10-15 13:43:18', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (374, '<EMAIL>', '2024-10-15 13:44:34', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (375, '<EMAIL>', '2024-10-15 13:44:53', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (376, '<EMAIL>', '2024-10-15 13:47:43', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (377, '<EMAIL>', '2024-10-15 13:58:02', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (378, '<EMAIL>', '2024-10-15 13:58:14', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (379, '<EMAIL>', '2024-10-15 14:00:10', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (380, '<EMAIL>', '2024-10-15 14:01:59', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (381, '<EMAIL>', '2024-10-15 14:02:01', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (382, '<EMAIL>', '2024-10-15 14:02:08', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (383, '<EMAIL>', '2024-10-15 14:02:28', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (384, '<EMAIL>', '2024-10-15 14:03:11', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (385, '<EMAIL>', '2024-10-15 14:03:13', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (386, '<EMAIL>', '2024-10-15 14:03:36', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (387, '<EMAIL>', '2024-10-15 14:05:35', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (388, '<EMAIL>', '2024-10-15 14:06:10', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (389, '<EMAIL>', '2024-10-15 14:06:14', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (390, '<EMAIL>', '2024-10-15 15:34:07', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (391, '<EMAIL>', '2024-10-15 15:35:30', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (392, '<EMAIL>', '2024-10-16 09:31:49', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (393, '<EMAIL>', '2024-10-16 09:31:56', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (394, '<EMAIL>', '2024-10-16 09:33:28', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (395, '<EMAIL>', '2024-10-16 09:33:36', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (396, '<EMAIL>', '2024-10-16 09:33:51', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (397, '<EMAIL>', '2024-10-16 09:33:55', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (398, '<EMAIL>', '2024-10-16 09:35:00', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (399, '<EMAIL>', '2024-10-16 09:53:42', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (400, '<EMAIL>', '2024-10-16 09:57:34', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (401, '<EMAIL>', '2024-10-16 09:57:42', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (402, '<EMAIL>', '2024-10-16 09:58:50', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (403, '<EMAIL>', '2024-10-16 09:58:54', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (404, '<EMAIL>', '2024-10-16 10:00:03', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (405, '<EMAIL>', '2024-10-16 10:00:08', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (406, '<EMAIL>', '2024-10-16 10:00:31', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (407, '<EMAIL>', '2024-10-16 10:00:53', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (408, '<EMAIL>', '2024-10-16 10:01:39', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (409, '<EMAIL>', '2024-10-16 10:01:45', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (410, '<EMAIL>', '2024-10-16 10:02:24', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (411, '<EMAIL>', '2024-10-16 10:02:29', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (412, '<EMAIL>', '2024-10-16 10:03:32', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (413, '<EMAIL>', '2024-10-16 10:03:37', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (414, '<EMAIL>', '2024-10-16 10:03:47', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (415, '<EMAIL>', '2024-10-16 10:03:53', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (416, '<EMAIL>', '2024-10-16 10:05:27', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (417, '<EMAIL>', '2024-10-16 10:05:32', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (418, '<EMAIL>', '2024-10-16 10:07:21', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (419, '<EMAIL>', '2024-10-16 10:07:25', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (420, '<EMAIL>', '2024-10-16 10:11:29', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (421, '<EMAIL>', '2024-10-16 10:11:34', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (422, '<EMAIL>', '2024-10-16 10:12:23', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (423, '<EMAIL>', '2024-10-16 10:12:29', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (424, '<EMAIL>', '2024-10-16 10:14:58', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (425, '<EMAIL>', '2024-10-16 10:15:03', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (426, '<EMAIL>', '2024-10-16 10:46:53', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (427, '<EMAIL>', '2024-10-16 10:46:57', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (428, '<EMAIL>', '2024-10-16 10:57:42', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (429, '<EMAIL>', '2024-10-16 10:57:48', '2024-10-16 10:58:00', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (430, '<EMAIL>', '2024-10-16 10:58:06', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (431, '<EMAIL>', '2024-10-16 10:58:11', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (432, '<EMAIL>', '2024-10-16 11:00:57', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (433, '<EMAIL>', '2024-10-16 11:01:02', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (434, '<EMAIL>', '2024-10-16 11:11:47', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (435, '<EMAIL>', '2024-10-16 11:11:53', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (436, '<EMAIL>', '2024-10-16 11:18:56', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (437, '<EMAIL>', '2024-10-16 11:19:02', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (438, '<EMAIL>', '2024-10-16 11:24:19', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (439, '<EMAIL>', '2024-10-16 11:24:23', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (440, '<EMAIL>', '2024-10-16 11:26:30', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (441, '<EMAIL>', '2024-10-16 11:26:34', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (442, '<EMAIL>', '2024-10-16 11:27:29', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (443, '<EMAIL>', '2024-10-16 11:27:34', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (444, '<EMAIL>', '2024-10-16 11:28:43', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (445, '<EMAIL>', '2024-10-16 11:28:47', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (446, '<EMAIL>', '2024-10-16 11:30:05', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (447, '<EMAIL>', '2024-10-16 11:30:10', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (448, '<EMAIL>', '2024-10-16 11:37:15', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (449, '<EMAIL>', '2024-10-16 11:37:20', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (450, '<EMAIL>', '2024-10-16 11:38:10', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (451, '<EMAIL>', '2024-10-16 11:38:14', '2024-10-16 11:39:16', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (452, '<EMAIL>', '2024-10-16 11:39:20', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (453, '<EMAIL>', '2024-10-16 11:39:29', '2024-10-16 11:39:47', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (454, '<EMAIL>', '2024-10-16 11:41:02', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (455, '<EMAIL>', '2024-10-16 11:41:10', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (456, '<EMAIL>', '2024-10-16 11:45:11', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (457, '<EMAIL>', '2024-10-16 11:45:16', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (458, '<EMAIL>', '2024-10-16 11:46:31', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (459, '<EMAIL>', '2024-10-16 11:46:35', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (460, '<EMAIL>', '2024-10-16 11:50:35', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (461, '<EMAIL>', '2024-10-16 11:50:41', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (462, '<EMAIL>', '2024-10-16 12:00:53', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (463, '<EMAIL>', '2024-10-16 12:00:57', '2024-10-16 17:35:01', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (464, '<EMAIL>', '2024-10-16 17:15:23', NULL, 'Ingreso Exitoso', '172.169.52.32', '');
INSERT INTO `th_sesiones` VALUES (465, '<EMAIL>', '2024-10-16 17:35:05', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (466, '<EMAIL>', '2024-10-16 17:35:10', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (467, '<EMAIL>', '2024-10-17 08:28:54', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (468, '<EMAIL>', '2024-10-17 08:29:01', '2024-10-17 08:29:11', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (469, '<EMAIL>', '2024-10-17 08:29:15', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (470, '<EMAIL>', '2024-10-17 08:29:25', '2024-10-18 08:42:10', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (471, '<EMAIL>', '2024-10-18 08:42:14', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (472, '<EMAIL>', '2024-10-18 08:42:20', '2024-10-18 09:47:49', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (473, '<EMAIL>', '2024-10-18 09:49:01', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (474, '<EMAIL>', '2024-10-18 09:49:10', '2024-10-18 09:56:58', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (475, '<EMAIL>', '2024-10-18 09:57:07', NULL, 'Autenticacion 1 Fallida', '*************', '');
INSERT INTO `th_sesiones` VALUES (476, '<EMAIL>', '2024-10-18 09:57:08', NULL, 'Autenticacion 1 Fallida', '*************', '');
INSERT INTO `th_sesiones` VALUES (477, '<EMAIL>', '2024-10-18 09:57:15', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (478, '<EMAIL>', '2024-10-18 09:57:23', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (479, '<EMAIL>', '2024-10-22 08:34:13', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (480, '<EMAIL>', '2024-10-22 08:34:19', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (481, '<EMAIL>', '2024-10-22 09:53:45', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (482, '<EMAIL>', '2024-10-22 09:53:49', '2024-10-22 09:54:42', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (483, '<EMAIL>', '2024-10-22 09:54:47', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (484, '<EMAIL>', '2024-10-22 09:54:52', '2024-10-22 09:57:00', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (485, '<EMAIL>', '2024-10-22 09:57:05', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (486, '<EMAIL>', '2024-10-22 09:57:10', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (487, '<EMAIL>', '2024-10-22 09:57:41', '2024-10-22 09:58:01', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (488, '<EMAIL>', '2024-10-22 09:58:08', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (489, '<EMAIL>', '2024-10-22 09:58:12', '2024-10-22 10:05:06', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (490, '<EMAIL>', '2024-10-22 09:58:15', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (491, '<EMAIL>', '2024-10-22 10:05:10', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (492, '<EMAIL>', '2024-10-22 10:05:15', '2024-10-22 10:06:02', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (493, '<EMAIL>', '2024-10-22 10:06:06', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (494, '<EMAIL>', '2024-10-22 10:06:10', '2024-10-22 10:08:18', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (495, '<EMAIL>', '2024-10-22 10:08:23', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (496, '<EMAIL>', '2024-10-22 10:08:40', '2024-10-22 11:20:27', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (497, '<EMAIL>', '2024-10-22 11:20:33', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (498, '<EMAIL>', '2024-10-22 11:20:38', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (499, '<EMAIL>', '2024-10-22 11:24:26', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (500, '<EMAIL>', '2024-10-22 11:24:31', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (501, '<EMAIL>', '2024-10-22 11:25:23', '2024-10-22 14:21:05', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (502, '<EMAIL>', '2024-10-22 14:21:11', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (503, '<EMAIL>', '2024-10-22 14:21:17', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (504, '<EMAIL>', '2024-10-22 14:21:21', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (505, '<EMAIL>', '2024-10-22 14:21:44', '2024-10-22 14:21:52', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (506, '<EMAIL>', '2024-10-22 14:21:59', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (507, '<EMAIL>', '2024-10-22 14:22:05', '2024-10-22 14:28:33', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (508, '<EMAIL>', '2024-10-22 14:28:37', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (509, '<EMAIL>', '2024-10-22 14:28:42', '2024-10-22 14:31:46', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (510, '<EMAIL>', '2024-10-22 14:31:51', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (511, '<EMAIL>', '2024-10-22 14:31:56', '2024-10-22 14:32:56', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (512, '<EMAIL>', '2024-10-22 14:33:01', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (513, '<EMAIL>', '2024-10-22 14:33:08', '2024-10-22 14:33:46', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (514, '<EMAIL>', '2024-10-22 14:33:51', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (515, '<EMAIL>', '2024-10-22 14:33:57', '2024-10-22 14:34:26', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (516, '<EMAIL>', '2024-10-22 14:34:41', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (517, '<EMAIL>', '2024-10-22 14:34:47', '2024-10-22 14:48:44', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (518, '<EMAIL>', '2024-10-22 14:48:51', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (519, '<EMAIL>', '2024-10-22 14:48:58', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (520, '<EMAIL>', '2024-10-22 16:53:57', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (521, '<EMAIL>', '2024-10-22 16:54:09', '2024-10-22 16:54:41', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (522, '<EMAIL>', '2024-10-22 16:54:46', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (523, '<EMAIL>', '2024-10-22 16:54:55', '2024-10-22 16:56:59', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (524, '<EMAIL>', '2024-10-22 16:57:07', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (525, '<EMAIL>', '2024-10-22 16:57:13', '2024-10-22 16:57:19', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (526, '<EMAIL>', '2024-10-22 16:57:27', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (527, '<EMAIL>', '2024-10-22 16:57:32', '2024-10-22 16:57:37', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (528, '<EMAIL>', '2024-10-22 16:57:43', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (529, '<EMAIL>', '2024-10-22 16:57:48', '2024-10-22 16:59:27', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (530, '<EMAIL>', '2024-10-22 16:59:32', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (531, '<EMAIL>', '2024-10-22 16:59:37', '2024-10-22 17:00:12', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (532, '<EMAIL>', '2024-10-22 17:00:16', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (533, '<EMAIL>', '2024-10-22 17:00:22', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (534, '<EMAIL>', '2024-10-23 07:58:30', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (535, '<EMAIL>', '2024-10-23 07:58:37', NULL, 'Credenciales Incorrectas', '*************', '');
INSERT INTO `th_sesiones` VALUES (536, '<EMAIL>', '2024-10-23 07:58:43', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (537, '<EMAIL>', '2024-10-23 07:58:59', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (538, '<EMAIL>', '2024-10-23 07:59:04', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (539, '<EMAIL>', '2024-10-23 08:00:06', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (540, '<EMAIL>', '2024-10-23 08:00:12', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (541, '<EMAIL>', '2024-10-23 08:01:33', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (542, '<EMAIL>', '2024-10-23 08:01:39', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (543, '<EMAIL>', '2024-10-23 08:02:28', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (544, '<EMAIL>', '2024-10-23 08:02:33', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (545, '<EMAIL>', '2024-10-23 08:03:50', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (546, '<EMAIL>', '2024-10-23 08:03:56', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (547, '<EMAIL>', '2024-10-23 08:04:54', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (548, '<EMAIL>', '2024-10-23 08:05:03', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (549, '<EMAIL>', '2024-10-23 08:06:10', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (550, '<EMAIL>', '2024-10-23 08:06:15', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (551, '<EMAIL>', '2024-10-23 08:08:07', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (552, '<EMAIL>', '2024-10-23 08:08:12', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (553, '<EMAIL>', '2024-10-23 08:08:31', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (554, '<EMAIL>', '2024-10-23 08:08:36', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (555, '<EMAIL>', '2024-10-23 08:08:59', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (556, '<EMAIL>', '2024-10-23 08:09:04', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (557, '<EMAIL>', '2024-10-23 09:25:37', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (558, '<EMAIL>', '2024-10-23 09:25:44', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (559, '<EMAIL>', '2024-10-23 09:29:05', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (560, '<EMAIL>', '2024-10-23 09:29:10', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (561, '<EMAIL>', '2024-10-23 09:30:50', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (562, '<EMAIL>', '2024-10-23 09:30:55', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (563, '<EMAIL>', '2024-10-23 09:31:25', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (564, '<EMAIL>', '2024-10-23 09:31:30', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (565, '<EMAIL>', '2024-10-23 09:32:01', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (566, '<EMAIL>', '2024-10-23 09:32:06', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (567, '<EMAIL>', '2024-10-23 09:34:17', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (568, '<EMAIL>', '2024-10-23 09:34:21', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (569, '<EMAIL>', '2024-10-23 10:02:30', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (570, '<EMAIL>', '2024-10-23 10:02:45', NULL, 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (571, '<EMAIL>', '2024-10-23 10:03:43', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (572, '<EMAIL>', '2024-10-23 10:03:48', '2024-10-23 10:03:57', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (573, '<EMAIL>', '2024-10-23 10:05:15', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (574, '<EMAIL>', '2024-10-23 10:05:21', '2024-10-23 10:05:39', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (575, '<EMAIL>', '2024-10-23 10:05:43', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (576, '<EMAIL>', '2024-10-23 10:05:50', '2024-10-23 10:06:28', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (577, '<EMAIL>', '2024-10-23 10:06:37', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (578, '<EMAIL>', '2024-10-23 10:09:30', '2024-10-23 10:09:39', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (579, '<EMAIL>', '2024-10-23 10:09:47', NULL, 'Autenticacion 1 Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (580, '<EMAIL>', '2024-10-23 10:09:53', '2024-10-23 10:14:45', 'Ingreso Exitoso', '*************', '');
INSERT INTO `th_sesiones` VALUES (581, '<EMAIL>', '2024-10-23 10:15:43', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (582, '<EMAIL>', '2024-10-23 10:16:00', '2024-10-23 10:26:29', 'Ingreso Exitoso', '*************', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3Mjk2OTc0NjB9.hp9TQW18ahSu2JCnjiBwrZV3wpftjMGLA182laqXaLA');
INSERT INTO `th_sesiones` VALUES (583, '<EMAIL>', '2024-10-23 10:26:33', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (584, '<EMAIL>', '2024-10-23 10:26:37', '2024-10-23 11:22:33', 'Ingreso Exitoso', '*************', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3Mjk3MDAxNjR9.eM3DyIXF1H9nhzwpM-WLWJkN9NnT0ycy51_aPwD9bC4');
INSERT INTO `th_sesiones` VALUES (585, '<EMAIL>', '2024-10-23 11:22:37', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (586, '<EMAIL>', '2024-10-23 11:22:41', '2024-10-23 11:24:16', 'Ingreso Exitoso', '*************', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3Mjk3MDE0NjF9.cBOJ_Ef4RgJSOVwAlex1c_GexzRFCF2w7QFDrd738R8');
INSERT INTO `th_sesiones` VALUES (587, '<EMAIL>', '2024-10-23 11:24:22', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (588, '<EMAIL>', '2024-10-23 11:25:07', '2024-10-23 11:39:41', 'Ingreso Exitoso', '*************', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3Mjk3MDE2MDd9.4bwpmmXed4CuOndKkKpDP-G1kCVl7tKk0vnI1-k5mGQ');
INSERT INTO `th_sesiones` VALUES (589, '<EMAIL>', '2024-10-23 11:39:55', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (590, '<EMAIL>', '2024-10-23 11:40:00', '2024-10-23 13:39:11', 'Ingreso Exitoso', '*************', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3Mjk3MDE5NTN9.W3tM5hVPKJSVOY1fKAqLLwbD-6Gp2xL6xqrvcIsyZP4');
INSERT INTO `th_sesiones` VALUES (591, '<EMAIL>', '2024-10-23 13:39:22', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (592, '<EMAIL>', '2024-10-23 13:39:26', '2024-10-23 13:46:53', 'Ingreso Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (593, '<EMAIL>', '2024-10-23 13:46:57', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (594, '<EMAIL>', '2024-10-23 13:47:04', '2024-10-23 14:17:45', 'Ingreso Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (595, '<EMAIL>', '2024-10-23 14:20:20', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (596, '<EMAIL>', '2024-10-23 14:20:26', '2024-10-23 14:20:36', 'Ingreso Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (597, '<EMAIL>', '2024-10-23 14:22:56', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (598, '<EMAIL>', '2024-10-23 14:23:05', '2024-10-23 14:23:13', 'Ingreso Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (599, '<EMAIL>', '2024-10-23 14:23:55', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (600, '<EMAIL>', '2024-10-23 14:24:01', '2024-10-23 14:25:07', 'Ingreso Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (601, '<EMAIL>', '2024-10-23 14:25:18', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (602, '<EMAIL>', '2024-10-23 14:25:23', '2024-10-23 14:25:34', 'Ingreso Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (603, '<EMAIL>', '2024-10-23 14:32:36', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (604, '<EMAIL>', '2024-10-23 14:32:44', '2024-10-23 14:32:52', 'Ingreso Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (605, '<EMAIL>', '2024-10-23 14:33:06', NULL, 'Autenticacion 1 Exitoso', '*************', NULL);
INSERT INTO `th_sesiones` VALUES (606, '<EMAIL>', '2024-10-23 14:33:13', '2024-10-23 14:33:47', 'Ingreso Exitoso', '*************', NULL);

-- ----------------------------
-- Table structure for tm_aplicaciones
-- ----------------------------
DROP TABLE IF EXISTS `tm_aplicaciones`;
CREATE TABLE `tm_aplicaciones`  (
  `int_idAplicacion` int NOT NULL AUTO_INCREMENT,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `fl_avatar` mediumblob NOT NULL,
  `fl_logo` mediumblob NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_app_db` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idAplicacion`) USING BTREE,
  INDEX `str_idSuscripcion`(`str_idSuscripcion` ASC) USING BTREE,
  CONSTRAINT `str_idSuscripcion` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tm_aplicaciones
-- ----------------------------
INSERT INTO `tm_aplicaciones` VALUES (1, 'Prisma Contratos', 'https://qaprisma.greta.pe/Prisma-Contratos', 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db_prisma_contratos');
INSERT INTO `tm_aplicaciones` VALUES (2, 'Prisma Procesos', 'https://qaprisma.greta.pe/Prisma-Procesos', 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procesos\n', 'GTLBS-0001', 'test');
INSERT INTO `tm_aplicaciones` VALUES (3, 'Prisma Registros', 'https://qaprisma.greta.pe/Prisma-Registros', 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registros\r ', 'GTLBS-0001', 'test');

-- ----------------------------
-- Table structure for tm_especialidades
-- ----------------------------
DROP TABLE IF EXISTS `tm_especialidades`;
CREATE TABLE `tm_especialidades`  (
  `int_idEspecialidades` int NOT NULL AUTO_INCREMENT,
  `str_idSuscripcion` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CodigoEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuarioCreador` int NULL DEFAULT NULL,
  `int_idUsuarioModificador` int NULL DEFAULT NULL,
  `dt_FechaCreacion` datetime NULL DEFAULT NULL,
  `dt_FechaModificacion` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`int_idEspecialidades`) USING BTREE,
  INDEX `Especialidad_Suscripcion_FK`(`str_idSuscripcion` ASC) USING BTREE,
  INDEX `Especialidad_UsuarioCreador_FK`(`int_idUsuarioCreador` ASC) USING BTREE,
  INDEX `Especialidad_UsuarioModificador_FK`(`int_idUsuarioModificador` ASC) USING BTREE,
  CONSTRAINT `Especialidad_Suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Especialidad_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Especialidad_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tm_especialidades
-- ----------------------------
INSERT INTO `tm_especialidades` VALUES (4, 'GTLBS-0001', 'JURI', 'Juridica', 1, NULL, '2024-10-10 22:34:47', NULL);
INSERT INTO `tm_especialidades` VALUES (5, 'GTLBS-0001', 'FINA', 'Financiera', 1, NULL, '2024-10-10 22:34:59', NULL);
INSERT INTO `tm_especialidades` VALUES (6, 'GTLBS-0001', 'LEGA', 'Legal', 1, NULL, '2024-10-10 22:35:17', NULL);

-- ----------------------------
-- Table structure for tm_perfiles
-- ----------------------------
DROP TABLE IF EXISTS `tm_perfiles`;
CREATE TABLE `tm_perfiles`  (
  `int_idPerfil` int NOT NULL AUTO_INCREMENT,
  `str_Nombre` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NULL DEFAULT NULL,
  `dt_FechaModificacion` datetime NULL DEFAULT NULL,
  `int_idUsuarioCreacion` int NULL DEFAULT NULL,
  `int_idUsuarioModificacion` int NULL DEFAULT NULL,
  PRIMARY KEY (`int_idPerfil`) USING BTREE,
  UNIQUE INDEX `nombre`(`str_Nombre` ASC) USING BTREE,
  INDEX `Perfiles_UsuarioCreador_FK`(`int_idUsuarioCreacion` ASC) USING BTREE,
  INDEX `Perfiles_UsuarioModificador_FK`(`int_idUsuarioModificacion` ASC) USING BTREE,
  CONSTRAINT `Perfiles_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `Perfiles_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tm_perfiles
-- ----------------------------
INSERT INTO `tm_perfiles` VALUES (8, 'Administrador', '2024-09-18 09:57:51', NULL, 1, NULL);
INSERT INTO `tm_perfiles` VALUES (9, 'Owner', '2024-09-04 09:57:51', NULL, 1, NULL);
INSERT INTO `tm_perfiles` VALUES (10, 'Solicitante', '2024-09-17 09:21:29', NULL, 1, NULL);
INSERT INTO `tm_perfiles` VALUES (11, 'Gestor', '2024-09-18 09:38:48', NULL, 1, NULL);
INSERT INTO `tm_perfiles` VALUES (12, 'Aprobador', '2024-09-28 17:16:52', NULL, 1, NULL);
INSERT INTO `tm_perfiles` VALUES (13, 'Gestor Controller', '2024-09-30 10:18:00', NULL, 1, NULL);
INSERT INTO `tm_perfiles` VALUES (14, 'Aprobador Gerente', '2024-09-30 23:41:34', NULL, 1, NULL);
INSERT INTO `tm_perfiles` VALUES (15, 'Gestor Administrador', '2024-10-02 16:02:50', NULL, 1, NULL);

-- ----------------------------
-- Table structure for tm_suscripcion
-- ----------------------------
DROP TABLE IF EXISTS `tm_suscripcion`;
CREATE TABLE `tm_suscripcion`  (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RutaLogo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL,
  `str_RutaIcon` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL,
  PRIMARY KEY (`str_idSuscripcion`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tm_suscripcion
-- ----------------------------
INSERT INTO `tm_suscripcion` VALUES ('GTLBS-0001', 'Suscripcion Greta', 'assets\\suscripcion\\GTLBS-0001\\logo.png', 'assets\\suscripcion\\GTLBS-0001\\icon.png');

-- ----------------------------
-- Table structure for tm_tipoacceso
-- ----------------------------
DROP TABLE IF EXISTS `tm_tipoacceso`;
CREATE TABLE `tm_tipoacceso`  (
  `str_idTipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DescripcionTipoAcceso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`str_idTipoAcceso`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tm_tipoacceso
-- ----------------------------
INSERT INTO `tm_tipoacceso` VALUES ('BOTN', 'Boton');
INSERT INTO `tm_tipoacceso` VALUES ('CREA', 'Crear');
INSERT INTO `tm_tipoacceso` VALUES ('DELE', 'Borrar');
INSERT INTO `tm_tipoacceso` VALUES ('EDIT', 'Editar');
INSERT INTO `tm_tipoacceso` VALUES ('PANT', 'Pantalla');
INSERT INTO `tm_tipoacceso` VALUES ('VISI', 'Visivilizar');

-- ----------------------------
-- Table structure for tm_usuarios
-- ----------------------------
DROP TABLE IF EXISTS `tm_usuarios`;
CREATE TABLE `tm_usuarios`  (
  `int_idUsuarios` int NOT NULL AUTO_INCREMENT,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Documento` int NOT NULL,
  `int_idEspecialidad` int NULL DEFAULT NULL,
  `str_UnidadNegocio` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Estado` int NULL DEFAULT 1,
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL DEFAULT NULL,
  `dt_FechaCreacion` datetime NULL DEFAULT NULL,
  `dt_FechaModificacion` datetime NULL DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL DEFAULT NULL,
  `str_RutaFoto` text CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL,
  PRIMARY KEY (`int_idUsuarios`) USING BTREE,
  INDEX `usuario_especialidad_fk`(`int_idEspecialidad` ASC) USING BTREE,
  CONSTRAINT `usuario_especialidad_FK` FOREIGN KEY (`int_idEspecialidad`) REFERENCES `tm_especialidades` (`int_idEspecialidades`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tm_usuarios
-- ----------------------------
INSERT INTO `tm_usuarios` VALUES (1, 'Administrador ', 'Solicitante', '<EMAIL>', 12345678, 4, 'Unidad De negocio', '$2b$12$iuDvLY.hKLsnIxeWnf5ec.ajaU7hHeBDK8sWoAX18uDEJa/9lEUIG', 1, NULL, NULL, '2024-10-10 22:35:23', NULL, '1', 'assets\\usuarios\\GTLBS-0001\\<EMAIL>\\foto.jpeg');
INSERT INTO `tm_usuarios` VALUES (35, 'Usuario', 'Gestor 1', '<EMAIL>', 98745632, 6, 'Unidad de Negocio', '$2b$12$aNuujxWQqAiEA8wN3Haqy.r19GgYcDjoueqXAUnJYxnYptvijm/26', 1, NULL, '2024-10-10 22:38:30', NULL, '1', NULL, 'assets\\usuarios\\GTLBS-0001\\<EMAIL>\\foto.jpeg');
INSERT INTO `tm_usuarios` VALUES (36, 'Usuario ', 'Aprobador 1', '<EMAIL>', 14789632, 6, 'Unidad de Negocio', '$2b$12$cvUIBbmY/Sz04TvvhBh8xekWiX8ExaYhOPCNsW/hCuGhZqy7CEWfe', 1, NULL, '2024-10-10 22:39:49', NULL, '1', NULL, 'assets\\usuarios\\GTLBS-0001\\<EMAIL>\\foto.jpeg');
INSERT INTO `tm_usuarios` VALUES (37, 'Usuario', 'Aprobador 2', '<EMAIL>', 36987412, 5, 'Unidad de Negocio', '$2b$12$DUUifMDh5hrc7eicHWTn0.Yf/8vE0bS2l0fbPc9GAGF3H3scIoQvu', 1, NULL, '2024-10-10 22:41:10', NULL, '1', NULL, 'assets\\usuarios\\GTLBS-0001\\<EMAIL>\\foto.jpeg');
INSERT INTO `tm_usuarios` VALUES (38, 'Usuario', 'Aprobador 3', '<EMAIL>', 25874136, 4, 'Unidad de Negocio', '$2b$12$3HykcIC0sj.77SPpifotZeERVQVoCLpxZ9uV8xO.WPyzcPoLHlUrG', 1, NULL, '2024-10-10 22:41:49', NULL, '1', NULL, 'assets\\usuarios\\GTLBS-0001\\<EMAIL>\\foto.jpeg');
INSERT INTO `tm_usuarios` VALUES (39, 'Usuario', 'Gestor Administrador', '<EMAIL>', 14563298, 5, 'Unidad de Negocio', '$2b$12$MlbIrwMrojwurtlxqeZLouinN8XvxqS4BWVYlFOYvg6TH3HKKMhke', 1, NULL, '2024-10-10 22:56:27', NULL, '1', NULL, 'assets\\usuarios\\GTLBS-0001\\<EMAIL>\\foto.jpeg');
INSERT INTO `tm_usuarios` VALUES (40, 'Usuario', 'Controller', '<EMAIL>', 25874136, 5, 'Unidad de Negocio', '$2b$12$8ATO0MMOj7P5CJqTP0aFruYhR5azG.5v4ELk59kWPHaEoZ285Xv2m', 1, NULL, '2024-10-22 02:32:03', NULL, '1', NULL, NULL);

-- ----------------------------
-- Table structure for tr_accesos
-- ----------------------------
DROP TABLE IF EXISTS `tr_accesos`;
CREATE TABLE `tr_accesos`  (
  `int_idAccesos` int NOT NULL AUTO_INCREMENT,
  `int_idPerfil` int NOT NULL,
  `str_idTipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL DEFAULT NULL,
  `str_Valor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NULL DEFAULT NULL,
  `int_idAplicacion` int NOT NULL,
  PRIMARY KEY (`int_idAccesos`) USING BTREE,
  INDEX `perfil_tabla_FK`(`int_idPerfil` ASC) USING BTREE,
  INDEX `acceso_Aplicacion_FK`(`int_idAplicacion` ASC) USING BTREE,
  INDEX `acceso_TipoAcceso_FK`(`str_idTipoAcceso` ASC) USING BTREE,
  CONSTRAINT `acceso_Aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `acceso_perfil_FK` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `acceso_TipoAcceso_FK` FOREIGN KEY (`str_idTipoAcceso`) REFERENCES `tm_tipoacceso` (`str_idTipoAcceso`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tr_accesos
-- ----------------------------
INSERT INTO `tr_accesos` VALUES (13, 8, 'VISI', 'Pantalla 500', 2);
INSERT INTO `tr_accesos` VALUES (14, 8, 'DELE', 'usuarios', 2);
INSERT INTO `tr_accesos` VALUES (15, 8, 'CREA', 'usuarios', 2);
INSERT INTO `tr_accesos` VALUES (16, 9, 'CREA', 'crear archivos', 2);
INSERT INTO `tr_accesos` VALUES (17, 10, 'VISI', 'Pantalla Solicitante', 1);
INSERT INTO `tr_accesos` VALUES (18, 10, 'CREA', 'Solicitudes', 1);
INSERT INTO `tr_accesos` VALUES (19, 11, 'PANT', 'Pantalla 100', 1);
INSERT INTO `tr_accesos` VALUES (20, 11, 'EDIT', 'Editar Solicitudes', 1);
INSERT INTO `tr_accesos` VALUES (21, 12, 'VISI', 'Pantalla Aprobar\r\n', 1);
INSERT INTO `tr_accesos` VALUES (22, 13, 'VISI', 'Solicitudes Nuevas', 1);
INSERT INTO `tr_accesos` VALUES (23, 14, 'VISI', '500 y Aprobar', 1);
INSERT INTO `tr_accesos` VALUES (24, 15, 'VISI', 'Todo ', 1);
INSERT INTO `tr_accesos` VALUES (25, 15, 'EDIT', 'Todo', 1);
INSERT INTO `tr_accesos` VALUES (26, 15, 'VISI', 'Todo ', 3);
INSERT INTO `tr_accesos` VALUES (27, 15, 'EDIT', 'Todo', 3);

-- ----------------------------
-- Table structure for tr_accesousuario
-- ----------------------------
DROP TABLE IF EXISTS `tr_accesousuario`;
CREATE TABLE `tr_accesousuario`  (
  `int_idAccesoUsuario` int NOT NULL AUTO_INCREMENT,
  `int_idAcceso` int NOT NULL,
  `int_idPerfil` int NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_fechaCreacion` datetime NOT NULL,
  `dt_fechaModificacion` datetime NULL DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int NULL DEFAULT NULL,
  PRIMARY KEY (`int_idAccesoUsuario`) USING BTREE,
  INDEX `accesos_acceso_FK`(`int_idAcceso` ASC) USING BTREE,
  INDEX `accesos_perfil_FK`(`int_idPerfil` ASC) USING BTREE,
  INDEX `accesos_usuario_FK`(`int_idUsuario` ASC) USING BTREE,
  INDEX `accesos_aplicacion_FK`(`int_idAplicacion` ASC) USING BTREE,
  INDEX `accesos_suscripcion_FK`(`str_idSuscripcion` ASC) USING BTREE,
  CONSTRAINT `accesos_acceso_FK` FOREIGN KEY (`int_idAcceso`) REFERENCES `tr_accesos` (`int_idAccesos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `accesos_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `accesos_perfil_FK` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `accesos_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `accesos_usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 149 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tr_accesousuario
-- ----------------------------
INSERT INTO `tr_accesousuario` VALUES (129, 17, 10, 1, 1, 'GTLBS-0001', '2024-10-10 22:34:15', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (130, 18, 10, 1, 1, 'GTLBS-0001', '2024-10-10 22:34:15', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (132, 21, 12, 1, 38, 'GTLBS-0001', '2024-10-10 22:42:06', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (135, 19, 11, 1, 35, 'GTLBS-0001', '2024-10-11 08:47:33', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (136, 20, 11, 1, 35, 'GTLBS-0001', '2024-10-11 08:47:33', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (137, 21, 12, 1, 36, 'GTLBS-0001', '2024-10-11 08:47:41', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (138, 21, 12, 1, 37, 'GTLBS-0001', '2024-10-11 08:47:48', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (139, 24, 15, 1, 39, 'GTLBS-0001', '2024-10-11 08:47:59', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (140, 25, 15, 1, 39, 'GTLBS-0001', '2024-10-11 08:47:59', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (141, 26, 15, 3, 1, 'GTLBS-0001', '2024-10-18 20:07:59', NULL, 1, NULL);
INSERT INTO `tr_accesousuario` VALUES (143, 22, 13, 1, 40, 'GTLBS-0001', '2024-10-22 02:56:15', NULL, 1, NULL);

-- ----------------------------
-- Table structure for tr_asisnacionaplicacion
-- ----------------------------
DROP TABLE IF EXISTS `tr_asisnacionaplicacion`;
CREATE TABLE `tr_asisnacionaplicacion`  (
  `int_idAsignacion` int NOT NULL AUTO_INCREMENT,
  `int_idSuscriptor` int NOT NULL,
  `int_idAplicacion` int NOT NULL,
  PRIMARY KEY (`int_idAsignacion`) USING BTREE,
  INDEX `asignacion_aplicacion_FK`(`int_idAplicacion` ASC) USING BTREE,
  INDEX `asignacion_suscriptor_FK`(`int_idSuscriptor` ASC) USING BTREE,
  CONSTRAINT `asignacion_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `asignacion_suscriptor_FK` FOREIGN KEY (`int_idSuscriptor`) REFERENCES `tr_suscriptores` (`int_idSuscriptor`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 458 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tr_asisnacionaplicacion
-- ----------------------------
INSERT INTO `tr_asisnacionaplicacion` VALUES (394, 20, 1);
INSERT INTO `tr_asisnacionaplicacion` VALUES (395, 21, 1);
INSERT INTO `tr_asisnacionaplicacion` VALUES (396, 22, 1);
INSERT INTO `tr_asisnacionaplicacion` VALUES (397, 23, 1);
INSERT INTO `tr_asisnacionaplicacion` VALUES (405, 19, 1);
INSERT INTO `tr_asisnacionaplicacion` VALUES (406, 18, 1);
INSERT INTO `tr_asisnacionaplicacion` VALUES (407, 18, 3);

-- ----------------------------
-- Table structure for tr_empresasuscriptor
-- ----------------------------
DROP TABLE IF EXISTS `tr_empresasuscriptor`;
CREATE TABLE `tr_empresasuscriptor`  (
  `int_idEmpresaSuscriptor` int NOT NULL AUTO_INCREMENT,
  `int_idSuscriptor` int NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idEmpresa` int NOT NULL,
  `dt_fechaCreacion` datetime NOT NULL,
  `dt_fechaModificacion` datetime NULL DEFAULT NULL,
  `int_idUsuarioCreador` int NOT NULL,
  `int_idUsuarioModificador` int NULL DEFAULT NULL,
  PRIMARY KEY (`int_idEmpresaSuscriptor`) USING BTREE,
  INDEX `EmpresaSuscriptor_suscriptor_FK`(`int_idSuscriptor` ASC) USING BTREE,
  INDEX `EmpresaSuscriptor_aplicacion_FK`(`int_idAplicacion` ASC) USING BTREE,
  INDEX `EmpresaSuscriptor_empresa_FK`(`int_idEmpresa` ASC) USING BTREE,
  CONSTRAINT `EmpresaSuscriptor_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `EmpresaSuscriptor_empresa_FK` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `EmpresaSuscriptor_suscriptor_FK` FOREIGN KEY (`int_idSuscriptor`) REFERENCES `tr_suscriptores` (`int_idSuscriptor`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 142 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tr_empresasuscriptor
-- ----------------------------
INSERT INTO `tr_empresasuscriptor` VALUES (117, 18, 1, 4, '2024-10-10 22:36:02', NULL, 1, NULL);
INSERT INTO `tr_empresasuscriptor` VALUES (118, 19, 1, 4, '2024-10-10 22:38:51', NULL, 35, NULL);
INSERT INTO `tr_empresasuscriptor` VALUES (119, 20, 1, 4, '2024-10-10 22:40:03', NULL, 36, NULL);
INSERT INTO `tr_empresasuscriptor` VALUES (120, 21, 1, 4, '2024-10-10 22:41:58', NULL, 37, NULL);
INSERT INTO `tr_empresasuscriptor` VALUES (121, 22, 1, 4, '2024-10-10 22:42:02', NULL, 38, NULL);
INSERT INTO `tr_empresasuscriptor` VALUES (122, 23, 1, 4, '2024-10-10 22:56:36', NULL, 39, NULL);
INSERT INTO `tr_empresasuscriptor` VALUES (123, 24, 1, 4, '2024-10-22 02:58:58', NULL, 40, NULL);

-- ----------------------------
-- Table structure for tr_suscriptores
-- ----------------------------
DROP TABLE IF EXISTS `tr_suscriptores`;
CREATE TABLE `tr_suscriptores`  (
  `int_idSuscriptor` int NOT NULL AUTO_INCREMENT,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RolSuscripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idSuscriptor`) USING BTREE,
  INDEX `suscriptor_usuario_FK`(`int_idUsuario` ASC) USING BTREE,
  INDEX `suscriptor_suscripcion_FK`(`str_idSuscripcion` ASC) USING BTREE,
  CONSTRAINT `suscriptor_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `suscriptor_usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_spanish2_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tr_suscriptores
-- ----------------------------
INSERT INTO `tr_suscriptores` VALUES (18, 1, 'GTLBS-0001', 'Administrador');
INSERT INTO `tr_suscriptores` VALUES (19, 35, 'GTLBS-0001', 'Suscriptor');
INSERT INTO `tr_suscriptores` VALUES (20, 36, 'GTLBS-0001', 'Suscriptor');
INSERT INTO `tr_suscriptores` VALUES (21, 37, 'GTLBS-0001', 'Suscriptor');
INSERT INTO `tr_suscriptores` VALUES (22, 38, 'GTLBS-0001', 'Suscriptor');
INSERT INTO `tr_suscriptores` VALUES (23, 39, 'GTLBS-0001', 'Suscriptor');
INSERT INTO `tr_suscriptores` VALUES (24, 40, 'GTLBS-0001', 'Suscriptor');

SET FOREIGN_KEY_CHECKS = 1;
