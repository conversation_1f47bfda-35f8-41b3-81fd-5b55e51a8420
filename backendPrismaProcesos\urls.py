"""
URL configuration for backendPrismaProcesos project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path,include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework.schemas import get_schema_view
urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/schema/', get_schema_view(title="Documentacion de la API"), name='api-schema'),
    path('', include('clausulasLegales.urls')),
    path('', include('archivos.urls')),
    path('', include('estados.urls')),
    path('', include('tiposSolicitud.urls')),
    path('', include('tiposDocumentos.urls')),
    path('', include('solicitudes.urls')),
    path('', include('empresas.urls')),
    path('', include('interlocutores.urls')),
    path('', include('clausulasSolicitud.urls')),
    path('', include('clausulasSolicitud.urls')),
    path('', include('tags.urls')),
    path('', include('consorcio.urls')),
    path('', include('unidadesNegocio.urls')),
    path('', include('SolicitudContenido.urls')),
    path('', include('graficos.urls')),
    path('', include('seguridad.urls')),
    path('', include('tipoCambio.urls')),
    path('', include('gestorTipoSolicitud.urls')),
    path('', include('tiempoEstimado.urls')),
    path('', include('usuarios.urls')),
    path('', include('credentialBot.urls')),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

