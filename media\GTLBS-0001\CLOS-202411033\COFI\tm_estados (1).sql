-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 21-11-2024 a las 15:42:13
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `db_prisma_contratos`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_estados`
--

CREATE TABLE `tm_estados` (
  `int_idEstado` int NOT NULL,
  `str_codEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_estados`
--

INSERT INTO `tm_estados` (`int_idEstado`, `str_codEstado`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'E001', 'GTLBS-0001', 'Nuevo', '2024-09-06 17:15:43', '2024-09-06 17:18:51', 1, 1),
(2, 'E002', 'GTLBS-0001', 'Asignado', '2024-09-18 12:14:10', NULL, 1, NULL),
(3, 'E003', 'GTLBS-0001', 'En Proceso', '2024-09-13 16:06:24', NULL, 1, NULL),
(4, 'E004', 'GTLBS-0001', 'En Validacion', '2024-09-18 12:14:10', NULL, 1, NULL),
(5, 'E005', 'GTLBS-0001', 'Aceptado', '2024-09-18 12:15:08', NULL, 1, NULL),
(6, 'E006', 'GTLBS-0001', 'En Aprobacion', '2024-09-18 12:18:15', NULL, 1, NULL),
(7, 'E007', 'GTLBS-0001', 'Aprobado', '2024-09-18 12:18:15', NULL, 1, NULL),
(8, 'E008', 'GTLBS-0001', 'Firmado', '2024-09-18 12:19:04', NULL, 1, NULL),
(9, 'E001', 'GLTBS-0001', 'Nuevo', '2024-09-06 17:15:43', '2024-09-06 17:18:51', 1, 1),
(10, 'E002', 'GLTBS-0001', 'Asignado', '2024-09-18 12:14:10', NULL, 1, NULL),
(11, 'E003', 'GLTBS-0001', 'En Proceso', '2024-09-13 16:06:24', NULL, 1, NULL),
(12, 'E004', 'GLTBS-0001', 'En Validacion', '2024-09-18 12:14:10', NULL, 1, NULL),
(13, 'E005', 'GLTBS-0001', 'Aceptado', '2024-09-18 12:15:08', NULL, 1, NULL),
(14, 'E006', 'GLTBS-0001', 'En Aprobacion', '2024-09-18 12:18:15', NULL, 1, NULL),
(15, 'E007', 'GLTBS-0001', 'Aprobado', '2024-09-18 12:18:15', NULL, 1, NULL),
(16, 'E008', 'GLTBS-0001', 'Firmado', '2024-09-18 12:19:04', NULL, 1, NULL);

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD PRIMARY KEY (`int_idEstado`),
  ADD KEY `Estados_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Estados_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Estados_suscripcion_FK` (`str_idSuscripcion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  MODIFY `int_idEstado` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD CONSTRAINT `Estados_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
