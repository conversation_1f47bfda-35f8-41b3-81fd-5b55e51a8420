from rest_framework import serializers

class ClausulasLegalesSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField(max_length=50)
    str_Nombre = serializers.CharField(max_length=150)
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()
    int_idEmpresa = serializers.IntegerField()
class ClausulasLegalesUpdateSerializer(serializers.Serializer):
    str_Nombre = serializers.CharField(max_length=150)
    int_idUsuarioModificacion = serializers.IntegerField()