from django.urls import path
from .views import ArchivoUploadAPIView,ArchivoListAPIView,ArchivoDownloadAPIView,TransferirArchivosHistoricos,DescargarArchivoHistorico,DatosArchivoHistorico,ArchivoDatosporTipo,ArchivoDeleteAPIView,ArchivoDownloadporTipo,ArchivoDownloadPlantilla,ArchivoUploadPlantilla,ArchivoDatosporPlantilla,FirmaUpload,FirmaDownload,ArchivoUploadFirmado,DownloadExcelTemplate,DescargarManualUsuario

urlpatterns = [
    path('api/archivos/upload/', ArchivoUploadAPIView.as_view(), name='archivo-upload'),
    path('api/archivos/', ArchivoListAPIView.as_view(), name='archivo-list'),
    path('api/archivos/descargar/', ArchivoDownloadAPIView.as_view(), name='archivo-download'),
    path('api/archivos/eliminar/', ArchivoDeleteAPIView.as_view(), name='archivo-delete'),
    path('api/archivos/descargar-archivo/', ArchivoDownloadporTipo.as_view(), name='archivo--descargar-archivo'),
    path('api/archivos/listar-archivo/', ArchivoDatosporTipo.as_view(), name='archivo--listar-archivo'),
    path('api/archivos/descargar-plantilla/', ArchivoDownloadPlantilla.as_view(), name='archivo-descargar-plantilla'),
    path('api/archivos/plantilla/upload/', ArchivoUploadPlantilla.as_view(), name='archivo-upload'),
    path('api/archivos/plantilla/listar-plantilla/', ArchivoDatosporPlantilla.as_view(), name='archivo-upload'),
    path('api/imagenes/firma/upload/', FirmaUpload.as_view(), name='firma-upload'),
    path('api/imagenes/firma/download/', FirmaDownload.as_view(), name='firma-download'),
    path('api/archivo/firmado/', ArchivoUploadFirmado.as_view(), name='firma-download'),
    path('api/archivo/downdload-plantilla-upload-solicitudes/', DownloadExcelTemplate.as_view(), name='plantilla-download'),
    path('api/archivo/download-manual-usuario/', DescargarManualUsuario.as_view(), name='plantilla-download'),
    path('api/archivo/transferir/historicos/', TransferirArchivosHistoricos.as_view(), name='historicos-transferir'),
    path('api/archivo/descargar/historicos/', DescargarArchivoHistorico.as_view(), name='historicos-descargar'),
    path('api/archivo/datos/descargar/historicos/', DatosArchivoHistorico.as_view(), name='datos-descargar')
]