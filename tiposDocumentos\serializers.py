from rest_framework import serializers

class TipDocumentoSerializer(serializers.Serializer):
    str_CodTipoDocumento  = serializers.CharField(max_length=4)
    str_idSuscripcion = serializers.Char<PERSON>ield(max_length=50)
    str_Nombre = serializers.CharField(max_length=150)
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()

class TipDocumentoUpdateSerializer(serializers.Serializer):
    str_CodTipoDocumento = serializers.CharField(max_length=150)
    str_Nombre = serializers.CharField(max_length=150)
    int_idUsuarioModificacion = serializers.IntegerField()