from rest_framework import serializers

class TagsSerializer(serializers.Serializer):
    int_idSolicitudes  = serializers.IntegerField()
    str_descripcion = serializers.CharField(max_length=255)
    int_idUsuarioCreacion = serializers.IntegerField()

class TagsUpdateSerializer(serializers.Serializer):
    str_descripcion = serializers.CharField(max_length=255)
    int_idUsuarioModificacion = serializers.IntegerField()
