from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
import re
from django.utils import timezone

class UsuariosApiView(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = "SELECT * FROM tm_usuarios WHERE str_idSuscripcion = %s  ORDER BY dt_FechaCreacion DESC"
        params = [str_idSuscripcion]
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'int_idUsuarios  ': row[0],
                    'str_Nombres': row[1],
                    'str_Apellidos': row[2],
                    'int_Documento': row[4]
                    
                }
                for row in rows
            ]
        return Response(clausulas)