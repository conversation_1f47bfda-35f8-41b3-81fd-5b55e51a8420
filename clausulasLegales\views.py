from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import ClausulasLegalesSerializer,ClausulasLegalesUpdateSerializer
import re

class ClausulasLegalesListCreateAPIView(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = "SELECT * FROM tm_clausulaslegales WHERE str_idSuscripcion = %s AND int_idEmpresa = %s "
        params = [str_idSuscripcion,int_idEmpresa]
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'int_idClausulasLegales': row[0],
                    'str_CodClausulasLegales': row[1],
                    'str_idSuscripcion': row[2],
                    'str_Nombre': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreacion': row[6],
                    'int_idUsuarioModificacion': row[7],
                    'int_idEmpresa': row[8]
                }
                for row in rows
            ]
        return Response(clausulas)
    def post(self, request):
        serializer = ClausulasLegalesSerializer(data=request.data)

        if serializer.is_valid():
            suscripcion = serializer.validated_data['str_idSuscripcion']
            int_idEmpresa = serializer.validated_data['int_idEmpresa']
            # Paso 1: Buscar el último código generado para la suscripción
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT str_CodClausulasLegales 
                    FROM tm_clausulaslegales 
                    WHERE str_idSuscripcion = %s AND int_idEmpresa = %s
                    ORDER BY str_CodClausulasLegales DESC 
                    LIMIT 1
                """, [suscripcion,int_idEmpresa])
                
                last_code_result = cursor.fetchone()
                
                if last_code_result:
                    last_code = last_code_result[0]  
                    
                    # Extraer el número del código existente
                    match = re.search(r'\d+$', last_code) 
                    if match:
                        last_number = int(match.group()) 
                    else:
                        last_number = 0  
                    
                    # Incrementar el número para el nuevo código
                    new_number = last_number + 1
                else:
                    # Si no hay ningún código previo, empezar desde 1
                    new_number = 1  
                
                # Paso 2: Formatear el nuevo código con tres dígitos (ejemplo: C001, C010)
                new_code = f"C{str(new_number).zfill(3)}"  # Asegurarse de que siempre tenga 3 dígitos
            
            # Paso 3: Insertar el nuevo código junto con los demás datos
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tm_clausulaslegales 
                (str_CodClausulasLegales, str_idSuscripcion, str_Nombre, dt_FechaCreacion, int_idUsuarioCreacion, int_idEmpresa)
                VALUES (%s, %s, %s, NOW(), %s , %s)
                """
                cursor.execute(query, [
                    new_code,
                    suscripcion,
                    serializer.validated_data['str_Nombre'],
                    serializer.validated_data['int_idUsuarioCreacion'],
                    int_idEmpresa
                ])

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ClausulasLegalesDetailAPIView(APIView):
    def get_object(self, pk):
        # Obtiene un solo registro por clave primaria
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tm_clausulaslegales WHERE int_idClausulasLegales = %s", [pk])
            row = cursor.fetchone()
            if row:
                return {
                    'str_CodClausulasLegales': row[0],
                    'str_CodClausulasLegales': row[1],
                    'str_idSuscripcion': row[2],
                    'str_Nombre': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreacion': row[6],
                    'int_idUsuarioModificacion': row[7],
                }
            return None

    def get(self, request, pk):
        clausula = self.get_object(pk)
        if clausula:
            return Response(clausula)
        return Response(status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk):
        clausula = self.get_object(pk)
        if not clausula:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = ClausulasLegalesUpdateSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                UPDATE tm_clausulaslegales 
                SET  str_CodClausulasLegales=%s,str_Nombre=%s, dt_FechaModificacion=NOW(), int_idUsuarioModificacion=%s 
                WHERE int_idClausulasLegales=%s
                """
                cursor.execute(query, [
                    serializer.validated_data['str_CodClausulasLegales'],
                    serializer.validated_data['str_Nombre'],
                    serializer.validated_data['int_idUsuarioModificacion'],
                    pk
                ])
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tm_clausulaslegales WHERE int_idClausulasLegales = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)