
-- MySQL dump 10.13  Distrib 8.0.39, for Linux (x86_64)
--
-- Host: localhost    Database: greta_seguridad
-- ------------------------------------------------------
-- Server version       8.0.39-0ubuntu0.24.04.2

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `auth_group`
--

DROP TABLE IF EXISTS `auth_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_group_permissions`
--

DROP TABLE IF EXISTS `auth_group_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`) USING BTREE,
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`) USING BTREE,
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_permission`
--

DROP TABLE IF EXISTS `auth_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `content_type_id` int NOT NULL,
  `codename` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`) USING BTREE,
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_user`
--

DROP TABLE IF EXISTS `auth_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `first_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `last_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `email` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `username` (`username`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_user_groups`
--

DROP TABLE IF EXISTS `auth_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user_groups` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `group_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_user_groups_user_id_group_id_94350c0c_uniq` (`user_id`,`group_id`) USING BTREE,
  KEY `auth_user_groups_group_id_97559544_fk_auth_group_id` (`group_id`) USING BTREE,
  CONSTRAINT `auth_user_groups_group_id_97559544_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `auth_user_groups_user_id_6a12ed8b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_user_user_permissions`
--

DROP TABLE IF EXISTS `auth_user_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_user_user_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `auth_user_user_permissions_user_id_permission_id_14a6b632_uniq` (`user_id`,`permission_id`) USING BTREE,
  KEY `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` (`permission_id`) USING BTREE,
  CONSTRAINT `auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `django_admin_log`
--

DROP TABLE IF EXISTS `django_admin_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_admin_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  `object_repr` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `action_flag` smallint unsigned NOT NULL,
  `change_message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `content_type_id` int DEFAULT NULL,
  `user_id` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`) USING BTREE,
  KEY `django_admin_log_user_id_c564eba6_fk_auth_user_id` (`user_id`) USING BTREE,
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_auth_user_id` FOREIGN KEY (`user_id`) REFERENCES `auth_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `django_admin_log_chk_1` CHECK ((`action_flag` >= 0))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `django_content_type`
--

DROP TABLE IF EXISTS `django_content_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_content_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `django_migrations`
--

DROP TABLE IF EXISTS `django_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_migrations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=145 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `django_session`
--

DROP TABLE IF EXISTS `django_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_session` (
  `session_key` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `session_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`) USING BTREE,
  KEY `django_session_expire_date_a5c62663` (`expire_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tc_empresas`
--

DROP TABLE IF EXISTS `tc_empresas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tc_empresas` (
  `int_idEmpresa` int NOT NULL AUTO_INCREMENT,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Ruc` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Moneda` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Pais` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_SimboloMoneda` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  PRIMARY KEY (`int_idEmpresa`) USING BTREE,
  KEY `tc_empresas_str_idSuscripcion_e003eee8_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tc_empresas_str_idSuscripcion_e003eee8_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `th_historialusuarios`
--

DROP TABLE IF EXISTS `th_historialusuarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `th_historialusuarios` (
  `int_idHistorialUsuario` int NOT NULL AUTO_INCREMENT,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaEliminacion` datetime(6) NOT NULL,
  `str_CorreoEliminacion` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idHistorialUsuario`) USING BTREE,
  KEY `th_historialusuarios_str_idSuscripcion_21a6d819_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `th_historialusuarios_str_idSuscripcion_21a6d819_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `th_sesiones`
--

DROP TABLE IF EXISTS `th_sesiones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `th_sesiones` (
  `int_idSesiones` int NOT NULL AUTO_INCREMENT,
  `dt_FechaInicio` datetime(6) NOT NULL,
  `dt_FechaCierre` datetime(6) DEFAULT NULL,
  `str_Estado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ipAddress` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_token` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  `dt_ultimaActividad` datetime(6) DEFAULT NULL,
  `str_correoUsuario` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idSesiones`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_aplicaciones`
--

DROP TABLE IF EXISTS `tm_aplicaciones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_aplicaciones` (
  `int_idAplicacion` int NOT NULL AUTO_INCREMENT,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `fl_avatar` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  `fl_logo` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_appDb` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_estado` tinyint(1) NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idAplicacion`) USING BTREE,
  KEY `tm_aplicaciones_str_idSuscripcion_a069687f_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_aplicaciones_str_idSuscripcion_a069687f_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_especialidades`
--

DROP TABLE IF EXISTS `tm_especialidades`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_especialidades` (
  `int_idEspecialidades` int NOT NULL AUTO_INCREMENT,
  `str_CodigoEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEspecialidad` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuarioCreador` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `dt_FechaCreacion` datetime(6) NOT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idEspecialidades`) USING BTREE,
  KEY `tm_especialidades_str_idSuscripcion_24f6529f_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_especialidades_str_idSuscripcion_24f6529f_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_monedas`
--

DROP TABLE IF EXISTS `tm_monedas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_monedas` (
  `str_codigo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_simbolo` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `bool_estado` tinyint(1) NOT NULL,
  PRIMARY KEY (`str_codigo`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_paises`
--

DROP TABLE IF EXISTS `tm_paises`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_paises` (
  `str_codigo` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `bool_estado` tinyint(1) NOT NULL,
  PRIMARY KEY (`str_codigo`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_perfiles`
--

DROP TABLE IF EXISTS `tm_perfiles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_perfiles` (
  `int_idPerfil` int NOT NULL AUTO_INCREMENT,
  `str_Nombre` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime(6) NOT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idPerfil`) USING BTREE,
  KEY `tm_perfiles_str_idSuscripcion_90dbecda_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_perfiles_str_idSuscripcion_90dbecda_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_suscripcion`
--

DROP TABLE IF EXISTS `tm_suscripcion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RutaIcon` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  `str_RutaLogo` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  PRIMARY KEY (`str_idSuscripcion`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_tipoacceso`
--

DROP TABLE IF EXISTS `tm_tipoacceso`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_tipoacceso` (
  `str_idTipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DescripcionTipoAcceso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`str_idTipoAcceso`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_unidadesnegocios`
--

DROP TABLE IF EXISTS `tm_unidadesnegocios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_unidadesnegocios` (
  `int_idUnidadesNegocio` int NOT NULL AUTO_INCREMENT,
  `str_Descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_fechaCreacion` datetime(6) NOT NULL,
  `dt_fechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreador` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuarioModificador` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idEmpresa` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idUnidadesNegocio`) USING BTREE,
  KEY `tm_unidadesnegocios_int_idEmpresa_ffe30b81_fk_tc_empres` (`int_idEmpresa`) USING BTREE,
  KEY `tm_unidadesnegocios_str_idSuscripcion_edc54284_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tm_unidadesnegocios_int_idEmpresa_ffe30b81_fk_tc_empres` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tm_unidadesnegocios_str_idSuscripcion_edc54284_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tm_usuarios`
--

DROP TABLE IF EXISTS `tm_usuarios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL AUTO_INCREMENT,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(254) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Documento` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Estado` int NOT NULL,
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime(6) DEFAULT NULL,
  `dt_FechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `str_RutaFoto` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci,
  `int_idEspecialidad` int DEFAULT NULL,
  PRIMARY KEY (`int_idUsuarios`) USING BTREE,
  KEY `tm_usuarios_int_idEspecialidad_87c1fb17_fk_tm_especi` (`int_idEspecialidad`) USING BTREE,
  CONSTRAINT `tm_usuarios_int_idEspecialidad_87c1fb17_fk_tm_especi` FOREIGN KEY (`int_idEspecialidad`) REFERENCES `tm_especialidades` (`int_idEspecialidades`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=94 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_accesos`
--

DROP TABLE IF EXISTS `tr_accesos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_accesos` (
  `int_idAccesos` int NOT NULL AUTO_INCREMENT,
  `str_idTipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Valor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idPerfil` int NOT NULL,
  PRIMARY KEY (`int_idAccesos`) USING BTREE,
  KEY `tr_accesos_int_idAplicacion_808d80b4_fk_tm_aplica` (`int_idAplicacion`) USING BTREE,
  KEY `tr_accesos_int_idPerfil_ae8a6749_fk_tm_perfiles_int_idPerfil` (`int_idPerfil`) USING BTREE,
  CONSTRAINT `tr_accesos_int_idAplicacion_808d80b4_fk_tm_aplica` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_accesos_int_idPerfil_ae8a6749_fk_tm_perfiles_int_idPerfil` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_accesousuario`
--

DROP TABLE IF EXISTS `tr_accesousuario`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_accesousuario` (
  `int_idAccesoUsuario` int NOT NULL AUTO_INCREMENT,
  `dt_fechaCreacion` datetime(6) NOT NULL,
  `dt_fechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL,
  `int_idAcceso` int NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idPerfil` int NOT NULL,
  `int_idUsuarios` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idAccesoUsuario`) USING BTREE,
  KEY `tr_accesousuario_int_idAcceso_051cbc83_fk_tr_acceso` (`int_idAcceso`) USING BTREE,
  KEY `tr_accesousuario_int_idAplicacion_571917f5_fk_tm_aplica` (`int_idAplicacion`) USING BTREE,
  KEY `tr_accesousuario_int_idPerfil_afa3f1bf_fk_tm_perfil` (`int_idPerfil`) USING BTREE,
  KEY `tr_accesousuario_int_idUsuarios_315a2012_fk_tm_usuari` (`int_idUsuarios`) USING BTREE,
  KEY `tr_accesousuario_str_idSuscripcion_01ebc7c4_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tr_accesousuario_int_idAcceso_051cbc83_fk_tr_acceso` FOREIGN KEY (`int_idAcceso`) REFERENCES `tr_accesos` (`int_idAccesos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_accesousuario_int_idAplicacion_571917f5_fk_tm_aplica` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_accesousuario_int_idPerfil_afa3f1bf_fk_tm_perfil` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_accesousuario_int_idUsuarios_315a2012_fk_tm_usuari` FOREIGN KEY (`int_idUsuarios`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_accesousuario_str_idSuscripcion_01ebc7c4_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=408 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_asignacionaplicacion`
--

DROP TABLE IF EXISTS `tr_asignacionaplicacion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_asignacionaplicacion` (
  `int_idAsignacion` int NOT NULL AUTO_INCREMENT,
  `bool_esDefault` tinyint(1) NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idSuscriptor` int NOT NULL,
  PRIMARY KEY (`int_idAsignacion`) USING BTREE,
  KEY `tr_asignacionaplicac_int_idAplicacion_5c6b916a_fk_tm_aplica` (`int_idAplicacion`) USING BTREE,
  KEY `tr_asignacionaplicac_int_idSuscriptor_07e81038_fk_tr_suscri` (`int_idSuscriptor`) USING BTREE,
  CONSTRAINT `tr_asignacionaplicac_int_idAplicacion_5c6b916a_fk_tm_aplica` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_asignacionaplicac_int_idSuscriptor_07e81038_fk_tr_suscri` FOREIGN KEY (`int_idSuscriptor`) REFERENCES `tr_suscriptores` (`int_idSuscriptor`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=673 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_disenio`
--

DROP TABLE IF EXISTS `tr_disenio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_disenio` (
  `int_idDisenio` int NOT NULL AUTO_INCREMENT,
  `fl_logo` longblob,
  `fl_icon` longblob,
  `fl_ImagenFondo` longblob,
  `str_nombreLogin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `fl_logoLogin` longblob,
  `fl_imagenLogin` longblob,
  `str_nombreApp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idAplicacion` int NOT NULL,
  PRIMARY KEY (`int_idDisenio`) USING BTREE,
  KEY `tr_disenio_int_idAplicacion_82356109_fk_tm_aplica` (`int_idAplicacion`) USING BTREE,
  CONSTRAINT `tr_disenio_int_idAplicacion_82356109_fk_tm_aplica` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_empresasuscriptor`
--

DROP TABLE IF EXISTS `tr_empresasuscriptor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_empresasuscriptor` (
  `int_idEmpresaSuscriptor` int NOT NULL AUTO_INCREMENT,
  `dt_fechaCreacion` datetime(6) NOT NULL,
  `dt_fechaModificacion` datetime(6) DEFAULT NULL,
  `int_idUsuarioCreador` int NOT NULL,
  `int_idUsuarioModificador` int DEFAULT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idEmpresa` int NOT NULL,
  `int_idSuscriptor` int NOT NULL,
  PRIMARY KEY (`int_idEmpresaSuscriptor`) USING BTREE,
  KEY `tr_empresasuscriptor_int_idAplicacion_e192f476_fk_tm_aplica` (`int_idAplicacion`) USING BTREE,
  KEY `tr_empresasuscriptor_int_idEmpresa_057f074f_fk_tc_empres` (`int_idEmpresa`) USING BTREE,
  KEY `tr_empresasuscriptor_int_idSuscriptor_c7823820_fk_tr_suscri` (`int_idSuscriptor`) USING BTREE,
  CONSTRAINT `tr_empresasuscriptor_int_idAplicacion_e192f476_fk_tm_aplica` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_empresasuscriptor_int_idEmpresa_057f074f_fk_tc_empres` FOREIGN KEY (`int_idEmpresa`) REFERENCES `tc_empresas` (`int_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_empresasuscriptor_int_idSuscriptor_c7823820_fk_tr_suscri` FOREIGN KEY (`int_idSuscriptor`) REFERENCES `tr_suscriptores` (`int_idSuscriptor`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=420 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_log`
--

DROP TABLE IF EXISTS `tr_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `dt_fechaOperacion` datetime(6) NOT NULL,
  `str_operacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_tabla` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuarios` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `tr_log_int_idUsuarios_82f321c8_fk_tm_usuarios_int_idUsuarios` (`int_idUsuarios`) USING BTREE,
  CONSTRAINT `tr_log_int_idUsuarios_82f321c8_fk_tm_usuarios_int_idUsuarios` FOREIGN KEY (`int_idUsuarios`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `tr_suscriptores`
--

DROP TABLE IF EXISTS `tr_suscriptores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tr_suscriptores` (
  `int_idSuscriptor` int NOT NULL AUTO_INCREMENT,
  `str_RolSuscripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `bool_estado` tinyint(1) NOT NULL,
  `int_idUsuarios` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  PRIMARY KEY (`int_idSuscriptor`) USING BTREE,
  KEY `tr_suscriptores_int_idUsuarios_b102549e_fk_tm_usuari` (`int_idUsuarios`) USING BTREE,
  KEY `tr_suscriptores_str_idSuscripcion_0a3d5858_fk_tm_suscri` (`str_idSuscripcion`) USING BTREE,
  CONSTRAINT `tr_suscriptores_int_idUsuarios_b102549e_fk_tm_usuari` FOREIGN KEY (`int_idUsuarios`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `tr_suscriptores_str_idSuscripcion_0a3d5858_fk_tm_suscri` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-02-09 17:38:58
