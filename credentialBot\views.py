from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from django.utils import timezone
from .serializers import CredentialBotSerializer
class CredentialBotApiView(APIView):
    def get(self, request):
        # Obtener el filtro opcional (por suscripci�n o gestor)
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
  
        query = "SELECT * FROM tm_CredentialBot WHERE str_idSuscripcion=%s ORDER BY dt_FechaCreacion DESC"
        params = [str_idSuscripcion]
        
        

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            registros = [
                {   
                    'int_idCredentialBot': row[0],
                    'str_idSuscripcion': row[1],
                    'str_claveApi': row[2],
                    'dt_fechaCambio': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreacion': row[6],
                    'int_idUsuarioModificacion': row[7],
                    'bool_estado' : row[8]
                }
                for row in rows
            ]

        return Response(registros, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = CredentialBotSerializer(data=request.data)
        
        if serializer.is_valid():
            suscripcion = serializer.validated_data['str_idSuscripcion']
            int_idUsuarioModificacion = serializer.validated_data['int_idUsuarioModificacion']
            
            # Obtener el último registro
            select_query = """
            SELECT * FROM tm_CredentialBot
            WHERE str_idSuscripcion = %s
            ORDER BY dt_FechaCreacion DESC
            LIMIT 1;
            """
            params = [suscripcion]

            with connection.cursor() as cursor:
                cursor.execute(select_query, params)
                last_record = cursor.fetchone()

                if last_record:
                    # Si existe un registro anterior, actualizamos su estado
                    update_query = """
                    UPDATE tm_CredentialBot
                    SET dt_FechaModificacion = NOW(),dt_fechaCambio=NOW(), int_idUsuarioModificacion = %s, bool_estado = 0
                    WHERE int_idCredentialBot = %s ;
                    """
                    cursor.execute(update_query, [ int_idUsuarioModificacion, last_record[0]])

                # Realizar el insert en todos los casos
                insert_query = """
                INSERT INTO tm_CredentialBot (
                    str_claveApi, bool_estado, str_idSuscripcion,
                    dt_FechaCreacion, int_idUsuarioCreacion
                ) VALUES (%s, %s,  %s, NOW(), %s);
                """
                cursor.execute(insert_query, [
                    serializer.validated_data['str_claveApi'],
                    1,
                    suscripcion,
                    int_idUsuarioModificacion
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk):
        # Actualizar un registro existente
        data = request.data
        fields = ['str_idSuscripcion', 'str_claveApi', 'dt_fechaCambio', 'int_idUsuarioModificacion']
        updates = []
        params = []

        # Crear la consulta din�mica para actualizar
        for field in fields:
            if field in data:
                updates.append(f"{field} = %s")
                params.append(data[field])

        if not updates:
            return Response(
                {"error": "Debe proporcionar al menos un campo para actualizar."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Agregar campos de auditor�a
        updates.append("dt_FechaModificacion = NOW()")
        query = f"UPDATE tm_CredentialBot SET {', '.join(updates)} WHERE int_idCredentialBot = %s"
        params.append(pk)

        with connection.cursor() as cursor:
            cursor.execute(query, params)

        return Response({"mensaje": "Registro actualizado con exito."}, status=status.HTTP_200_OK)

    def delete(self, request, pk):
        # Eliminar un registro por su ID
        query = "DELETE FROM tm_CredentialBot WHERE int_idCredentialBot = %s"

        with connection.cursor() as cursor:
            cursor.execute(query, [pk])

        return Response({"mensaje": "Registro eliminado con exito."}, status=status.HTTP_200_OK)
    
