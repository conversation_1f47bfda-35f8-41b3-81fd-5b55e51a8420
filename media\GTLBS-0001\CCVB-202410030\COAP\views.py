from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection, DatabaseError
from .serializers import SolicitudSerializer, SolicitudUpdateSerializer,AprobadorSerializer,SolicitudSerializerAdenda
from datetime import datetime
from django.test import RequestFactory
from rest_framework.renderers import JSONRenderer


class SolicitudesTodo(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
        SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
            s.str_idSuscriptor = %s
            OR t.str_descripcion IS NULL
        GROUP BY 
            s.int_idSolicitudes
        ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]                   

                }
                for row in rows
            ]
        return Response(solicitudes)
class SolicitudesArchivadas(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = """
        SELECT 
            s.int_idSolicitudes, 
            s.int_idSolicitante, 
            CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
            s.str_idSuscriptor, 
            s.int_idEmpresa,
            s.int_idEstado, 
            es.str_Nombre AS estado_nombre,
            s.int_idUnidadNegocio, 
            un.str_Descripcion,
            s.str_DeTerceros, 
            s.str_Visible, 
            s.int_idTipoSol,
            ts.str_Nombre AS nombre_TipoSolicitud, 
            s.dt_FechaRegistro, 
            s.dt_FechaEsperada, 
            s.int_idGestor, 
            s.int_idClienteAsociado, 
            e.str_NombreEmpresa, 
            e.str_RazonSocial, 
            e.str_Ruc,
            GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
            s.str_CodSolicitudes,
            s.db_Honorarios,
            s.int_SolicitudGuardada,
            ts.str_CodTipoSol,
             MIN(stc.str_Moneda) AS moneda
        FROM 
            tr_solicitudes s
        LEFT JOIN 
            tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
        LEFT JOIN 
            tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
        LEFT JOIN 
            tm_estados es ON s.int_idEstado = es.int_idEstado 
        LEFT JOIN 
            tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
        LEFT JOIN 
            tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
        LEFT JOIN 
            tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
        LEFT JOIN 
            tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
        WHERE  
            s.str_idSuscriptor = %s
            AND s.str_Visible = 'no'
            OR t.str_descripcion IS NULL
        GROUP BY 
            s.int_idSolicitudes
        ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                   'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class SolicitudesVisibles(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
                       SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE    
                s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
            GROUP BY 
            s.int_idSolicitudes;
            ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class FiltrarSolicitudes(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')

        query = """
                   SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.str_idSuscriptor = %s
                
         
        """
        params = [str_idSuscriptor]

        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)

        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)

        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)

         
        query += " GROUP BY s.int_idSolicitudes  ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes, status=status.HTTP_200_OK)
class MatrizGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.str_CodSolicitudes, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_solicitante,
                sc.db_Presupuesto,
                e.str_NombreEmpresa, 
                un.str_Descripcion,
                es.str_Nombre AS estado_nombre,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                sc.str_ObjetivoContrato,
                s.dt_FirmaContrato,
                s.dt_FechaRegistro,
                CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos) AS nombre_gestor,
                sc.str_RenovacionAuto,
                sc.str_Garantia,
                s.db_Honorarios,
                sc.str_Margen,
                sc.str_FormaPago,
                sc.str_ResolucionAnticipada,
                s.int_HorasTrabajadas,
                s.int_idSolicitudes,
                 s.dt_FechaFin,
                sc.str_Moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN  
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor  = ug.int_idUsuarios 
            LEFT JOIN 
                tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
            WHERE  
                s.str_idSuscriptor = %s
                AND YEAR(s.dt_FechaRegistro) = %s 
            ORDER BY 
                s.dt_FechaRegistro DESC;
        """
        params = [ str_idSuscriptor,año_filtro]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                    'Moneda':row[20]
                }
                for row in rows
            ]
        return Response(solicitudes)
class AsignarGestor(APIView):
    def put(self, request):
        int_idSolicitudes  = request.data.get('int_idSolicitudes')        
        int_idGestor = request.data.get('int_idGestor')
        int_idUsuarioModificacion = request.data.get('int_idUsuarioModificacion')
        fecha_actual = datetime.now()

        try:
            with connection.cursor() as cursor:

                update_query = """
                    UPDATE tr_solicitudes 
                    SET int_idGestor = %s, int_idUsuarioModificacion = %s, dt_FechaModificacion = %s
                    WHERE int_idSolicitudes = %s
                """
                update_params = [int_idGestor, int_idUsuarioModificacion, fecha_actual, int_idSolicitudes ]

            with connection.cursor() as cursor:
                cursor.execute(update_query, update_params)
                if cursor.rowcount == 0:
                    return Response(
                        {"error": "No se encontró la solicitud con el ID proporcionado."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            return Response(
                {"mensaje": "El Gestor fue asignado a la solicitud."},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class FirmarContrato(APIView):
    def put(self, request):
        int_idSolicitudes  = request.data.get('int_idSolicitudes')        
        dt_FirmaContrato = request.data.get('dt_FirmaContrato')
        dt_FechaFin = request.data.get('dt_FechaFin')
        int_HorasTrabajadas = request.data.get('int_HorasTrabajadas')
        int_idUsuarioModificacion = request.data.get('int_idUsuarioModificacion')
        fecha_actual = datetime.now()

        try:
            with connection.cursor() as cursor:

                update_query = """
                    UPDATE tr_solicitudes 
                    SET dt_FirmaContrato = %s,int_HorasTrabajadas = %s, int_idUsuarioModificacion = %s, dt_FechaModificacion = %s,dt_FechaFin=%s
                    WHERE int_idSolicitudes = %s
                """
                update_params = [dt_FirmaContrato,int_HorasTrabajadas, int_idUsuarioModificacion, fecha_actual,dt_FechaFin, int_idSolicitudes ]

            with connection.cursor() as cursor:
                cursor.execute(update_query, update_params)
                if cursor.rowcount == 0:
                    return Response(
                        {"error": "No se encontró la solicitud con el ID proporcionado."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            return Response(
                {"mensaje": "El contrato fue subido correctamente."},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class EditarEstadoSolicitud(APIView):
    def put(self, request):
        int_idSolicitudes  = request.data.get('int_idSolicitudes')
        nombre_estado = request.data.get('nombre_estado')
        int_idUsuarioCreacion = request.data.get('int_idUsuarioCreacion')
        str_idSuscriptor = request.data.get('str_idSuscriptor')
        fecha_actual = datetime.now()

        try:
            with connection.cursor() as cursor:
                select_query = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_idSuscripcion  = %s AND str_Nombre = %s
                """
                cursor.execute(select_query, [str_idSuscriptor, nombre_estado])
                estado = cursor.fetchone()
                if not estado:
                    return Response(
                        {"error": "No se encontró un estado con el 'nombre_estado' y 'str_idSuscriptor' proporcionados."},
                        status=status.HTTP_404_NOT_FOUND
                    )
                else:
                        estado[0],

                
                str_idEstado = estado[0]

            # Realiza el UPDATE para cambiar el estado de la solicitud
            update_query = """
                UPDATE tr_solicitudes 
                SET int_idEstado = %s, int_idUsuarioModificacion = %s, dt_FechaModificacion = %s
                WHERE int_idSolicitudes = %s
            """
            update_params = [str_idEstado, int_idUsuarioCreacion, fecha_actual, int_idSolicitudes ]

            with connection.cursor() as cursor:
                cursor.execute(update_query, update_params)
                if cursor.rowcount == 0:
                    return Response(
                        {"error": "No se encontró la solicitud con el ID proporcionado."},
                        status=status.HTTP_404_NOT_FOUND
                    )

            # Registra el cambio de estado en la tabla tr_estadoregistros
            insert_query = """
                INSERT INTO tr_estadoregistros (int_idSolicitudes,str_idSuscriptor  , int_idEstado, dt_FechaCambio, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s,%s, %s, %s, %s)
            """
            insert_params = [int_idSolicitudes ,str_idSuscriptor, str_idEstado, fecha_actual, fecha_actual, int_idUsuarioCreacion]

            with connection.cursor() as cursor:
                cursor.execute(insert_query, insert_params)

            return Response(
                {"mensaje": "El estado de la solicitud se ha actualizado correctamente y el cambio ha sido registrado."},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class HistorialEstadosSolicitud(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitudes = request.query_params.get('int_idSolicitudes')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT
                er.int_idEstadoRegistros,
                er.str_idSuscriptor,
                er.int_idSolicitudes,
                s.int_idSolicitante,
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                er.int_idEstado,
                e.str_Nombre,
                er.dt_FechaCambio
            FROM
                tr_estadoregistros er
            INNER JOIN 
                tm_estados e ON er.int_idEstado = e.int_idEstado
            INNER JOIN 
                tr_solicitudes s ON er.int_idSolicitudes = s.int_idSolicitudes
             INNER JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios
            WHERE
                er.str_idSuscriptor = %s && er.int_idSolicitudes = %s
            ORDER BY 
               s.dt_FechaRegistro DESC;
        """
        params = [str_idSuscriptor,int_idSolicitudes]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idEstadoRegistros': row[0],
                    'str_idSuscriptor': row[1],
                    'int_idSolicitudes': row[2],
                    'Solicitante': row[3],
                    'Nombres_solicitante': row[4],
                    'Estado': row[5],
                    'Nombre_estado': row[6],
                    'fecha_Cambio': row[7],

                    
                }
                for row in rows
            ]
        return Response(solicitudes)
class ArchivarSolicitud(APIView):
    def put(self, request):
        int_idSolicitudes = request.data.get('int_idSolicitudes')
        int_idUsuarioModificacion  = request.data.get('int_idUsuarioModificacion ')
        fecha_actual = datetime.now()

        if not int_idSolicitudes :
            return Response(
                {"error": "Los campos 'int_idSolicitudes' y 'str_idEstado' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        update_query = """
            UPDATE tr_solicitudes 
            SET str_Visible = %s,int_idUsuarioModificacion = %s,dt_FechaModificacion = %s
            WHERE int_idSolicitudes = %s
        """
        update_params = ["no", int_idUsuarioModificacion,fecha_actual,int_idSolicitudes]

        with connection.cursor() as cursor:
            cursor.execute(update_query, update_params)
            if cursor.rowcount == 0:
                return Response(
                    {"error": "No se encontró la solicitud con el ID proporcionado."},
                    status=status.HTTP_404_NOT_FOUND
                )


        return Response(
            {"mensaje": "El estado de la solicitud se ha actualizado correctamente y el cambio ha sido registrado."},
            status=status.HTTP_200_OK
        )
class SolicitudesGestor(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.int_idGestor = %s
                && s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
                
        """
        params = [int_idGestor,str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                     'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]

                }
                for row in rows
            ]
        return Response(solicitudes)

class MatrizGestor(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')
        año_filtro = request.query_params.get('year')
        str_CodSolicitudes = request.query_params.get('str_CodSolicitudes')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
SELECT 
    s.str_CodSolicitudes, 
    MAX(COALESCE(ca.str_Interlocutor, u.str_Interlocutor)) AS str_Interlocutor,
    MAX(sc.db_Presupuesto) AS db_Presupuesto,
    MAX(e.str_NombreEmpresa) AS str_NombreEmpresa, 
    MAX(un.str_Descripcion) AS str_Descripcion,
    MAX(es.str_Nombre) AS estado_nombre,
    MAX(ts.str_Nombre) AS nombre_TipoSolicitud, 
    MAX(sc.str_ObjetivoContrato) AS str_ObjetivoContrato,
    MAX(s.dt_FirmaContrato) AS dt_FirmaContrato,
    MAX(s.dt_FechaRegistro) AS dt_FechaRegistro,
    MAX(CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos)) AS nombre_gestor,
    MAX(sc.str_RenovacionAuto) AS str_RenovacionAuto,
    MAX(sc.str_Garantia) AS str_Garantia,
    MAX(s.db_Honorarios) AS db_Honorarios,
    MAX(sc.str_Margen) AS str_Margen,
    MAX(sc.str_FormaPago) AS str_FormaPago,
    MAX(sc.str_ResolucionAnticipada) AS str_ResolucionAnticipada,
    MAX(s.int_HorasTrabajadas) AS int_HorasTrabajadas,
    MAX(s.int_idSolicitudes) AS int_idSolicitudes,
    MAX(s.dt_FechaFin) AS dt_FechaFin,
    MAX(sc.str_Moneda) AS str_Moneda,
    MAX(sc.str_Penalidades) AS str_Penalidades,
    MAX(CASE 
        WHEN clale.str_Nombre = 'Clausula de no competencia' THEN 'Sí'
        ELSE NULL
    END) AS Clausula
FROM 
    tr_solicitudes s
LEFT JOIN 
    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
LEFT JOIN 
    tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
LEFT JOIN 
    tm_estados es ON s.int_idEstado = es.int_idEstado 
LEFT JOIN 
    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
LEFT JOIN 
    tm_usuarios ug ON s.int_idSolicitante = ug.int_idUsuarios 
LEFT JOIN 
    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
LEFT JOIN 
    tm_interlocutores ca ON sc.int_idInterlocutor = ca.int_idInterlocutor
LEFT JOIN 
    tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor     
LEFT JOIN 
    tr_clausulasactivas claac ON s.int_idSolicitudes = claac.int_idSolicitudes      
LEFT JOIN 
    tr_clausulasincluidas clain ON claac.int_idClausulaIncluida = clain.int_idClausulasIncluidas
LEFT JOIN 
    tm_clausulaslegales clale ON clain.int_idClausulaLegal = clale.int_idClausulasLegales         
WHERE  
    s.int_idGestor = %s
    AND s.str_idSuscriptor = %s

"""
        

        params = [int_idGestor, str_idSuscriptor]

       
        if año_filtro and str_CodSolicitudes:
    
            query += """
                AND (
                    (s.str_CodSolicitudes = %s AND YEAR(s.dt_FechaRegistro) = %s)
                    OR s.str_CodSolicitudes LIKE %s
                )
            """
            params.extend([str_CodSolicitudes, año_filtro, f"%{str_CodSolicitudes}%"])
        elif año_filtro:
          
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(año_filtro)
        elif str_CodSolicitudes:
            
            query += " AND s.str_CodSolicitudes LIKE %s"
            params.append(f"%{str_CodSolicitudes}%")

      
        if str_CodSolicitudes:
            query += "GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro  ORDER BY s.dt_FechaRegistro ASC;"
        else:
            query += " GROUP BY s.str_CodSolicitudes,s.dt_FechaRegistro ORDER BY s.dt_FechaRegistro DESC ;"


        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente/Proveedor': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                    'Moneda': row[20],
                    'Penalidades': row[21],
                    'Clausula de no competencia': row[22],
                }
                for row in rows
            ]
        return Response(solicitudes)

class FiltrarSolicitudesGestor(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        int_idGestor = request.query_params.get('int_idGestor')

        # Consulta base
        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN  
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.str_idSuscriptor = %s 
                AND s.int_idGestor = %s
        """
        params = [str_idSuscriptor, int_idGestor]

        # Añadir condiciones de filtrado
        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)

        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)

        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)

        # Añadir condiciones para tags
        if str_descripcion:
            # Buscar solicitudes que tengan al menos uno de los tags
            tag_conditions = []
            for tag in str_descripcion:
                tag_conditions.append(f"t.str_descripcion LIKE %s")
                params.append(f'%{tag}%')
            tag_query = " OR ".join(tag_conditions)

            # Añadir la condición de tags a la consulta principal
            query += f"""
                AND s.int_idSolicitudes IN (
                    SELECT DISTINCT t.int_idSolicitudes
                    FROM tr_tags t
                    WHERE {tag_query}
                )
            """

        # Agrupar resultados
        query += " GROUP BY s.int_idSolicitudes ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]

        return Response(solicitudes, status=status.HTTP_200_OK)
class SolicitudesController(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                 s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
                AND s.int_idGestor = %s
                OR es.str_Nombre = "Nuevo"
            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
                
        """
        params = [str_idSuscriptor,int_idGestor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                      'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class FiltrarSolicitudesController(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        int_idGestor = request.query_params.get('int_idGestor')

        query = """
           SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.str_idSuscriptor = %s AND s.int_idGestor = %s OR es.str_Nombre = "Nuevo"
        """
        params = [str_idSuscriptor,int_idGestor]

        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)

        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)

        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)

        if str_descripcion:
            query += " AND (t.str_descripcion IN %s OR t.str_descripcion IS NULL)"
            params.append(tuple(str_descripcion))

        query += " GROUP BY s.int_idSolicitudes ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                      'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes, status=status.HTTP_200_OK)
class SolicitudesSolicitante(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitante = request.query_params.get('int_idSolicitante')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.int_idSolicitante = %s
                && s.str_idSuscriptor = %s 
                AND s.str_Visible = 'si'
            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
        """
        params = [int_idSolicitante,str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]
        return Response(solicitudes)
class FiltrarSolicitudesSolicitante(APIView):
    def get(self, request):
        str_idSolicitudes = request.query_params.getlist('str_idSolicitudes')
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        mes_fecha_registro = request.query_params.get('mes_fecha_registro')
        str_idEmpresa = request.query_params.getlist('str_idEmpresa')
        estado = request.query_params.get('estado')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_Visible = request.query_params.get('str_Visible')
        str_idSuscriptor = request.query_params.getlist('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        int_idSolicitante = request.query_params.get('int_idSolicitante')

        # Consulta base
        query = """
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                un.str_Descripcion,
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol,
                MIN(stc.str_Moneda) AS moneda
            FROM 
                tr_solicitudes s
            LEFT JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            LEFT JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN  
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            LEFT JOIN 
                tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.str_idSuscriptor = %s 
                AND s.int_idSolicitante = %s
        """
        params = [str_idSuscriptor, int_idSolicitante]

        # Añadir condiciones de filtrado
        if str_idSolicitudes:
            query += " AND s.str_idSolicitudes IN %s"
            params.append(tuple(str_idSolicitudes))

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if mes_fecha_registro:
            query += " AND MONTH(s.dt_FechaRegistro) = %s"
            params.append(mes_fecha_registro)

        if str_idEmpresa:
            query += " AND s.int_idEmpresa IN %s"
            params.append(tuple(str_idEmpresa))

        if str_Visible:
            query += " AND s.str_Visible = %s"
            params.append(str_Visible)

        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)

        if estado:
            query += " AND es.str_Nombre = %s"
            params.append(estado)

        # Añadir condiciones para tags
        if str_descripcion:
            # Buscar solicitudes que tengan al menos uno de los tags
            tag_conditions = []
            for tag in str_descripcion:
                tag_conditions.append(f"t.str_descripcion LIKE %s")
                params.append(f'%{tag}%')
            tag_query = " OR ".join(tag_conditions)

            # Añadir la condición de tags a la consulta principal
            query += f"""
                AND s.int_idSolicitudes IN (
                    SELECT DISTINCT t.int_idSolicitudes
                    FROM tr_tags t
                    WHERE {tag_query}
                )
            """

        # Agrupar resultados
        query += " GROUP BY s.int_idSolicitudes ORDER BY s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25]
                }
                for row in rows
            ]

        return Response(solicitudes, status=status.HTTP_200_OK)
class SolicitudesAprobador(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idAprobador = request.query_params.get('int_idAprobador')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
        SELECT 
            s.int_idSolicitudes,  
            s.int_idSolicitante, 
            CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
            s.str_idSuscriptor, 
            s.int_idEmpresa,
            s.int_idEstado, 
            es.str_Nombre AS estado_nombre,
            s.int_idUnidadNegocio, 
            un.str_Descripcion,
            s.str_DeTerceros, 
            s.str_Visible, 
            s.int_idTipoSol,
            ts.str_Nombre AS nombre_TipoSolicitud, 
            s.dt_FechaRegistro, 
            s.dt_FechaEsperada, 
            s.int_idGestor, 
            s.int_idClienteAsociado, 
            e.str_NombreEmpresa, 
            e.str_RazonSocial, 
            e.str_Ruc,
            GROUP_CONCAT(DISTINCT t.str_descripcion SEPARATOR ', ') AS tags, -- Asegurando que los tags no se repitan
            s.str_CodSolicitudes,
            s.db_Honorarios,
            s.int_SolicitudGuardada,
            ts.str_CodTipoSol,
            MIN(stc.str_Moneda) AS moneda,
            MIN(a.dt_FechaAceptacion) AS fechaAceptacion
        FROM 
            tr_solicitudes s
        LEFT JOIN 
            tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
        LEFT JOIN 
            tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
        LEFT JOIN  
            tm_estados es ON s.int_idEstado = es.int_idEstado 
        LEFT JOIN 
            tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
        LEFT JOIN 
            tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
        LEFT JOIN 
            tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
        LEFT JOIN 
            tr_solicitudcont stc ON stc.int_idSolicitudes = s.int_idSolicitudes 
        LEFT JOIN 
            tr_aprobadores a ON a.int_idSolicitudes = s.int_idSolicitudes   
        WHERE    
            a.int_idUsuario = %s
            AND s.str_idSuscriptor = %s
            AND es.str_Nombre = "En Aprobacion"
            AND s.str_Visible = 'si'
        GROUP BY 
            s.int_idSolicitudes
        ORDER BY 
            s.dt_FechaRegistro DESC;
        """
        params = [int_idAprobador,str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_Descripcion_UnidadNegocio': row[8],
                    'str_DeTerceros': row[9],
                    'str_Visible': row[10],
                    'int_idTipoSol': row[11],
                    'nombre_TipoSolicitud': row[12],
                    'dt_FechaRegistro': row[13],
                    'dt_FechaEsperada': row[14],
                    'int_idGestor': row[15],
                    'int_idClienteAsociado': row[16],
                    'str_NombreEmpresa': row[17],
                    'str_RazonSocial': row[18],
                    'str_Ruc': row[19],
                    'tags': row[20],
                    'str_CodSolicitudes': row[21],
                    'db_Honorarios': row[22],
                    'int_SolicitudGuardada': row[23],
                    'str_CodTipoSol': row[24],
                    'str_Moneda': row[25],
                    'dt_FechaAceptacion': row[26]
                }
                for row in rows
            ]
        return Response(solicitudes)

class SolicitudesCreate(APIView):
    def post(self, request):
        serializer = SolicitudSerializer(data=request.data)
        if serializer.is_valid():
            int_idTipoSolicitud = serializer.validated_data['int_idTipoSol']
            str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
            int_idUsuarioCreacion=serializer.validated_data['int_idUsuarioCreacion'],

            fecha_actual = datetime.now().strftime("%Y%m")  
            try:
                with connection.cursor() as cursor:
                    select_query = """
                    SELECT str_CorrelativoTipoSolicitud 
                    FROM tr_correlativosolicitud 
                    WHERE int_idTipoSolicitud = %s AND str_idSuscriptor = %s 
                    """
                    cursor.execute(select_query, [int_idTipoSolicitud, str_idSuscriptor])
                    resultado = cursor.fetchone()
                    print(resultado)
                    if resultado:
                        correlativo_actual = int(resultado[0])
                        nuevo_correlativo = correlativo_actual + 1
                    else:
                        nuevo_correlativo = 1

                    nuevo_correlativo_str = f"{nuevo_correlativo:03d}" 
                    print(nuevo_correlativo_str)
                    if resultado:
                        update_query = """
                        UPDATE tr_correlativosolicitud 
                        SET str_CorrelativoTipoSolicitud = %s , dt_FechaModificacion = NOW() , int_idUsuarioModificacion =%s
                        WHERE int_idTipoSolicitud = %s AND str_idSuscriptor = %s 
                        """
                        cursor.execute(update_query, [nuevo_correlativo_str,int_idUsuarioCreacion, int_idTipoSolicitud, str_idSuscriptor])
                    else:
                        insert_query = """
                        INSERT INTO tr_correlativosolicitud (int_idCorrelativo, str_idSuscriptor, int_idTipoSolicitud, str_CorrelativoTipoSolicitud, dt_FechaCreacion) 
                        VALUES (DEFAULT, %s, %s, %s, NOW())
                        """
                        cursor.execute(insert_query, [str_idSuscriptor, int_idTipoSolicitud, nuevo_correlativo_str])
                    select_queryTS = """
                    SELECT str_CodTipoSol 
                    FROM tm_tiposolicitud 
                    WHERE int_idTipoSolicitud = %s AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryTS, [int_idTipoSolicitud, str_idSuscriptor])
                    resultadoTS = cursor.fetchone()
                    if not resultadoTS:
                        return Response({"error": "Tipo de solicitud no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    codTipoSolicitud = resultadoTS[0]
                    str_CodSolicitudes = f"{codTipoSolicitud}-{fecha_actual}{nuevo_correlativo_str}"
                    select_queryES = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_Nombre = "Nuevo" AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryES, [str_idSuscriptor])
                    resultadoES = cursor.fetchone()
                    if not resultadoES:
                        return Response({"error": "Estado no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    Id_estado = resultadoES[0]
                    insert_solicitud_query = """
                    INSERT INTO tr_solicitudes 
                    (str_CodSolicitudes, int_idSolicitante, str_idSuscriptor, int_idEmpresa ,db_Honorarios, int_idEstado, int_idUnidadNegocio,int_SolicitudGuardada , 
                    str_DeTerceros, str_Visible, int_idTipoSol, dt_FechaRegistro, dt_FechaEsperada, dt_FechaCreacion, int_idUsuarioCreacion,int_idClienteAsociado) 
                    VALUES (%s, %s, %s, %s, %s, %s,%s, %s,%s, %s, %s,NOW(),  %s, NOW(), %s, %s)
                    """
                    int_idSolicitante = serializer.validated_data.get('int_idSolicitante', None)
                    int_idEmpresa = serializer.validated_data.get('int_idEmpresa', None)
                    db_Honorarios = serializer.validated_data.get('db_Honorarios', None)
                    int_idUnidadNegocio = serializer.validated_data.get('int_idUnidadNegocio', None)
                    int_SolicitudGuardada = serializer.validated_data.get('int_SolicitudGuardada', "no")
                    str_DeTerceros = serializer.validated_data.get('str_DeTerceros', None)
                    dt_FechaEsperada = serializer.validated_data.get('dt_FechaEsperada', None)
                    int_idClienteAsociado  = serializer.validated_data.get('int_idClienteAsociado', None)

                    cursor.execute(insert_solicitud_query, [
                        str_CodSolicitudes,
                       int_idSolicitante,
                        str_idSuscriptor,
                        int_idEmpresa,
                        db_Honorarios,
                        Id_estado,
                        int_idUnidadNegocio,
                        int_SolicitudGuardada,
                        str_DeTerceros,
                        "si",
                        int_idTipoSolicitud,
                        dt_FechaEsperada,
                        int_idUsuarioCreacion,
                        int_idClienteAsociado
                        
                    ])
                    solicitud_id = cursor.lastrowid
                    
                return Response({
                    "data": serializer.data,
                    "int_idSolicitudes": solicitud_id,
                    "str_CodSolicitudes": str_CodSolicitudes
                }, status=status.HTTP_201_CREATED)
            except DatabaseError as e:
                return Response({"error": "Error de base de datos: " + str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SolicitudesCreateAdenda(APIView):
    def post(self, request):
        serializer = SolicitudSerializerAdenda(data=request.data)
        if serializer.is_valid():
            int_idTipoSolicitud = serializer.validated_data['int_idTipoSol']
            str_idSuscriptor = serializer.validated_data['str_idSuscriptor']
            int_idUsuarioCreacion = serializer.validated_data['int_idUsuarioCreacion']
            str_CodSolicitudes = serializer.validated_data['str_CodSolicitudes']
            fecha_actual = datetime.now().strftime("%Y%m")  

            try:
                with connection.cursor() as cursor:
                    # Buscar si ya existe un código similar
                    base_cod_solicitud = f"{str_CodSolicitudes}-A"
                    select_query = """
                    SELECT str_CodSolicitudes 
                    FROM tr_solicitudes 
                    WHERE str_CodSolicitudes LIKE %s
                    """
                    cursor.execute(select_query, [f"{base_cod_solicitud}%"])
                    resultados = cursor.fetchall()

                    # Calcular el siguiente sufijo -A1, -A2, etc.
                    if resultados:
                        numeros_existentes = []
                        for resultado in resultados:
                            cod_existente = resultado[0]
                            try:
                                # Extraer el número al final del código, por ejemplo: "-A2"
                                sufijo_numero = cod_existente.split("-A")[1]
                                numeros_existentes.append(int(sufijo_numero))
                            except (IndexError, ValueError):
                                continue
                        # Asignar el siguiente número disponible
                        if numeros_existentes:
                            next_number = max(numeros_existentes) + 1
                        else:
                            next_number = 1
                    else:
                        next_number = 1

                    # Formar el nuevo código con el número
                    str_CodSolicitudes = f"{base_cod_solicitud}{next_number}"

                    # Obtener el estado
                    select_queryES = """
                    SELECT int_idEstado 
                    FROM tm_estados 
                    WHERE str_Nombre = "Nuevo" AND str_idSuscripcion = %s
                    """
                    cursor.execute(select_queryES, [str_idSuscriptor])
                    resultadoES = cursor.fetchone()
                    if not resultadoES:
                        return Response({"error": "Estado no encontrado."}, status=status.HTTP_404_NOT_FOUND)
                    Id_estado = resultadoES[0]

                    # Insertar la nueva solicitud
                    insert_solicitud_query = """
                    INSERT INTO tr_solicitudes 
                    (str_CodSolicitudes, int_idSolicitante, str_idSuscriptor, int_idEmpresa ,db_Honorarios, int_idEstado, int_idUnidadNegocio, int_SolicitudGuardada, 
                    str_DeTerceros, str_Visible, int_idTipoSol, dt_FechaRegistro, dt_FechaEsperada, dt_FechaCreacion, int_idUsuarioCreacion, int_idClienteAsociado) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, NOW(), %s, %s)
                    """
                    int_idSolicitante = serializer.validated_data.get('int_idSolicitante', None)
                    int_idEmpresa = serializer.validated_data.get('int_idEmpresa', None)
                    db_Honorarios = serializer.validated_data.get('db_Honorarios', None)
                    int_idUnidadNegocio = serializer.validated_data.get('int_idUnidadNegocio', None)
                    int_SolicitudGuardada = serializer.validated_data.get('int_SolicitudGuardada', "no")
                    str_DeTerceros = serializer.validated_data.get('str_DeTerceros', None)
                    dt_FechaEsperada = serializer.validated_data.get('dt_FechaEsperada', None)
                    int_idClienteAsociado = serializer.validated_data.get('int_idClienteAsociado', None)

                    cursor.execute(insert_solicitud_query, [
                        str_CodSolicitudes,
                        int_idSolicitante,
                        str_idSuscriptor,
                        int_idEmpresa,
                        db_Honorarios,
                        Id_estado,
                        int_idUnidadNegocio,
                        int_SolicitudGuardada,
                        str_DeTerceros,
                        "si",
                        int_idTipoSolicitud,
                        dt_FechaEsperada,
                        int_idUsuarioCreacion,
                        int_idClienteAsociado
                    ])
                    solicitud_id = cursor.lastrowid
                    
                return Response({
                    "data": serializer.data,
                    "int_idSolicitudes": solicitud_id,
                    "str_CodSolicitudes": str_CodSolicitudes
                }, status=status.HTTP_201_CREATED)
            except DatabaseError as e:
                return Response({"error": "Error de base de datos: " + str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SolicitudDetailAPIView(APIView):
    def get_object(self, pk):
        with connection.cursor() as cursor:
            cursor.execute("""
            SELECT 
                s.int_idSolicitudes, 
                s.int_idSolicitante, 
                CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo,
                s.str_idSuscriptor, 
                s.int_idEmpresa,
                s.int_idEstado, 
                es.str_Nombre AS estado_nombre,
                s.int_idUnidadNegocio, 
                s.str_DeTerceros, 
                s.str_Visible, 
                s.int_idTipoSol,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                s.int_idGestor, 
                s.int_idClienteAsociado, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos) AS nombre_gestor,
                s.int_SolicitudGuardada,
                ts.str_CodTipoSol
            FROM 
                tr_solicitudes s
            INNER JOIN 
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            INNER JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            INNER JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            INNER JOIN 
                tm_usuarios u ON s.int_idSolicitante = u.int_idUsuarios 
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            WHERE  
                s.int_idSolicitudes = %s 

            GROUP BY 
                s.int_idSolicitudes
            ORDER BY 
                s.dt_FechaRegistro DESC;
            """, [pk])
            row = cursor.fetchone()
            if row:
                return {
                    'int_idSolicitudes': row[0],
                    'int_idSolicitante': row[1],
                    'nombre_completo': row[2],
                    'str_idSuscriptor': row[3],
                    'int_idEmpresa': row[4],
                    'int_idEstado': row[5],
                    'estado_nombre': row[6],
                    'int_idUnidadNegocio': row[7],
                    'str_DeTerceros': row[8],
                    'str_Visible': row[9],
                    'int_idTipoSol': row[10],
                    'nombre_TipoSolicitud': row[11],
                    'dt_FechaRegistro': row[12],
                    'dt_FechaEsperada': row[13],
                    'int_idGestor': row[14],
                    'int_idClienteAsociado': row[15],
                    'str_NombreEmpresa': row[16],
                    'str_RazonSocial': row[17],
                    'str_Ruc': row[18],
                    'tags': row[19],
                    'nombre_gestor': row[20],
                    'int_SolicitudGuardada':row[21],
                    'str_CodTipoSol':row[22]
                }
            return None


    def get(self, request, pk):
        solicitud = self.get_object(pk)
        if solicitud:
            return Response(solicitud)
        return Response(
            {"error": "Solicitud no encontrada."},
            status=status.HTTP_404_NOT_FOUND
        )
    def put(self, request, pk):
        # Instanciar el serializer con los datos del request
        serializer = SolicitudUpdateSerializer(data=request.data)
        
        # Validar los datos recibidos
        if serializer.is_valid():
            data = serializer.validated_data
            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE tr_solicitudes
                    SET 
                        int_idEmpresa = %s, 
                        int_idUnidadNegocio = %s, 
                        str_DeTerceros = %s, 
                        str_Visible = 'si', 
                        int_idTipoSol = %s, 
                        dt_FechaEsperada = %s, 
                        int_idClienteAsociado = %s,
                        int_SolicitudGuardada = %s,
                        dt_FechaModificacion= NOW(),
                        int_idUsuarioModificacion =%s,
                        db_Honorarios =%s
                    WHERE 
                        int_idSolicitudes = %s
                """, [
                    data['int_idEmpresa'],
                    data['int_idUnidadNegocio'],
                    data['str_DeTerceros'],
                    data['int_idTipoSol'],
                    data['dt_FechaEsperada'],
                    data.get('int_idClienteAsociado'),
                    data['int_SolicitudGuardada'],
                    data['int_idUsuarioModificacion'],
                    data['db_Honorarios'],
                    pk

                ])
                
                return Response({'message': 'Solicitud actualizada correctamente'}, status=status.HTTP_200_OK)
        else:
            # Si los datos no son válidos, devolver errores de validación
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tr_solicitudes WHERE int_idSolicitudes = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)

class InsertarAprobadorView(APIView):

    def post(self, request, format=None):
        serializer = AprobadorSerializer(data=request.data)
        
        if serializer.is_valid():
            aprobador_data = serializer.validated_data
            
            sql_query = '''
                INSERT INTO tr_aprobadores (
                    str_idSuscripcion,
                    int_idUsuario,
                    int_idSolicitudes,
                    int_OrdenAprobacion,
                    int_EstadoAprobacion,
                    dt_FechaCreacion,
                    int_idUsuarioCreacion
                ) VALUES ( %s, %s, %s, %s, %s, NOW(), %s)
            '''

            # Definir los valores que se van a insertar
            values = (
                aprobador_data['str_idSuscripcion'],
                aprobador_data['int_idUsuario'],
                aprobador_data['int_idSolicitudes'],
                aprobador_data['int_OrdenAprobacion'],
                0,
                aprobador_data['int_idUsuarioCreacion']
            )

            try:
                # Ejecutar la consulta SQL manual
                with connection.cursor() as cursor:
                    cursor.execute(sql_query, values)
                
                # Respuesta exitosa si todo sale bien
                return Response({'message': 'Aprobador insertado correctamente'}, status=status.HTTP_201_CREATED)

            except Exception as e:
                # Enviar respuesta de error si algo falla
                return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        else:
            # Responder con errores de validación del serializer
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
class ListarAprobadoresView(APIView):

    def get(self, request, str_idSuscripcion, int_idSolicitudes, format=None):
        try:
            # Consulta SQL para obtener los aprobadores según suscripción y solicitud
            sql_query = '''
                SELECT 
                    a.int_idAprobador,
                    a.str_idSuscripcion,
                    a.int_idUsuario,
                    u.str_Nombres,
                    u.str_Apellidos,
                    a.int_OrdenAprobacion,
                    a.int_EstadoAprobacion,
                    a.dt_FechaCreacion,
                    a.dt_FechaAceptacion
                FROM 
                    tr_aprobadores a
                JOIN 
                    tm_usuarios u ON a.int_idUsuario = u.int_idUsuarios
                WHERE 
                    a.str_idSuscripcion = %s
                    AND a.int_idSolicitudes = %s
                ORDER BY 
                    a.int_OrdenAprobacion ASC;
            '''

            # Parámetros de la consulta
            values = (str_idSuscripcion, int_idSolicitudes)

            # Ejecutar la consulta SQL
            with connection.cursor() as cursor:
                cursor.execute(sql_query, values)
                aprobadores = cursor.fetchall()

            # Estructurar los datos para la respuesta
            aprobadores_list = [
                {
                    'int_idAprobador': aprobador[0],
                    'str_idSuscripcion': aprobador[1],
                    'int_idUsuario': aprobador[2],
                    'str_Nombres': aprobador[3],
                    'str_Apellidos': aprobador[4],
                    'int_OrdenAprobacion': aprobador[5],
                    'int_EstadoAprobacion': aprobador[6],
                    'dt_FechaCreacion': aprobador[7].strftime('%Y-%m-%d %H:%M:%S'),
                    'dt_FechaAceptacion': aprobador[8]

                }
                for aprobador in aprobadores
            ]

            # Devolver la lista de aprobadores
            return Response(aprobadores_list, status=status.HTTP_200_OK)

        except Exception as e:
            # Manejo de errores
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
class ActualizarAprobadorView(APIView):

    def put(self, request, int_idSolicitudes, int_idUsuario, suscriptor, format=None):
        try:
            # Consultar el orden del aprobador actual
            sql_orden_query = '''
                SELECT int_OrdenAprobacion 
                FROM tr_aprobadores
                WHERE int_idSolicitudes = %s AND int_idUsuario = %s
            '''
            values_orden = (int_idSolicitudes, int_idUsuario)
            
            with connection.cursor() as cursor:
                cursor.execute(sql_orden_query, values_orden)
                result = cursor.fetchone()

            # Si el aprobador no existe
            if not result:
                return Response({'message': 'Aprobador no encontrado'}, status=status.HTTP_404_NOT_FOUND)

            # Obtener el orden del aprobador actual
            orden_aprobador = result[0]

            # Validar que los aprobadores anteriores hayan aprobado si el orden es mayor a 1
            if orden_aprobador > 1:
                sql_verificar_anterior = '''
                    SELECT int_OrdenAprobacion, int_EstadoAprobacion
                    FROM tr_aprobadores
                    WHERE int_idSolicitudes = %s AND int_OrdenAprobacion < %s
                '''
                
                # Verificar desde el primer aprobador hasta el anterior al actual
                with connection.cursor() as cursor:
                    cursor.execute(sql_verificar_anterior, (int_idSolicitudes, orden_aprobador))
                    aprobadores_anteriores = cursor.fetchall()

                    # Comprobar si algún aprobador anterior no ha aprobado
                    for aprobacion_anterior in aprobadores_anteriores:
                        if aprobacion_anterior[1] != 1:  # índice 1 es el int_EstadoAprobacion
                            return Response({
                                'message': f'El aprobador en la posición {aprobacion_anterior[0]} debe aprobar antes de continuar. Estado actual: {aprobacion_anterior[1]}'
                            }, status=status.HTTP_400_BAD_REQUEST)

            # Actualizar el estado del aprobador actual
            nuevo_estado = 1  # Suponiendo que 1 es el estado de "Aprobado"

            sql_update_query = '''
                UPDATE 
                    tr_aprobadores
                SET 
                    int_EstadoAprobacion = %s,
                    dt_FechaAceptacion = NOW()
                WHERE 
                    int_idSolicitudes = %s
                    AND int_idUsuario = %s
            '''

            update_values = (nuevo_estado, int_idSolicitudes, int_idUsuario)

            with connection.cursor() as cursor:
                cursor.execute(sql_update_query, update_values)

            # Comprobar si se actualizó algún registro
            if cursor.rowcount > 0:
                # Obtener el último orden de aprobación
                sql_max_orden_query = '''
                    SELECT MAX(int_OrdenAprobacion) 
                    FROM tr_aprobadores
                    WHERE int_idSolicitudes = %s
                '''
                with connection.cursor() as cursor:
                    cursor.execute(sql_max_orden_query, (int_idSolicitudes,))
                    max_orden_result = cursor.fetchone()

                if not max_orden_result or max_orden_result[0] is None:
                    return Response({'message': 'No se encontraron aprobadores para actualizar.'}, status=status.HTTP_404_NOT_FOUND)

                ultimo_orden = max_orden_result[0]

                # Si el aprobador actual es el último, cambiar el estado de la solicitud a "Aprobado"
                if orden_aprobador == ultimo_orden:
                    response = self.cambiar_estado_a_aprobado(int_idSolicitudes, int_idUsuario, suscriptor)

                    # Verificar la respuesta de la vista
                    if response.status_code != 200:
                        return Response({
                            'message': 'Error al intentar cambiar el estado de la solicitud a Aprobado',
                            'details': response.data
                        })

                return Response({'message': 'Aprobador actualizado correctamente'}, status=status.HTTP_200_OK)
            else:
                return Response({'message': 'No se encontró ningún registro para actualizar'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            # Manejo de errores
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def cambiar_estado_a_aprobado(self, int_idSolicitudes, int_idUsuario, suscriptor):
        # Cambiar el estado de la solicitud a "Aprobado"
        request_data = {
            'int_idSolicitudes': int_idSolicitudes,
            'nombre_estado': 'Aprobado', 
            'int_idUsuarioCreacion': int_idUsuario,
            'str_idSuscriptor': suscriptor, 
        }

        # Serializamos los datos a JSON
        json_data = JSONRenderer().render(request_data)
        
        # Creamos una petición utilizando el tipo de contenido adecuado
        factory = RequestFactory()
        request = factory.put(
            'http://localhost:8001/api/solicitudes/editar-estado/', 
            data=json_data,
            content_type='application/json'  # Definimos el tipo de contenido como JSON
        )
        
        # Obtenemos la vista y ejecutamos la solicitud
        view = EditarEstadoSolicitud.as_view()
        response = view(request)

        return response
class RechazarSolicitud(APIView):

    def put(self, request, int_idSolicitudes, int_idUsuario, format=None):
        try:
            # Consultar el orden del aprobador actual
            sql_orden_query = '''
                SELECT int_OrdenAprobacion 
                FROM tr_aprobadores
                WHERE int_idSolicitudes = %s AND int_idUsuario = %s
            '''
            values_orden = (int_idSolicitudes, int_idUsuario)
            
            with connection.cursor() as cursor:
                cursor.execute(sql_orden_query, values_orden)
                result = cursor.fetchone()

            # Si el aprobador no existe
            if not result:
                return Response({'message': 'Aprobador no encontrado'}, status=status.HTTP_404_NOT_FOUND)

            # Obtener el orden del aprobador actual
            orden_aprobador = result[0]

            # Validar que los aprobadores anteriores hayan aprobado si el orden es mayor a 1
            if orden_aprobador > 1:
                sql_verificar_anterior = '''
                    SELECT int_OrdenAprobacion, int_EstadoAprobacion
                    FROM tr_aprobadores
                    WHERE int_idSolicitudes = %s AND int_OrdenAprobacion < %s
                '''
                
                # Verificar desde el primer aprobador hasta el anterior al actual
                with connection.cursor() as cursor:
                    cursor.execute(sql_verificar_anterior, (int_idSolicitudes, orden_aprobador))
                    aprobadores_anteriores = cursor.fetchall()

                    # Comprobar si algún aprobador anterior no ha aprobado
                    for aprobacion_anterior in aprobadores_anteriores:
                        if aprobacion_anterior[1] != 1:  # índice 1 es el int_EstadoAprobacion
                            return Response({
                                'message': f'El aprobador en la posición {aprobacion_anterior[0]} debe aprobar antes de continuar. Estado actual: {aprobacion_anterior[1]}'
                            }, status=status.HTTP_400_BAD_REQUEST)

            # Actualizar el estado del aprobador actual a "rechazado" (0)
            nuevo_estado = 0  # Cambiado a 0 para representar el estado de "Rechazado"

            sql_update_query = '''
                UPDATE 
                    tr_aprobadores
                SET 
                    int_EstadoAprobacion = %s,
                    dt_FechaAceptacion = NOW()
                WHERE 
                    int_idSolicitudes = %s
                    AND int_idUsuario = %s
            '''

            update_values = (nuevo_estado, int_idSolicitudes, int_idUsuario)

            with connection.cursor() as cursor:
                cursor.execute(sql_update_query, update_values)

            # Comprobar si se actualizó algún registro
            if cursor.rowcount > 0:
                return Response({'message': 'Aprobador rechazado correctamente'}, status=status.HTTP_200_OK)
            else:
                return Response({'message': 'No se encontró ningún registro para actualizar'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            # Manejo de errores
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        
        
#REGISTROS

class BusquedaRegistros(APIView):
    def get(self, request):
        anio_fecha_registro = request.query_params.get('anio_fecha_registro')
        anio_fecha_Firma = request.query_params.get('anio_fecha_Firma')
        anio_fecha_fin = request.query_params.get('anio_fecha_fin')
        int_idGestor = request.query_params.get('int_idGestor')
        str_NombreTipoSolicitud = request.query_params.get('str_NombreTipoSolicitud')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        str_descripcion = request.query_params.getlist('str_descripcion')
        nombre_asociado = request.query_params.get('nombre_asociado')

        query = """
                   SELECT 
                s.int_idSolicitudes, 
                CONCAT(us.str_Nombres, ' ', us.str_Apellidos) AS solicitante ,
                u.str_Interlocutor,
                s.str_idSuscriptor, 
                es.str_Nombre AS estado_nombre,
                un.str_Descripcion,
                s.str_DeTerceros, 
                ts.str_Nombre AS nombre_TipoSolicitud, 
                s.dt_FechaRegistro, 
                s.dt_FechaEsperada, 
                e.str_NombreEmpresa, 
                e.str_RazonSocial, 
                e.str_Ruc,
                GROUP_CONCAT(t.str_descripcion SEPARATOR ', ') AS tags,
                s.str_CodSolicitudes,
                s.db_Honorarios,
                s.dt_FirmaContrato, 
                s.dt_FechaFin,
                CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos) AS gestor

            FROM 
                tr_solicitudes s
            LEFT JOIN
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            INNER JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
             LEFT JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
             LEFT JOIN 
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor
            LEFT JOIN 
                tm_usuarios us ON s.int_idSolicitante = us.int_idUsuarios
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor = ug.int_idUsuarios 
            LEFT JOIN 
                tr_tags t ON t.int_idSolicitudes = s.int_idSolicitudes 
            WHERE 
                s.str_idSuscriptor = %s AND es.str_Nombre = "Firmado"
            
        """
        params = [str_idSuscriptor]

        if anio_fecha_registro:
            query += " AND YEAR(s.dt_FechaRegistro) = %s"
            params.append(anio_fecha_registro)

        if anio_fecha_Firma:
            query += " AND YEAR(s.dt_FirmaContrato) = %s"
            params.append(anio_fecha_Firma)

        if anio_fecha_fin:
            query += " AND YEAR(s.dt_FechaFin) = %s"
            params.append(anio_fecha_fin)

        if int_idGestor:
            query += " AND s.int_idGestor = %s"
            params.append(int_idGestor)
            
        if nombre_asociado:
            query += " AND u.str_Interlocutor = %s"
            params.append(nombre_asociado)

        if str_NombreTipoSolicitud:
            query += " AND ts.str_Nombre = %s"
            params.append(str_NombreTipoSolicitud)

        if str_descripcion:
            query += " AND (t.str_descripcion IN %s OR t.str_descripcion IS NULL)"
            params.append(tuple(str_descripcion))

        query += " GROUP BY s.int_idSolicitudes ORDER BY  s.dt_FechaRegistro DESC;"

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            solicitudes = [
                {
                    'Id Solicitud': row[0],
                    'Solicitante': row[1],
                    'Nombre de Cliente': row[2],
                    'Suscripcion': row[3],
                    'Nombre de Estado': row[4],
                    'Unidad de Negocio': row[5],
                    'De terceros': row[6],
                    'Tipo de Solicitud': row[7],
                    'Fecha Registro': row[8],
                    'Fecha Esperada': row[9],
                    'Empresa': row[10],
                    'Razon Social': row[11],
                    'Ruc': row[12],
                    'tags': row[13],
                    'Codigo De Solicitud': row[14],
                    'Honorarios': row[15],
                    'Fecha Firma': row[16],
                    'Fecha Fin': row[17],
                    'Gestor': row[18]
                    

                }
                for row in rows
            ]
        return Response(solicitudes, status=status.HTTP_200_OK)



class SolicitudesPorFinalizar(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.str_CodSolicitudes, 
                u.str_Interlocutor,                
                sc.db_Presupuesto,
                e.str_NombreEmpresa, 
                un.str_Descripcion,
                es.str_Nombre AS estado_nombre,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                sc.str_ObjetivoContrato,
                s.dt_FirmaContrato,
                s.dt_FechaRegistro,
                CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos) AS nombre_gestor,
                sc.str_RenovacionAuto,
                sc.str_Garantia,
                s.db_Honorarios,
                sc.str_Margen,
                sc.str_FormaPago,
                sc.str_ResolucionAnticipada,
                s.int_HorasTrabajadas,
                s.int_idSolicitudes,
                s.dt_FechaFin
            FROM 
                tr_solicitudes s
           LEFT JOIN
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            INNER JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            INNER JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor  = ug.int_idUsuarios 
            LEFT JOIN
                tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
            WHERE  
                s.str_idSuscriptor = %s
                AND s.dt_FechaFin >= CURDATE()  
                AND YEAR(s.dt_FechaFin) = %s 
            ORDER BY 
                s.dt_FechaFin ASC;
        """
        params = [ str_idSuscriptor,año_filtro]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                }
                for row in rows
            ]
        return Response(solicitudes)
    
class SolicitudesVencidas(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = """
            SELECT 
                s.str_CodSolicitudes, 
                u.str_Interlocutor,                
                sc.db_Presupuesto,
                e.str_NombreEmpresa, 
                un.str_Descripcion,
                es.str_Nombre AS estado_nombre,
                ts.str_Nombre AS nombre_TipoSolicitud, 
                sc.str_ObjetivoContrato,
                s.dt_FirmaContrato,
                s.dt_FechaRegistro,
                CONCAT(ug.str_Nombres, ' ', ug.str_Apellidos) AS nombre_gestor,
                sc.str_RenovacionAuto,
                sc.str_Garantia,
                s.db_Honorarios,
                sc.str_Margen,
                sc.str_FormaPago,
                sc.str_ResolucionAnticipada,
                s.int_HorasTrabajadas,
                s.int_idSolicitudes,
                s.dt_FechaFin
            FROM 
                tr_solicitudes s
           LEFT JOIN
                tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
            INNER JOIN 
                tm_tiposolicitud ts ON s.int_idTipoSol = ts.int_idTipoSolicitud 
            INNER JOIN 
                tm_estados es ON s.int_idEstado = es.int_idEstado 
            LEFT JOIN
                tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio  
            LEFT JOIN 
                tm_interlocutores u ON s.int_idClienteAsociado = u.int_idInterlocutor
            LEFT JOIN 
                tm_usuarios ug ON s.int_idGestor  = ug.int_idUsuarios 
            LEFT JOIN
                tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes  
            WHERE  
                s.str_idSuscriptor = %s
                AND s.dt_FechaFin < CURDATE()  
                AND YEAR(s.dt_FechaFin) = %s 
            ORDER BY 
                s.dt_FechaFin ASC;
        """
        params = [ str_idSuscriptor,año_filtro]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'Cod.Solicitud': row[0],
                    'Cliente': row[1],
                    'Presupuesto': row[2],
                    'Empresa': row[3],
                    'Unidad Negocio': row[4],
                    'Estado': row[5],
                    'Tipo Contrato': row[6],
                    'Descripcion Contrato': row[7],
                    'Fecha Firma': row[8],
                    'Fecha Registro': row[9],
                    'Gestor': row[10],
                    'Renovación Automatica': row[11],
                    'Garantía': row[12],
                    'Honorarios': row[13],
                    'Margen': row[14],
                    'Forma Pago': row[15],
                    'Resolución Anticipada': row[16],
                    'Horas Trabajadas': row[17],
                    'int_idSolicitudes': row[18],
                    'Fecha Fin': row[19],
                }
                for row in rows
            ]
        return Response(solicitudes)