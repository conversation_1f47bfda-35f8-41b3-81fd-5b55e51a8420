from configparser import ConfigParser


class Response:
    def __init__(self, message="", data=None, state=False):
        self.message: str = message
        self.data = data
        self.state: bool = state

    def to_string(self):
        return f"Message: {self.message}, Data: {self.data}, State: {self.state}"

    def to_json(self):
        return {"message": self.message, "data": self.data, "state": self.state}


class Config:
    def __init__(self, header=None):
        self.header = header
        self.config_obj = None
        self.config_path = "config.ini"

    def __get_config_obj(self):
        if not self.config_obj:
            config_obj = ConfigParser()
            config_obj.read(self.config_path)
        return config_obj

    def get_object_by_data(self, data):
        config_obj = self.__get_config_obj()
        if not config_obj[self.header]:
            return Response(message="Header not found", state=False)
        if not config_obj[self.header][data]:
            return Response(message=f"Data '{data}' not found", state=False)
        return Response(
            message="Success", data=config_obj[self.header][data], state=True
        )
