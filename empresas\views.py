from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import EmpresasSerializer,EmpresaUpdateSerializer
import openai


class EmpresaList(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')

        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = "SELECT * FROM tc_empresas WHERE str_idSuscripcion = %s"
        params = [str_idSuscripcion]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            empresas = [
                {
                    'int_idEmpresa': row[0],
                    'str_idSuscripcion': row[1],
                    'str_NombreEmpresa': row[2],
                    'str_RazonSocial': row[3],
                    'str_Ruc ': row[4],
                    'dt_FechaCreacion': row[5],
                    'dt_FechaModificacion': row[6],
                    'int_idUsuarioCreacion': row[7],
                    'int_idUsuarioModificacion': row[8],
                }
                for row in rows
            ]
        return Response(empresas)
    def post(self, request):
        serializer = EmpresasSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tc_empresas 
                (str_idSuscripcion,str_NombreEmpresa,str_RazonSocial,str_Ruc, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s, %s,%s, NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['str_idSuscripcion'],
                    serializer.validated_data['str_NombreEmpresa'],
                    serializer.validated_data['str_RazonSocial'],
                    serializer.validated_data['str_Ruc'],
                    serializer.validated_data['int_idUsuarioCreacion'],
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class EmpresaDetailAPIView(APIView):
    def get_object(self, pk):
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tc_empresas WHERE int_idEmpresa = %s", [pk])
            row = cursor.fetchone()
            if row:
                return {
                    'str_idSuscripcion': row[0],
                    'str_NombreEmpresa': row[1],
                    'str_RazonSocial': row[2],
                    'str_Ruc ': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreacion': row[6],
                    'int_idUsuarioModificacion': row[7],
                }
            return None

    def get(self, request, pk):
        clausula = self.get_object(pk)
        if clausula:
            return Response(clausula)
        return Response(status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk):
        clausula = self.get_object(pk)
        if not clausula:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = EmpresaUpdateSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                UPDATE tc_empresas 
                SET  str_NombreEmpresa=%s,str_RazonSocial=%s,str_Ruc=%s, dt_FechaModificacion=NOW(), int_idUsuarioModificacion=%s 
                WHERE int_idEmpresa=%s
                """
                cursor.execute(query, [
                    serializer.validated_data['str_NombreEmpresa'],
                    serializer.validated_data['str_RazonSocial'],
                    serializer.validated_data['str_Ruc'],
                    serializer.validated_data['int_idUsuarioModificacion'],
                    pk
                ])
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tc_empresas WHERE int_idEmpresa = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class BuscarEmpresaRuc(APIView):
    def get(self, request, ruc):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')

        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tc_empresas WHERE str_Ruc = %s && str_idSuscripcion= %s", [ruc,str_idSuscripcion])
            row = cursor.fetchone()
            if row:
                data = {
                    'str_idSuscripcion': row[0],
                    'str_NombreEmpresa': row[1],
                    'str_RazonSocial': row[2],
                    'str_Ruc': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreacion': row[6],
                    'int_idUsuarioModificacion': row[7],
                }
                return Response(data, status=200)
            return Response({"detail": "Empresa no encontrada."}, status=404)
            
class BuscarMonedaEmpresa(APIView):
    def get(self, request, int_idEmpresa):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')    
        with connection.cursor() as cursor:
            cursor.execute("SELECT str_Pais,str_Moneda FROM tc_empresas WHERE int_idEmpresa = %s && str_idSuscripcion= %s ", [int_idEmpresa,str_idSuscripcion])
            row = cursor.fetchone()
            if row:
                data = {
                    'str_Pais': row[0],
                    'str_Moneda': row[1],
                   
                }
                return Response(data, status=200)
            return Response({"detail": "Empresa no encontrada."}, status=404)
            
