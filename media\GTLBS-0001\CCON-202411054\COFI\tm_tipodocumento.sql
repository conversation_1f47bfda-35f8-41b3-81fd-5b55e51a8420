-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 21-11-2024 a las 16:07:26
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `db_prisma_contratos`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipodocumento`
--

CREATE TABLE `tm_tipodocumento` (
  `int_idTipoDocumentos` int NOT NULL,
  `str_CodTipoDocumento` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_tipodocumento`
--

INSERT INTO `tm_tipodocumento` (`int_idTipoDocumentos`, `str_CodTipoDocumento`, `str_idSuscripcion`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(1, 'DOAD', 'GTLBS-0001', 'Documentos adjuntos', '2024-09-20 04:27:03', NULL, 1, NULL),
(2, 'DOPC', 'GTLBS-0001', 'Plantilla modelo de contrato', '2024-09-11 09:11:11', NULL, 1, NULL),
(3, 'COAP', 'GTLBS-0001', 'Contratos Aprobados', '2024-09-26 22:41:34', NULL, 1, NULL),
(4, 'COFI', 'GTLBS-0001', 'Contratos Firmados', '2024-09-27 19:53:53', NULL, 1, NULL),
(5, 'DOAD', 'GLTBS-0001', 'Documentos adjuntos', '2024-09-20 04:27:03', NULL, 1, NULL),
(6, 'DOPC', 'GLTBS-0001', 'Plantilla modelo de contrato', '2024-09-11 09:11:11', NULL, 1, NULL),
(7, 'COAP', 'GLTBS-0001', 'Contratos Aprobados', '2024-09-26 22:41:34', NULL, 1, NULL),
(8, 'COFI', 'GLTBS-0001', 'Contratos Firmados', '2024-09-27 19:53:53', NULL, 1, NULL);

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD PRIMARY KEY (`int_idTipoDocumentos`),
  ADD KEY `TipoDocumentos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoDocumentos_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  MODIFY `int_idTipoDocumentos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD CONSTRAINT `TipoDocumentos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoDocumentos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
