from django.urls import path
from .views import ClausulasIncluidasList, ClausulasIncluidasDetalle,ClausulasActivasList,EliminarClausulaAPIView,ClausulasActivasAPIView,ActivarClausulaPorNombre

urlpatterns = [
    path('api/clausulasIncluidas/', ClausulasIncluidasList.as_view(), name='clausulasIncluidas-list-create'),
    path('api/clausulasIncluidas/<str:suscripcion>/<str:pk>/', ClausulasIncluidasDetalle.as_view(), name='clausulasIncluidas-detail'),
    path('api/clausulasActivas/', ClausulasActivasList.as_view(), name='clausulasIncluidas-list-create'),
    path('api/clausulasActivas/Nombre/', ActivarClausulaPorNombre.as_view(), name='clausulasIncluidas-list-create'),
    path('api/clausulasActivas/<int:clausula_id>/<str:suscripcion>/<int:solicitud>', EliminarClausulaAPIView.as_view(), name='clausulasIncluidas-detail'),
    path('api/clausulasActivas/list/<str:suscripcion>/<int:solicitud>', ClausulasActivasAPIView.as_view(), name='clausulasIncluidas-detail'),

]