from django.urls import path
from .views import ConsorciosCreate,ConsorcioEliminar,ListarConsorcioporSolicitud,ConsorciosSolicitud,EliminarAsociado,ConsorcioEditar
urlpatterns = [
    path('api/consorcio/', ConsorciosCreate.as_view(), name='crear-consorcio'),
    path('api/consorcio/eliminar/', ConsorcioEliminar.as_view(), name='consorcio-detail'),
    path('api/consorcio/editar/', ConsorcioEditar.as_view(), name='consorcio-detail'),
    path('api/consorcio/solicitud/', ListarConsorcioporSolicitud.as_view(), name='listar-consorcio-porsolicitud'),
    path('api/consorcio/asignar/', ConsorciosSolicitud.as_view(), name='asignar-consorcio'),
    path('api/consorcio/asociado/eliminar/<int:pk>/', EliminarAsociado.as_view(), name='consorcio-detail'),

]