from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import InterlocutoresSerializer,InterlocutoresUpdateSerializer

class InterlocutoresList(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')

        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = "SELECT * FROM tm_interlocutores WHERE str_idSuscripcion = %s"
        params = [str_idSuscripcion]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            empresas = [
                {
                    'int_idInterlocutor': row[0],
                    'str_idSuscripcion': row[1],
                    'str_Interlocutor': row[2],
                    'str_RazonSocial': row[3],
                    'str_TipoDoc': row[4],
                    'str_RepLegal ': row[5],
                    'str_Correo': row[6],
                    'str_Documento': row[7],
                    'str_Domicilio': row[8],
                    'int_RLPartida': row[9],
                    'str_obligaciones': row[10],
                    'int_ValorAporte': row[11],
                    'int_PorcentajeAporte': row[12],
                    'str_ValorServicios': row[13],
                    'str_ValorHonorarios': row[14],
                    'dt_FechaCreacion': row[15],
                    'dt_FechaModificacion': row[16],
                    'int_idUsuarioCreacion': row[17],
                    'int_idUsuarioModificacion': row[18],
                }
                for row in rows
            ]
        return Response(empresas)
    def post(self, request):
        serializer = InterlocutoresSerializer(data=request.data)

        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tm_interlocutores 
                (str_idSuscripcion,str_Interlocutor,str_RazonSocial,str_TipoDoc,str_RepLegal,str_Correo,str_Documento,str_Domicilio,int_RLPartida,str_RLTipoDocumento,str_RLDocumento,str_obligaciones,int_ValorAporte,int_PorcentajeAporte,str_ValorServicios,str_ValorHonorarios,dt_FechaCreacion,int_idUsuarioCreacion)
                VALUES ( %s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s, NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['str_idSuscripcion'],
                    serializer.validated_data['str_Interlocutor'],
                    serializer.validated_data.get('str_RazonSocial', ''), 
                    serializer.validated_data.get('str_TipoDoc', ''),
                    serializer.validated_data.get('str_RepLegal', ''),  
                    serializer.validated_data.get('str_Correo', ''),
                    serializer.validated_data.get('str_Documento', ''), 
                    serializer.validated_data.get('str_Domicilio', ''), 
                    serializer.validated_data.get('int_RLPartida', None),
                    serializer.validated_data.get('str_RLTipoDocumento',  ''),
                    serializer.validated_data.get('str_RLDocumento',  ''),
                    serializer.validated_data.get('str_obligaciones', ''),
                    serializer.validated_data.get('int_ValorAporte', None),
                    serializer.validated_data.get('int_PorcentajeAporte', None), 
                    serializer.validated_data.get('str_ValorServicios', ''),
                    serializer.validated_data.get('str_ValorHonorarios', ''), 
                    serializer.validated_data.get('int_idUsuarioCreacion', None) 
                ])
                interlocutor_id = cursor.lastrowid
            response_data = serializer.data
            response_data['int_idInterlocutor'] = interlocutor_id
            return Response(response_data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class InterlocutoresAPIView(APIView):
    def get_object(self, pk):
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tm_interlocutores WHERE int_idInterlocutor = %s", [pk])
            row = cursor.fetchone()
            if row:
                return {
                   'int_idInterlocutor': row[0],
                    'str_idSuscripcion': row[1],
                    'str_Interlocutor': row[2],
                    'str_RazonSocial': row[3],
                    'str_TipoDoc': row[4],
                    'str_Documento': row[5],
                    'str_Domicilio': row[6],
                    'str_Correo': row[7],
                    'str_obligaciones': row[8],
                    'int_ValorAporte': row[9],
                    'int_PorcentajeAporte': row[10],
                    'str_ValorServicios': row[11],
                    'str_ValorHonorarios': row[12],
                    'str_RepLegal': row[13],
                    'int_RLPartida': row[14],
                    'str_RLTipoDocumento': row[15],
                    'str_RLDocumento': row[16],
                    'dt_FechaCreacion': row[17],
                    'dt_FechaModificacion': row[18],
                    'int_idUsuarioCreacion ': row[19],
                    'int_idUsuarioModificacion  ': row[20],
                }
            return None

    def get(self, request, pk):
        clausula = self.get_object(pk)
        if clausula:
            return Response(clausula)
        return Response(status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk):
        clausula = self.get_object(pk)
        if not clausula:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = InterlocutoresUpdateSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                update_fields = []
                params = []
                
                for field, value in serializer.validated_data.items():
                    update_fields.append(f"{field} = %s")
                    params.append(value)

                update_fields.append("dt_FechaModificacion = NOW()")

                query = f"""
                UPDATE tm_interlocutores 
                SET {', '.join(update_fields)}
                WHERE int_idInterlocutor = %s
                """
                params.append(pk)
                
                cursor.execute(query, params)
            
            return Response(serializer.data, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tm_interlocutores WHERE int_idInterlocutor = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)
    
class BuscarInterlocutoresDocumento(APIView):
    def get(self, request, documento):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')

        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT int_idInterlocutor, str_idSuscripcion, str_RazonSocial, str_TipoDoc, 
                       str_Documento, str_Domicilio, str_Correo, str_obligaciones, 
                       int_ValorAporte, int_PorcentajeAporte, str_ValorServicios, 
                       str_ValorHonorarios, str_RepLegal, int_RLPartida, 
                       str_RLTipoDocumento, str_RLDocumento, dt_FechaCreacion, 
                       dt_FechaModificacion, int_idUsuarioCreacion, int_idUsuarioModificacion, str_Interlocutor
                FROM tm_interlocutores 
                WHERE str_Documento = %s AND str_idSuscripcion = %s
            """, [documento, str_idSuscripcion])

            row = cursor.fetchone()
            if row:
                data = {
                    'int_idInterlocutor': row[0],
                    'str_idSuscripcion': row[1],
                    'str_RazonSocial': row[2],
                    'str_TipoDoc': row[3],
                    'str_Documento': row[4],
                    'str_Domicilio': row[5],
                    'str_Correo': row[6],
                    'str_obligaciones': row[7],
                    'int_ValorAporte': row[8],
                    'int_PorcentajeAporte': row[9],
                    'str_ValorServicios': row[10],
                    'str_ValorHonorarios': row[11],
                    'str_RepLegal': row[12],
                    'int_RLPartida': row[13],
                    'str_RLTipoDocumento': row[14],
                    'str_RLDocumento': row[15],
                    'dt_FechaCreacion': row[16],
                    'dt_FechaModificacion': row[17],
                    'int_idUsuarioCreacion': row[18],
                    'int_idUsuarioModificacion': row[19],
                    'str_Interlocutor': row[20],
                }
                return Response(data, status=200)

            return Response({"detail": "Persona no encontrada."}, status=404)