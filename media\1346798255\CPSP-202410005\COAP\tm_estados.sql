-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 09-10-2024 a las 22:28:49
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `prisma_desarrollo`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_estados`
--

CREATE TABLE `tm_estados` (
  `int_idEstado` int NOT NULL,
  `str_codEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD PRIMARY KEY (`int_idEstado`),
  ADD KEY `Estados_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Estados_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `Estados_suscripcion_FK` (`str_idSuscripcion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  MODIFY `int_idEstado` int NOT NULL AUTO_INCREMENT;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD CONSTRAINT `Estados_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
