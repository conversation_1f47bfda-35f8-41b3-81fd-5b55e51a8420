from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import TagsSerializer,TagsUpdateSerializer

class TagsCreate(APIView):
    def post(self, request):
        serializer = TagsSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tr_tags 
                (int_idSolicitudes ,str_descripcion, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s, NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['int_idSolicitudes'],
                    serializer.validated_data['str_descripcion'],
                    serializer.validated_data['int_idUsuarioCreacion'],
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TagsDetalle(APIView):
    def get_object(self, pk):
        with connection.cursor() as cursor:
            cursor.execute("""
            SELECT 
                int_idTags, 
                int_idSolicitudes , 
                str_descripcion,
                dt_FechaCreacion, 
                dt_FechaModificacion, 
                int_idUsuarioCreacion, 
                int_idUsuarioModificacion
            FROM 
                tr_tags
            WHERE 
                int_idTags= %s
        """, [pk])
            row = cursor.fetchone()
            if row:
                return {
                                        'int_idTags': row[0],
                    'int_idSolicitudes': row[1],
                    'str_descripcion': row[2],
                    'dt_FechaCreacion': row[3],
                    'dt_FechaModificacion': row[4],
                    'int_idUsuarioCreacion': row[5],
                    'int_idUsuarioModificacion': row[6],
                }
            return None

    def get(self, request, pk):
        clausula = self.get_object(pk)
        if clausula:
            return Response(clausula)
        return Response(status=status.HTTP_404_NOT_FOUND)

    # def put(self, request, pk):
    #     clausula = self.get_object(pk)
    #     if not clausula:
    #         return Response(status=status.HTTP_404_NOT_FOUND)

    #     serializer = TagsUpdateSerializer(data=request.data)
    #     if serializer.is_valid():
    #         with connection.cursor() as cursor:
    #             query = """
    #             UPDATE tr_tags 
    #             SET  str_descripcion, dt_FechaModificacion=NOW(), int_idUsuarioModificacion=%s 
    #             WHERE int_idTags =%s
    #             """
    #             cursor.execute(query, [
    #                 serializer.validated_data['str_descripcion'],
    #                 serializer.validated_data['int_idUsuarioModificacion'],
    #                 pk
    #             ])
    #         return Response(serializer.data)
    #     return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tr_tags WHERE int_idTags = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)
class BuscarTagsNombre(APIView):
    def get(self, request, nombre):

        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tr_tags WHERE str_descripcion = %s ", [nombre])
            row = cursor.fetchone()
            if row:
                data = {
                    'int_idTags': row[0],
                    'int_idSolicitudes': row[1],
                    'str_descripcion': row[2],
                    'dt_FechaCreacion': row[3],
                    'dt_FechaModificacion': row[4],
                    'int_idUsuarioCreacion': row[5],
                    'int_idUsuarioModificacion': row[6],
                }
                return Response(data, status=200)
            return Response({"detail": "Solicitud no encontrada."}, status=404)
class BuscarTagsSolicitud(APIView):
    def get(self, request,int_idSolicitudes):

        if not int_idSolicitudes:
            return Response(
                {"error": "El parámetro 'int_idSolicitudes' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = "SELECT * FROM tr_tags WHERE int_idSolicitudes = %s"
        params = [int_idSolicitudes]
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'int_idTags': row[0],
                    'int_idSolicitudes': row[1],
                    'str_descripcion': row[2],
                    'dt_FechaCreacion': row[3],
                    'dt_FechaModificacion': row[4],
                    'int_idUsuarioCreacion': row[5],
                    'int_idUsuarioModificacion': row[6],
                }
                for row in rows
            ]
        return Response(clausulas)
class BuscarTagsSolicitudNombre(APIView):
    def get(self, request, int_idSolicitudes,str_descripcion):

        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tr_tags WHERE int_idSolicitudes = %s && str_descripcion = %s ", [int_idSolicitudes,str_descripcion])
            row = cursor.fetchone()
            if row:
                data = {
                    'int_idTags': row[0],
                    'int_idSolicitudes': row[1],
                    'str_descripcion': row[2],
                    'dt_FechaCreacion': row[3],
                    'dt_FechaModificacion': row[4],
                    'int_idUsuarioCreacion': row[5],
                    'int_idUsuarioModificacion': row[6],
                }
                return Response(data, status=200)
            return Response({"detail": "Solicitud no encontrada."}, status=404)

class EliminarTags(APIView):
    def delete(self, request,solicitud):
        try:
            with connection.cursor() as cursor:
                query = """
                DELETE FROM tr_tags 
                WHERE int_idSolicitudes= %s
                """
                cursor.execute(query, [solicitud])
            return Response({"message": "Tag eliminada correctamente"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)