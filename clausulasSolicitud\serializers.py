from rest_framework import serializers

class ClausulasIncluidasSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField(max_length=50)
    int_idTipoSolicitud = serializers.IntegerField()
    int_idClausulaLegal = serializers.IntegerField()
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()

class ClausulasIncluidasUpdateSerializer(serializers.Serializer):
    int_idTipoSolicitud = serializers.IntegerField()
    int_idClausulaLegal = serializers.IntegerField()
    int_idUsuarioModificacion = serializers.IntegerField()

class ClausulasActivasSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField(max_length=50)
    int_idSolicitudes = serializers.IntegerField()
    int_idClausulaIncluida  = serializers.IntegerField()
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()

class ClausulasActivasUpdateSerializer(serializers.Serializer):
    int_idSolicitudes = serializers.IntegerField()
    int_idTipoSolicitud = serializers.IntegerField()
    int_idClausulaLegal = serializers.IntegerField()
    int_idUsuarioModificacion = serializers.IntegerField()
    
class ClausulasActivasNombreSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField(max_length=100)
    int_idTipoSolicitud = serializers.IntegerField()
    int_idUsuarioCreacion = serializers.IntegerField()
    str_Nombre = serializers.CharField(max_length=255) 
    int_idSolicitudes = serializers.IntegerField()

    