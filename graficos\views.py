from django.db import connection
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime

class ConteoEstadosPorGestor(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')

        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Obtener el mes y el año actuales
            fecha_actual = datetime.now()
            mes_actual = fecha_actual.month
            año_actual = fecha_actual.year

            # Ejecutar la consulta SQL dura con filtro por mes y año actuales
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT
                        e.str_Nombre AS nombre_estado,
                        COUNT(*) AS conteo
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idGestor = %s
                        AND s.str_idSuscriptor =%s
                        AND EXTRACT(MONTH FROM r.dt_FechaCambio) = %s
                        AND EXTRACT(YEAR FROM r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                    GROUP BY
                        e.str_Nombre;
                """, [int_idGestor,str_idSuscriptor, mes_actual, año_actual])

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [{"nombre_estado": nombre_estado, "conteo": conteo} for nombre_estado, conteo in resultados]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class ConteoEstadosPorGeneral(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        try:
            # Obtener el mes y el año actuales
            fecha_actual = datetime.now()
            año_actual = fecha_actual.year

            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [str_idSuscriptor, año_actual]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            # Ejecutar la consulta SQL dura con filtro por mes y año actuales
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT
                        e.str_Nombre AS nombre_estado,
                        COUNT(*) AS conteo
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.str_idSuscriptor = %s
                        AND EXTRACT(YEAR FROM r.dt_FechaCambio) = %s
                        {tipo_sol_condition}
                    GROUP BY
                        e.str_Nombre;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [{"nombre_estado": nombre_estado, "conteo": conteo} for nombre_estado, conteo in resultados]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioPorGestor(APIView):
    def get(self, request):
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idGestor, str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_dias) AS promedio_dias
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                       TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'En Aprobacion' THEN r.dt_FechaCambio END)
                        ) AS diferencia_dias
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idGestor = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'En Aprobacion')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                resultados = cursor.fetchall()
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioPorSolicitante(APIView):
    def get(self, request):
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idSolicitante:
            return Response({"error": "El parámetro 'int_idSolicitante' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idSolicitante, str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idSolicitante,
                    AVG(diferencia_dias) AS promedio_dias
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idSolicitante,
                       TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'En Aprobacion' THEN r.dt_FechaCambio END)
                        ) AS diferencia_dias
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idSolicitante = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'En Aprobacion')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idSolicitante
                ) AS subconsulta
                GROUP BY
                    int_idSolicitante;
                """, params)

                resultados = cursor.fetchall()
            response_data = [
                {"int_idSolicitante": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioPorGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_dias) AS promedio_dias
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                       TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'En Aprobacion' THEN r.dt_FechaCambio END)
                        ) AS diferencia_dias
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'En Aprobacion')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                resultados = cursor.fetchall()
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)      
class TiempoPromedioPreparacion(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idGestor, str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Aprobado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idGestor = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Aprobado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioPreparacionSolicitante(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idSolicitante:
            return Response({"error": "El parámetro 'int_idSolicitante' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idSolicitante, str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idSolicitante,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idSolicitante,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Aprobado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idSolicitante = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Aprobado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idSolicitante
                ) AS subconsulta
                GROUP BY
                    int_idSolicitante;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idSolicitante": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioPreparacionGeneral(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            # Ejecutar la consulta SQL dura
            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Aprobado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Aprobado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioTotal(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idGestor, str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idGestor = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)    
class TiempoPromedioTotalSolicitante(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idSolicitante:
            return Response({"error": "El parámetro 'int_idSolicitante' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idSolicitante, str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idSolicitante,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idSolicitante,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idSolicitante = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idSolicitante
                ) AS subconsulta
                GROUP BY
                    int_idSolicitante;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idSolicitante": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)   
class TiempoPromedioTotalGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                        AND s.int_idEmpresa = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class SolicitudesTotales(APIView):
    def get(self, request):
        # Obtener el id_gestor y str_idSuscriptor desde los parámetros de la consulta
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idGestor, año_filtro, str_idSuscriptor, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                # Cambiar la consulta para contar el número de solicitudes
                cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM tr_solicitudes
                    WHERE int_idGestor = %s
                    AND YEAR(dt_FechaCreacion) = %s
                    AND str_idSuscriptor = %s
                    AND int_idEmpresa = %s
                    {tipo_sol_condition}
                """, params)

                # Obtener el resultado del conteo
                conteo_solicitudes = cursor.fetchone()[0]

            # Preparar los datos para la respuesta JSON
            response_data = {
                "int_idGestor": int_idGestor,
                "total": conteo_solicitudes
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)    
class SolicitudesTotalesSolicitante(APIView):
    def get(self, request):
        # Obtener el id_gestor y str_idSuscriptor desde los parámetros de la consulta
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idSolicitante:
            return Response({"error": "El parámetro 'int_idSolicitante' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idSolicitante, año_filtro, str_idSuscriptor, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                # Cambiar la consulta para contar el número de solicitudes
                cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM tr_solicitudes
                    WHERE int_idSolicitante = %s
                    AND YEAR(dt_FechaCreacion) = %s
                    AND str_idSuscriptor = %s
                    AND int_idEmpresa = %s
                    {tipo_sol_condition}
                """, params)

                # Obtener el resultado del conteo
                conteo_solicitudes = cursor.fetchone()[0]

            # Preparar los datos para la respuesta JSON
            response_data = {
                "int_idSolicitante": int_idSolicitante,
                "total": conteo_solicitudes
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)    
class SolicitudesTotalesGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        año_filtro = request.query_params.get('year')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor:
            return Response({"error": "El parámetro 'str_idSuscriptor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [año_filtro, str_idSuscriptor, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                # Cambiar la consulta para contar el número de solicitudes
                cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM tr_solicitudes
                    WHERE
                    YEAR(dt_FechaCreacion) = %s
                    AND str_idSuscriptor = %s
                    AND int_idEmpresa = %s
                    {tipo_sol_condition}
                """, params)

                # Obtener el resultado del conteo
                conteo_solicitudes = cursor.fetchone()[0]

            # Preparar los datos para la respuesta JSON
            response_data = {
                "total": conteo_solicitudes
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class ContratosFirmadosPorGestor(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            count_params = [str_idSuscriptor, int_idGestor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                count_params.append(int_idTipoSol)

            # Consulta para contar solo los contratos firmados en el año especificado
            count_query = f"""
                SELECT
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_contratos_firmados
                FROM
                    tr_estadoregistros r
                INNER JOIN
                    tm_estados es ON r.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idGestor = %s
                    AND YEAR(r.dt_FechaCambio) = %s
                    AND s.int_idEmpresa = %s
                    AND es.str_Nombre = 'Firmado'
                    {tipo_sol_condition}
            """

            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                result = cursor.fetchone()

            cantidad_firmados = result[0] if result else 0

            response_data = {
                "total_contratos_firmados": cantidad_firmados
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class ContratosFirmadosPorSolicitante(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            count_params = [str_idSuscriptor, int_idSolicitante, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                count_params.append(int_idTipoSol)

            # Consulta para contar solo los contratos firmados en el año especificado
            count_query = f"""
                SELECT
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_contratos_firmados
                FROM
                    tr_estadoregistros r
                INNER JOIN
                    tm_estados es ON r.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idSolicitante = %s
                    AND YEAR(r.dt_FechaCambio) = %s
                    AND s.int_idEmpresa = %s
                    AND es.str_Nombre = 'Firmado'
                    {tipo_sol_condition}
            """

            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                result = cursor.fetchone()

            cantidad_firmados = result[0] if result else 0

            response_data = {
                "total_contratos_firmados": cantidad_firmados
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class ContratosFirmadosGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            count_params = [str_idSuscriptor, año_filtro, int_idEmpresa]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                count_params.append(int_idTipoSol)

            # Consulta para contar solo los contratos firmados en el año especificado
            count_query = f"""
                SELECT
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_contratos_firmados
                FROM
                    tr_estadoregistros r
                INNER JOIN
                    tm_estados es ON r.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                WHERE
                    s.str_idSuscriptor = %s
                    AND YEAR(r.dt_FechaCambio) = %s
                    AND s.int_idEmpresa = %s
                    AND es.str_Nombre = 'Firmado'
                    {tipo_sol_condition}
            """

            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                result = cursor.fetchone()

            cantidad_firmados = result[0] if result else 0

            response_data = {
                "total_contratos_firmados": cantidad_firmados
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class GraficaUnidadNegocio(APIView):
    def get(self, request):
        # Parámetros de entrada
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        año_filtro = request.query_params.get('year')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idGestor = %s
                    AND s.int_idEmpresa = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND es.str_Nombre <> "Firmado"
                    AND es.str_Nombre <> "Nuevo"
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    cantidad_solicitudes DESC
                LIMIT 10;
            """
            count_params = [str_idSuscriptor, int_idGestor, int_idEmpresa, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)     
class GraficaUnidadNegocioSolicitante(APIView):
    def get(self, request):
        # Parámetros de entrada
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        año_filtro = request.query_params.get('year')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idSolicitante = %s
                    AND s.int_idEmpresa = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND es.str_Nombre <> "Firmado"
                    AND es.str_Nombre <> "Nuevo"
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    cantidad_solicitudes DESC
                LIMIT 10;
            """
            count_params = [str_idSuscriptor, int_idSolicitante, int_idEmpresa, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)       
class GraficaUnidadNegocioGeneral(APIView):
    def get(self, request):
        # Parámetros de entrada
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        año_filtro = request.query_params.get('year')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idEmpresa = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND es.str_Nombre <> "Firmado"
                    AND es.str_Nombre <> "Nuevo"
                    AND s.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    cantidad_solicitudes DESC
                LIMIT 10;
            """
            count_params = [str_idSuscriptor, int_idEmpresa, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class GraficaUnidadNegocioFirmados(APIView):
    def get(self, request):
        # Parámetros de entrada
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        año_filtro = request.query_params.get('year')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idGestor = %s
                    AND s.int_idEmpresa = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND es.str_Nombre = "Firmado" 
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    cantidad_solicitudes DESC
                LIMIT 10;
            """
            count_params = [str_idSuscriptor, int_idGestor, int_idEmpresa, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)     
class GraficaUnidadNegocioSolicitanteFirmados(APIView):
    def get(self, request):
        # Parámetros de entrada
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        año_filtro = request.query_params.get('year')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idSolicitante = %s
                    AND s.int_idEmpresa = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND es.str_Nombre = "Firmado" 
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    cantidad_solicitudes DESC
                LIMIT 10;
            """
            count_params = [str_idSuscriptor, int_idSolicitante, int_idEmpresa, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)       
class GraficaUnidadNegocioGeneralFirmados(APIView):
    def get(self, request):
        # Parámetros de entrada
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        año_filtro = request.query_params.get('year')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idEmpresa = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND es.str_Nombre = "Firmado" 
                    AND s.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    cantidad_solicitudes DESC
                LIMIT 10;
            """
            count_params = [str_idSuscriptor, int_idEmpresa, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class GraficaPresupuestoUN(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idGestor = request.query_params.get('int_idGestor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes,
                    SUM(
                        CASE
                            WHEN sc.str_Moneda = 'empresa' THEN s.db_Honorarios * (
                                SELECT tc.str_valorCambio
                                FROM tm_tipoCambio tc
                                WHERE tc.int_idEmpresa = %s
                                    AND tc.dt_FechaCambio <= s.dt_FechaRegistro
                                ORDER BY tc.dt_FechaCambio DESC
                                LIMIT 1
                            )
                            ELSE s.db_Honorarios
                        END
                    ) AS total_honorarios,
                    e.str_SimboloMoneda AS str_SimboloMoneda
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes
                INNER JOIN
                    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idGestor = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND s.int_idEmpresa = %s
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    total_honorarios DESC
                LIMIT 10;

            """
            count_params = [int_idEmpresa, str_idSuscriptor, int_idGestor, año_filtro, int_idEmpresa, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                total_honorarios = row[2]
                str_SimboloMoneda = row[3]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes,
                    'total_honorarios': total_honorarios,
                    'str_SimboloMoneda': str_SimboloMoneda
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 
class GraficaPresupuestoUNSolicitante(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes,
                    SUM(
                        CASE
                            WHEN sc.str_Moneda = 'empresa' THEN s.db_Honorarios * (
                                SELECT tc.str_valorCambio
                                FROM tm_tipoCambio tc
                                WHERE tc.int_idEmpresa = %s
                                    AND tc.dt_FechaCambio <= s.dt_FechaRegistro
                                ORDER BY tc.dt_FechaCambio DESC
                                LIMIT 1
                            )
                            ELSE s.db_Honorarios
                        END
                    ) AS total_honorarios,
                    e.str_SimboloMoneda AS str_SimboloMoneda
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes
                INNER JOIN
                    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idSolicitante = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND s.int_idEmpresa = %s
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    total_honorarios DESC
                LIMIT 10;

            """
            count_params = [int_idEmpresa, str_idSuscriptor, int_idSolicitante, año_filtro, int_idEmpresa, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                total_honorarios = row[2]
                str_SimboloMoneda = row[3]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes,
                    'total_honorarios': total_honorarios,
                    'str_SimboloMoneda': str_SimboloMoneda
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 
class GraficaPresupuestoUNGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes,
                    SUM(
                        CASE
                            WHEN sc.str_Moneda = 'empresa' THEN s.db_Honorarios * (
                                SELECT tc.str_valorCambio
                                FROM tm_tipoCambio tc
                                WHERE tc.int_idEmpresa = %s
                                    AND tc.dt_FechaCambio <= s.dt_FechaRegistro
                                ORDER BY tc.dt_FechaCambio DESC
                                LIMIT 1
                            )
                            ELSE s.db_Honorarios
                        END
                    ) AS total_honorarios,
                    e.str_SimboloMoneda AS str_SimboloMoneda
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                LEFT JOIN
                    tr_solicitudcont sc ON s.int_idSolicitudes = sc.int_idSolicitudes
                INNER JOIN
                    tc_empresas e ON s.int_idEmpresa = e.int_idEmpresa
                WHERE
                    s.str_idSuscriptor = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND s.int_idEmpresa = %s
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    total_honorarios DESC
                LIMIT 10;
            """
            count_params = [int_idEmpresa, str_idSuscriptor, año_filtro, int_idEmpresa, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                total_honorarios = row[2]
                str_SimboloMoneda = row[3]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes,
                    'total_honorarios': total_honorarios,
                    'str_SimboloMoneda': str_SimboloMoneda
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class GraficaEstadoSolicitudes(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idGestor = request.query_params.get('int_idGestor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    es.str_Nombre AS estado,
                    MONTH(r.dt_FechaCambio) AS mes,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_estadoregistros r
                INNER JOIN
                    tm_estados es ON r.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idGestor = %s
                    AND YEAR(r.dt_FechaCambio) = %s
                    AND s.int_idEmpresa = %s
                    AND (es.str_Nombre = 'Firmado' OR es.str_Nombre = 'Nuevo')
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion, es.str_Nombre, MONTH(r.dt_FechaCambio)
                ORDER BY
                    MONTH(r.dt_FechaCambio)
            """
            count_params = [str_idSuscriptor, int_idGestor, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = {
                "Firmado": {
                    "cantidad": [0] * 12
                },
                "Nuevo": {
                    "cantidad": [0] * 12
                }
            }

            for row in resultados:
                estado = row[1]
                mes = row[2] - 1
                cantidad_solicitudes = row[3]
                response_data[estado]["cantidad"][mes] += cantidad_solicitudes

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)   
class GraficaEstadoSolicitudesSolicitante(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    es.str_Nombre AS estado,
                    MONTH(r.dt_FechaCambio) AS mes,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_estadoregistros r
                INNER JOIN
                    tm_estados es ON r.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idSolicitante = %s
                    AND YEAR(r.dt_FechaCambio) = %s
                    AND s.int_idEmpresa = %s
                    AND (es.str_Nombre = 'Firmado' OR es.str_Nombre = 'Nuevo')
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion, es.str_Nombre, MONTH(r.dt_FechaCambio)
                ORDER BY
                    MONTH(r.dt_FechaCambio)
            """
            count_params = [str_idSuscriptor, int_idSolicitante, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = {
                "Firmado": {
                    "cantidad": [0] * 12
                },
                "Nuevo": {
                    "cantidad": [0] * 12
                }
            }

            for row in resultados:
                estado = row[1]
                mes = row[2] - 1
                cantidad_solicitudes = row[3]
                response_data[estado]["cantidad"][mes] += cantidad_solicitudes

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)   
class GraficaEstadoSolicitudesGeneral(APIView):
     def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    es.str_Nombre AS estado,
                    MONTH(r.dt_FechaCambio) AS mes,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes
                FROM
                    tr_estadoregistros r
                INNER JOIN
                    tm_estados es ON r.int_idEstado = es.int_idEstado
                INNER JOIN
                    tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                WHERE
                    s.str_idSuscriptor = %s
                    AND YEAR(r.dt_FechaCambio) = %s
                    AND s.int_idEmpresa = %s
                    AND (es.str_Nombre = 'Firmado' OR es.str_Nombre = 'Nuevo')
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion, es.str_Nombre, MONTH(r.dt_FechaCambio)
                ORDER BY
                    MONTH(r.dt_FechaCambio)
            """
            count_params = [str_idSuscriptor, año_filtro, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = {
                "Firmado": {
                    "cantidad": [0] * 12
                },
                "Nuevo": {
                    "cantidad": [0] * 12
                }
            }

            for row in resultados:
                estado = row[1]
                mes = row[2] - 1
                cantidad_solicitudes = row[3]
                response_data[estado]["cantidad"][mes] += cantidad_solicitudes

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class GraficaHorasTrabajadas(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idGestor = request.query_params.get('int_idGestor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes,
                    SUM(s.int_HorasTrabajadas) AS total_horas_trabajadas
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idGestor = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND s.int_idEmpresa = %s
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    total_horas_trabajadas DESC
                LIMIT 10
                ;
            """
            count_params = [str_idSuscriptor, int_idGestor, año_filtro, int_idEmpresa, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                total_horas_trabajadas = row[2]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes,
                    'total_horas_trabajadas': total_horas_trabajadas
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)  
class GraficaHorasTrabajadasSolicitante(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idSolicitante = request.query_params.get('int_idSolicitante')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes,
                    SUM(s.int_HorasTrabajadas) AS total_horas_trabajadas
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND s.int_idSolicitante = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND s.int_idEmpresa = %s
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    total_horas_trabajadas DESC
                LIMIT 10
                ;
            """
            count_params = [str_idSuscriptor, int_idSolicitante, año_filtro, int_idEmpresa, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                total_horas_trabajadas = row[2]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes,
                    'total_horas_trabajadas': total_horas_trabajadas
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)      
class GraficaHorasTrabajadasGeneral(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        pais = request.query_params.get('pais')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not str_idSuscriptor or not año_filtro:
            return Response(
                {"error": "Los parámetros 'str_idSuscriptor' y 'year' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            pais_condition = ""
            pais_param = []
            tipo_sol_condition = ""

            if pais:
                pais_condition = " AND e.str_Pais = %s"
                pais_param = [pais]

            if int_idTipoSol:
                tipo_sol_condition = " AND s.int_idTipoSol = %s"

            count_query = f"""
                SELECT
                    un.str_Descripcion AS unidad_negocio,
                    COUNT(DISTINCT s.int_idSolicitudes) AS cantidad_solicitudes,
                    SUM(s.int_HorasTrabajadas) AS total_horas_trabajadas
                FROM
                    tr_solicitudes s
                INNER JOIN
                    tm_unidadesnegocios un ON s.int_idUnidadNegocio = un.int_idUnidadesNegocio
                INNER JOIN
                    tm_estados es ON s.int_idEstado = es.int_idEstado
                WHERE
                    s.str_idSuscriptor = %s
                    AND YEAR(s.dt_FechaRegistro) = %s
                    AND s.int_idEmpresa = %s
                    AND un.int_idEmpresa = %s
                    {pais_condition}
                    {tipo_sol_condition}
                GROUP BY
                    un.str_Descripcion
                ORDER BY
                    total_horas_trabajadas DESC
                LIMIT 10
                ;
            """
            count_params = [str_idSuscriptor, año_filtro, int_idEmpresa, int_idEmpresa] + pais_param
            if int_idTipoSol:
                count_params.append(int_idTipoSol)
            with connection.cursor() as cursor:
                cursor.execute(count_query, count_params)
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON (solo datos para gráficas)
            response_data = []
            for row in resultados:
                unidad_negocio = row[0]
                cantidad_solicitudes = row[1]
                total_horas_trabajadas = row[2]
                response_data.append({
                    'unidad_negocio': unidad_negocio,
                    'cantidad_solicitudes': cantidad_solicitudes,
                    'total_horas_trabajadas': total_horas_trabajadas
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)          
class TiempoPromedioPorGestorTotal(APIView):
    def get(self, request):
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idGestor, str_idSuscriptor, año_filtro]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_dias) AS promedio_dias
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                       TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'En Proceso' THEN r.dt_FechaCambio END)
                        ) AS diferencia_dias
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idGestor = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'En Proceso')
                        AND YEAR(r.dt_FechaCambio) = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                resultados = cursor.fetchall()
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioPorGeneralTotal(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [str_idSuscriptor, año_filtro]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_dias) AS promedio_dias
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                       TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'En Proceso' THEN r.dt_FechaCambio END)
                        ) AS diferencia_dias
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('Asignado', 'En Proceso')
                        AND YEAR(r.dt_FechaCambio) = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                resultados = cursor.fetchall()
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)      
class TiempoPromedioPreparacionTotal(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        int_idTipoSol = request.query_params.get('int_idTipoSol')

        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Construir condición adicional para int_idTipoSol
            tipo_sol_condition = ""
            params = [int_idGestor, str_idSuscriptor, año_filtro]

            if int_idTipoSol:
                tipo_sol_condition = "AND s.int_idTipoSol = %s"
                params.append(int_idTipoSol)

            with connection.cursor() as cursor:
                cursor.execute(f"""
                SELECT
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'En Proceso' THEN r.dt_FechaCambio END),
                            MIN(CASE WHEN e.str_Nombre = 'En Validacion' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM
                        tr_estadoregistros r
                    JOIN
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE
                        s.int_idGestor = %s
                        AND s.str_idSuscriptor = %s
                        AND e.str_Nombre IN ('En Proceso', 'En Validacion')
                        AND YEAR(r.dt_FechaCambio) = %s
                        {tipo_sol_condition}
                    GROUP BY
                        r.int_idSolicitudes, s.int_idGestor
                ) AS subconsulta
                GROUP BY
                    int_idGestor;
                """, params)

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioPreparacionGeneralTotal(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        try:
            
            # Ejecutar la consulta SQL dura
            with connection.cursor() as cursor:
                cursor.execute("""
                SELECT 
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT 
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'En Proceso' THEN r.dt_FechaCambio END), 
                            MIN(CASE WHEN e.str_Nombre = 'En Validacion' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM 
                        tr_estadoregistros r
                    JOIN 
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN 
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE 
                        s.str_idSuscriptor =%s
                        AND e.str_Nombre IN ('En Proceso', 'En Validacion')
                        AND YEAR(r.dt_FechaCambio) = %s
                    GROUP BY 
                        r.int_idSolicitudes, s.int_idGestor 
                ) AS subconsulta
                GROUP BY 
                    int_idGestor;
                """, [str_idSuscriptor, año_filtro])

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class TiempoPromedioTotalTotal(APIView):
    def get(self, request):
        # Obtener el id_gestor desde los parámetros de la consulta
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
           
            with connection.cursor() as cursor:
                cursor.execute("""
                SELECT 
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT 
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END), 
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM 
                        tr_estadoregistros r
                    JOIN 
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN 
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE 
                        s.int_idGestor = %s 
                        AND s.str_idSuscriptor= %s
                        AND e.str_Nombre IN ('Asignado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                    GROUP BY 
                        r.int_idSolicitudes, s.int_idGestor 
                ) AS subconsulta
                GROUP BY 
                    int_idGestor;
                """, [int_idGestor,str_idSuscriptor , año_filtro])

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)    
class TiempoPromedioTotalGeneralTotal(APIView):
    def get(self, request):

        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        try:
           
            with connection.cursor() as cursor:
                cursor.execute("""
                SELECT 
                    int_idGestor,
                    AVG(diferencia_horas) AS promedio_horas
                FROM (
                    SELECT 
                        r.int_idSolicitudes,
                        s.int_idGestor,
                        TIMESTAMPDIFF(
                            HOUR,
                            MIN(CASE WHEN e.str_Nombre = 'Asignado' THEN r.dt_FechaCambio END), 
                            MIN(CASE WHEN e.str_Nombre = 'Firmado' THEN r.dt_FechaCambio END)
                        ) AS diferencia_horas
                    FROM 
                        tr_estadoregistros r
                    JOIN 
                        tm_estados e ON r.int_idEstado = e.int_idEstado
                    JOIN 
                        tr_solicitudes s ON r.int_idSolicitudes = s.int_idSolicitudes
                    WHERE 
                        s.str_idSuscriptor= %s
                        AND e.str_Nombre IN ('Asignado', 'Firmado')
                        AND YEAR(r.dt_FechaCambio) = %s
                    GROUP BY 
                        r.int_idSolicitudes, s.int_idGestor 
                ) AS subconsulta
                GROUP BY 
                    int_idGestor;
                """, [str_idSuscriptor ,  año_filtro])

                # Obtener todos los resultados de la consulta
                resultados = cursor.fetchall()

            # Preparar los datos para la respuesta JSON
            response_data = [
                {"int_idGestor": row[0], "total": row[1] if row[1] is not None else 0}
                for row in resultados
            ]

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class SolicitudesTotalesTotal(APIView):
    def get(self, request):
        # Obtener el id_gestor y str_idSuscriptor desde los parámetros de la consulta
        int_idGestor = request.query_params.get('int_idGestor')
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        if not int_idGestor:
            return Response({"error": "El parámetro 'int_idGestor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
           
            with connection.cursor() as cursor:
                # Cambiar la consulta para contar el número de solicitudes
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM tr_solicitudes 
                    WHERE int_idGestor = %s 
                    AND YEAR(dt_FechaRegistro) = %s 
                    AND str_idSuscriptor = %s
                """, [int_idGestor, año_filtro , str_idSuscriptor])

                # Obtener el resultado del conteo
                conteo_solicitudes = cursor.fetchone()[0]

            # Preparar los datos para la respuesta JSON
            response_data = {
                "int_idGestor": int_idGestor,
                "total": conteo_solicitudes
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class SolicitudesTotalesGeneralTotal(APIView):
    def get(self, request):

        str_idSuscriptor = request.query_params.get('str_idSuscriptor')
        año_filtro = request.query_params.get('year')
        if not str_idSuscriptor:
            return Response({"error": "El parámetro 'str_idSuscriptor' es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            
            
            with connection.cursor() as cursor:
                # Cambiar la consulta para contar el número de solicitudes
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM tr_solicitudes 
                    WHERE
                    YEAR(dt_FechaRegistro) = %s 
                    AND str_idSuscriptor = %s
                """, [ año_filtro, str_idSuscriptor])

                # Obtener el resultado del conteo
                conteo_solicitudes = cursor.fetchone()[0]

            # Preparar los datos para la respuesta JSON
            response_data = {
                "total": conteo_solicitudes
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)