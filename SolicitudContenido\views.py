from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import SolicitudContSerializer, SolicitudContUpdateSerializer

class ContenidoSolicitudes(APIView):
    def get(self, request):
        str_idSuscriptor = request.query_params.get('str_idSuscriptor')

        if not str_idSuscriptor:
            return Response(
                {"error": "El parámetro 'str_idSuscriptor' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = "SELECT * FROM tr_solicitudcont WHERE str_idSuscriptor = %s"
        params = [str_idSuscriptor]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            solicitudes = [
                {
                    'int_idSolicitudCont':           row[0],
                    'str_idSuscriptor':              row[1],
                    'int_idSolicitudes':             row[2],
                    'int_idInterlocutor':            row[3],
                    'int_idInterlocutorComprador':   row[4],
                    'int_NumAsociados':              row[5],
                    'str_Moneda':                    row[6],
                    'db_Presupuesto':                row[7],
                    'str_Margen':                    row[8],
                    'str_PlazoSolicitud':            row[9],
                    'str_TipoServicio':              row[10],
                    'str_InfoAdicional':             row[11],
                    'str_DocAdjuntos':               row[12],
                    'str_CondicionPago':             row[13],
                    'str_ConsultorAsignado':         row[14],
                    'str_RenovacionAuto':            row[15],
                    'str_DetalleRenovAuto':          row[16],
                    'str_AjusteHonorarios':          row[17],
                    'str_DetalleAjusteHonorarios':   row[18],
                    'str_Garantia':                  row[19],
                    'str_DetalleGarantia':           row[20],
                    'str_ResolucionAnticipada':      row[21],
                    'str_DetalleResolucionAnticipada': row[22],
                    'str_Penalidades':               row[23],
                    'str_DetallePenalidades':        row[24],
                    'str_BienMuebleInmueble':        row[25],
                    'str_BienPartidaCertificada':    row[26],
                    'str_BienDireccion':             row[27],
                    'str_BienUso':                   row[28],
                    'str_BienDescripcion':           row[29],
                    'str_ObjetivoContrato':          row[30],
                    'str_MonedaContrato':            row[31],
                    'str_RentaPactada':              row[32],
                    'str_ImporteVenta':              row[33],
                    'str_FormaPago':                 row[34],
                    'str_PlazoArriendo':             row[35],
                    'dt_FechaArriendo':              row[36],
                    'dt_FechaVenta':                 row[37],
                    'str_InteresRetraso':            row[38],
                    'str_detalleInteresRetraso':     row[39],
                    'str_ObligacionesConjuntas':     row[40],
                    'str_GarantiaVenta':             row[41],
                    'str_DetalleGarantiaVenta':      row[42],
                    'str_InfoCompartida':            row[43],
                    'str_ReajusteRenta':             row[44],
                    'str_DetalleReajusteRenta':      row[45],
                    'dt_FechaRenAut':      row[46],
                    'str_plazoForzoso': row[47],
                    'db_inversion': row[48],
                    'str_servicioIniciado': row[49],
                    'str_ans': row[50]
                }
                for row in rows
            ]
        return Response(solicitudes)

class ContenidoIngresar(APIView):
    def post(self, request):
        # Solo requerimos str_idSuscriptor e int_idSolicitudes
        str_idSuscriptor = request.data.get('str_idSuscriptor')
        int_idSolicitudes = request.data.get('int_idSolicitudes')

        if not str_idSuscriptor or not int_idSolicitudes:
            return Response(
                {"error": "Los campos 'str_idSuscriptor' e 'int_idSolicitudes' son obligatorios."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Aquí puedes manejar el resto de los campos como opcionales
        data = {
            'str_idSuscriptor': str_idSuscriptor,
            'int_idSolicitudes': int_idSolicitudes,
            'int_idInterlocutor': request.data.get('int_idInterlocutor'),
            'int_idInterlocutorComprador': request.data.get('int_idInterlocutorComprador'),
            'int_NumAsociados': request.data.get('int_NumAsociados'),
            'int_Honorarios': request.data.get('int_Honorarios'),
            'str_Moneda': request.data.get('str_Moneda'),
            'db_Presupuesto': request.data.get('db_Presupuesto'),
            'str_Margen': request.data.get('str_Margen'),
            'str_PlazoSolicitud': request.data.get('str_PlazoSolicitud'),
            'str_TipoServicio': request.data.get('str_TipoServicio'),
            'str_InfoAdicional': request.data.get('str_InfoAdicional'),
            'str_DocAdjuntos': request.data.get('str_DocAdjuntos'),
            'str_CondicionPago': request.data.get('str_CondicionPago'),
            'str_ConsultorAsignado': request.data.get('str_ConsultorAsignado'),
            'str_RenovacionAuto': request.data.get('str_RenovacionAuto'),
            'str_DetalleRenovAuto': request.data.get('str_DetalleRenovAuto'),
            'str_AjusteHonorarios': request.data.get('str_AjusteHonorarios'),
            'str_DetalleAjusteHonorarios': request.data.get('str_DetalleAjusteHonorarios'),
            'str_Garantia': request.data.get('str_Garantia'),
            'str_DetalleGarantia': request.data.get('str_DetalleGarantia'),
            'str_ResolucionAnticipada': request.data.get('str_ResolucionAnticipada'),
            'str_DetalleResolucionAnticipada': request.data.get('str_DetalleResolucionAnticipada'),
            'str_Penalidades': request.data.get('str_Penalidades'),
            'str_DetallePenalidades': request.data.get('str_DetallePenalidades'),
            'str_BienMuebleInmueble': request.data.get('str_BienMuebleInmueble'),
            'str_BienPartidaCertificada': request.data.get('str_BienPartidaCertificada'),
            'str_BienDireccion': request.data.get('str_BienDireccion'),
            'str_BienUso': request.data.get('str_BienUso'),
            'str_BienDescripcion': request.data.get('str_BienDescripcion'),
            'str_ObjetivoContrato': request.data.get('str_ObjetivoContrato'),
            'str_MonedaContrato': request.data.get('str_MonedaContrato'),
            'str_RentaPactada': request.data.get('str_RentaPactada'),
            'str_ImporteVenta': request.data.get('str_ImporteVenta'),
            'str_FormaPago': request.data.get('str_FormaPago'),
            'str_PlazoArriendo': request.data.get('str_PlazoArriendo'),
            'dt_FechaArriendo': request.data.get('dt_FechaArriendo'),
            'dt_FechaVenta': request.data.get('dt_FechaVenta'),
            'str_InteresRetraso': request.data.get('str_InteresRetraso'),
            'str_detalleInteresRetraso': request.data.get('str_detalleInteresRetraso'),
            'str_ObligacionesConjuntas': request.data.get('str_ObligacionesConjuntas'),
            'str_GarantiaVenta': request.data.get('str_GarantiaVenta'),
            'str_DetalleGarantiaVenta': request.data.get('str_DetalleGarantiaVenta'),
            'str_InfoCompartida': request.data.get('str_InfoCompartida'),
            'str_ReajusteRenta': request.data.get('str_ReajusteRenta'),
            'str_DetalleReajusteRenta': request.data.get('str_DetalleReajusteRenta'),
            'dt_FechaRenAut': request.data.get('dt_FechaRenAut'),
            'str_plazoForzoso': request.data.get('str_plazoForzoso'),
            'db_inversion': request.data.get('db_inversion'),
            'str_servicioIniciado': request.data.get('str_servicioIniciado'),
            'str_ans': request.data.get('str_ans'),
        }

        with connection.cursor() as cursor:
            query = """
            INSERT INTO tr_solicitudcont 
            (str_idSuscriptor, int_idSolicitudes, int_idInterlocutor, int_idInterlocutorComprador, int_NumAsociados, 
              str_Moneda, db_Presupuesto, str_Margen, str_PlazoSolicitud, str_TipoServicio, 
             str_InfoAdicional, str_DocAdjuntos, str_CondicionPago ,str_ConsultorAsignado, str_RenovacionAuto, str_DetalleRenovAuto, 
             str_AjusteHonorarios, str_DetalleAjusteHonorarios, str_Garantia, str_DetalleGarantia, 
             str_ResolucionAnticipada, str_DetalleResolucionAnticipada, str_Penalidades, str_DetallePenalidades, 
             str_BienMuebleInmueble, str_BienPartidaCertificada, str_BienDireccion, str_BienUso, 
             str_BienDescripcion, str_ObjetivoContrato, str_MonedaContrato, str_RentaPactada, str_ImporteVenta, 
             str_FormaPago, str_PlazoArriendo, dt_FechaArriendo, dt_FechaVenta, str_InteresRetraso, str_detalleInteresRetraso,
             str_ObligacionesConjuntas, str_GarantiaVenta, str_DetalleGarantiaVenta, str_InfoCompartida,str_ReajusteRenta,str_DetalleReajusteRenta,dt_FechaRenAut,str_plazoForzoso,db_inversion,str_servicioIniciado,str_ans)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s, 
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(query, [
                data['str_idSuscriptor'],
                data['int_idSolicitudes'],
                data.get('int_idInterlocutor'),
                data.get('int_idInterlocutorComprador'),
                data.get('int_NumAsociados'),
                data.get('str_Moneda'),
                data.get('db_Presupuesto'),
                data.get('str_Margen'),
                data.get('str_PlazoSolicitud'),
                data.get('str_TipoServicio'),
                data.get('str_InfoAdicional'),
                data.get('str_DocAdjuntos'),
                data.get('str_CondicionPago'),
                data.get('str_ConsultorAsignado'),
                data.get('str_RenovacionAuto'),
                data.get('str_DetalleRenovAuto'),
                data.get('str_AjusteHonorarios'),
                data.get('str_DetalleAjusteHonorarios'),
                data.get('str_Garantia'),
                data.get('str_DetalleGarantia'),
                data.get('str_ResolucionAnticipada'),
                data.get('str_DetalleResolucionAnticipada'),
                data.get('str_Penalidades'),
                data.get('str_DetallePenalidades'),
                data.get('str_BienMuebleInmueble'),
                data.get('str_BienPartidaCertificada'),
                data.get('str_BienDireccion'),
                data.get('str_BienUso'),
                data.get('str_BienDescripcion'),
                data.get('str_ObjetivoContrato'),
                data.get('str_MonedaContrato'),
                data.get('str_RentaPactada'),
                data.get('str_ImporteVenta'),
                data.get('str_FormaPago'),
                data.get('str_PlazoArriendo'),
                data.get('dt_FechaArriendo'),
                data.get('dt_FechaVenta'),
                data.get('str_InteresRetraso'),
                data.get('str_detalleInteresRetraso'),
                data.get('str_ObligacionesConjuntas'),
                data.get('str_GarantiaVenta'),
                data.get('str_DetalleGarantiaVenta'),
                data.get('str_InfoCompartida'),
                data.get('str_ReajusteRenta'),
                data.get('str_DetalleReajusteRenta'),
                data.get('dt_FechaRenAut'),
                data.get('str_plazoForzoso'),
                data.get('db_inversion'),
                data.get('str_servicioIniciado'),
                data.get('str_ans'),
            ])

            # Obtener el ID del registro recién creado
            solicitudcont_id = cursor.lastrowid  # Asegúrate de que esto funcione con tu base de datos

        # Incluir el ID en la respuesta
        return Response({'id': solicitudcont_id, **data}, status=status.HTTP_201_CREATED)
class SolicitudesContAPIView(APIView):
    def get_object(self, pk):
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tr_solicitudcont WHERE int_idSolicitudes = %s", [pk])
            row = cursor.fetchone()
            if row:
                return {
                'int_idSolicitudCont':           row[0],
                'str_idSuscriptor':              row[1],
                'int_idSolicitudes':             row[2],
                'int_idInterlocutor':            row[3],
                'int_idInterlocutorComprador':   row[4],
                'int_NumAsociados':              row[5],
                'str_Moneda':                    row[6],
                'db_Presupuesto':                row[7],
                'str_Margen':                    row[8],
                'str_PlazoSolicitud':            row[9],
                'str_TipoServicio':              row[10],
                'str_InfoAdicional':             row[11],
                'str_DocAdjuntos':               row[12],
                'str_CondicionPago':             row[13],
                'str_ConsultorAsignado':         row[14],
                'str_RenovacionAuto':            row[15],
                'str_DetalleRenovAuto':          row[16],
                'str_AjusteHonorarios':          row[17],
                'str_DetalleAjusteHonorarios':   row[18],
                'str_Garantia':                  row[19],
                'str_DetalleGarantia':           row[20],
                'str_ResolucionAnticipada':      row[21],
                'str_DetalleResolucionAnticipada': row[22],
                'str_Penalidades':               row[23],
                'str_DetallePenalidades':        row[24],
                'str_BienMuebleInmueble':        row[25],
                'str_BienPartidaCertificada':    row[26],
                'str_BienDireccion':             row[27],
                'str_BienUso':                   row[28],
                'str_BienDescripcion':           row[29],
                'str_ObjetivoContrato':          row[30],
                'str_MonedaContrato':            row[31],
                'str_RentaPactada':              row[32],
                'str_ImporteVenta':              row[33],
                'str_FormaPago':                 row[34],
                'str_PlazoArriendo':             row[35],
                'dt_FechaArriendo':              row[36],
                'dt_FechaVenta':                 row[37],
                'str_InteresRetraso':            row[38],
                'str_detalleInteresRetraso':     row[39],
                'str_ObligacionesConjuntas':     row[40],
                'str_GarantiaVenta':             row[41],
                'str_DetalleGarantiaVenta':      row[42],
                'str_InfoCompartida':            row[43],
                'str_ReajusteRenta':             row[44],
                'str_DetalleReajusteRenta':      row[45],
                'dt_FechaRenAut':                row[46],
                'str_plazoForzoso':                row[47],
                'db_inversion':                row[48],
                'str_servicioIniciado':                row[49],
                'str_ans': row[50]
                }
        return None

    def get(self, request, pk):
        solicitud = self.get_object(pk)
        if solicitud:
            return Response(solicitud)
        return Response(
            {"error": "Solicitud no encontrada."},
            status=status.HTTP_404_NOT_FOUND
        )

    def put(self, request, pk):
        data = request.data
        solicitud = self.get_object(pk)
        
        if solicitud is None:
            return Response({'error': 'Solicitud no encontrada'}, status=status.HTTP_404_NOT_FOUND)

        with connection.cursor() as cursor:
            cursor.execute("""
                UPDATE tr_solicitudcont 
                SET int_idInterlocutor = %s,
                    int_idInterlocutorComprador = %s,
                    int_NumAsociados = %s,
                    str_Moneda = %s,
                    db_Presupuesto = %s,
                    str_Margen = %s,
                    str_PlazoSolicitud = %s,
                    str_TipoServicio = %s,
                    str_InfoAdicional = %s,
                    str_DocAdjuntos = %s,
                    str_CondicionPago=%s,
                    str_ConsultorAsignado = %s,
                    str_RenovacionAuto = %s,
                    str_DetalleRenovAuto = %s,
                    str_AjusteHonorarios = %s,
                    str_DetalleAjusteHonorarios = %s,
                    str_Garantia = %s,
                    str_DetalleGarantia = %s,
                    str_ResolucionAnticipada = %s,
                    str_DetalleResolucionAnticipada = %s,
                    str_Penalidades = %s,
                    str_DetallePenalidades = %s,
                    str_BienMuebleInmueble = %s,
                    str_BienPartidaCertificada = %s,
                    str_BienDireccion = %s,
                    str_BienUso = %s,
                    str_BienDescripcion = %s,
                    str_ObjetivoContrato = %s,
                    str_MonedaContrato = %s,
                    str_RentaPactada = %s,
                    str_ImporteVenta = %s,
                    str_FormaPago = %s,
                    str_PlazoArriendo = %s,
                    dt_FechaArriendo = %s,
                    dt_FechaVenta = %s,
                    str_InteresRetraso = %s,
                    str_detalleInteresRetraso= %s,
                    str_ObligacionesConjuntas = %s,
                    str_GarantiaVenta= %s,
                    str_DetalleGarantiaVenta = %s,
                    str_InfoCompartida = %s , 
                    str_ReajusteRenta = %s,
                    str_DetalleReajusteRenta = %s,
                    dt_FechaRenAut = %s,
                    str_plazoForzoso = %s,
                    db_inversion = %s,
                    str_servicioIniciado = %s,
                    str_ans = %s
                WHERE int_idSolicitudes = %s
            """, [
                data.get('int_idInterlocutor'),
                data.get('int_idInterlocutorComprador'),
                data.get('int_NumAsociados'),
                data.get('str_Moneda'),
                data.get('db_Presupuesto'),
                data.get('str_Margen'),
                data.get('str_PlazoSolicitud'),
                data.get('str_TipoServicio'),
                data.get('str_InfoAdicional'),
                data.get('str_DocAdjuntos'),
                data.get('str_CondicionPago'),
                data.get('str_ConsultorAsignado'),
                data.get('str_RenovacionAuto'),
                data.get('str_DetalleRenovAuto'),
                data.get('str_AjusteHonorarios'),
                data.get('str_DetalleAjusteHonorarios'),
                data.get('str_Garantia'),
                data.get('str_DetalleGarantia'),
                data.get('str_ResolucionAnticipada'),
                data.get('str_DetalleResolucionAnticipada'),
                data.get('str_Penalidades'),
                data.get('str_DetallePenalidades'),
                data.get('str_BienMuebleInmueble'),
                data.get('str_BienPartidaCertificada'),
                data.get('str_BienDireccion'),
                data.get('str_BienUso'),
                data.get('str_BienDescripcion'),
                data.get('str_ObjetivoContrato'),
                data.get('str_MonedaContrato'),
                data.get('str_RentaPactada'),
                data.get('str_ImporteVenta'),
                data.get('str_FormaPago'),
                data.get('str_PlazoArriendo'),
                data.get('dt_FechaArriendo'),
                data.get('dt_FechaVenta'),
                data.get('str_InteresRetraso'),
                data.get('str_detalleInteresRetraso'),
                data.get('str_ObligacionesConjuntas'),
                data.get('str_GarantiaVenta'),
                data.get('str_DetalleGarantiaVenta'),
                data.get('str_InfoCompartida'),
                data.get('str_ReajusteRenta'),
                data.get('str_detalleInteresRetraso'),
                data.get('dt_FechaRenAut'),
                data.get('str_plazoForzoso'),
                data.get('db_inversion'),
                data.get('str_servicioIniciado'),
                data.get('str_ans'),
                pk
            ])
        
        return Response({'message': 'Solicitud actualizada correctamente'}, status=status.HTTP_200_OK)

    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tr_solicitudcont WHERE int_idSolicitudes = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)