from rest_framework import serializers

class InterlocutoresSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.Char<PERSON><PERSON>(max_length=50)
    str_Interlocutor = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_RazonSocial = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_TipoDoc = serializers.CharField(max_length=50, required=False, allow_blank=True)
    str_RepLegal = serializers.Char<PERSON>ield(max_length=255, required=False, allow_blank=True)
    str_Correo = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_Documento = serializers.CharField(max_length=20, required=False, allow_blank=True)
    str_Domicilio = serializers.CharField(max_length=255, required=False, allow_blank=True)
    int_RLPartida = serializers.Char<PERSON><PERSON>(max_length=50,required=False, allow_blank=True)
    str_RLTipoDocumento = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_RLDocumento = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_obligaciones = serializers.CharField(max_length=255, required=False, allow_blank=True)
    int_ValorAporte = serializers.IntegerField(required=False, allow_null=True)
    int_PorcentajeAporte = serializers.FloatField(required=False, allow_null=True) 
    str_ValorServicios = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_ValorHonorarios = serializers.CharField(max_length=255, required=False, allow_blank=True)
    int_idUsuarioCreacion = serializers.IntegerField(required=False, allow_null=True)

class InterlocutoresUpdateSerializer(serializers.Serializer):
    str_Interlocutor = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_RazonSocial = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_TipoDoc = serializers.CharField(max_length=50, required=False, allow_blank=True)
    str_RepLegal = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_Correo = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_Documento = serializers.CharField(max_length=20, required=False, allow_blank=True)
    str_Domicilio = serializers.CharField(max_length=255, required=False, allow_blank=True)
    int_RLPartida = serializers.CharField(max_length=50,required=False, allow_blank=True ,allow_null=True)
    str_obligaciones = serializers.CharField(max_length=255, required=False, allow_blank=True)
    int_ValorAporte = serializers.IntegerField(required=False, allow_null=True)
    int_PorcentajeAporte = serializers.FloatField(required=False, allow_null=True)
    str_ValorServicios = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_ValorHonorarios = serializers.CharField(max_length=255, required=False, allow_blank=True)
    int_idUsuarioModificacion = serializers.IntegerField(required=False, allow_null=True)
    str_RLTipoDocumento = serializers.CharField(max_length=255, required=False, allow_blank=True)
    str_RLDocumento = serializers.CharField(max_length=255, required=False, allow_blank=True)