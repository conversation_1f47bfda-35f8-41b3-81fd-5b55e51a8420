from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import TipDocumentoSerializer,TipDocumentoUpdateSerializer

class TipDocumentoListCreateAPIView(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')

        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = "SELECT * FROM tm_tipodocumento WHERE str_idSuscripcion = %s"
        params = [str_idSuscripcion]
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'int_idTipoDocumentos': row[0],
                    'str_CodTipoDocumento': row[1],
                    'str_idSuscripcion': row[2],
                    'int_Nombre': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreacion': row[6],
                    'int_idUsuarioModificacion': row[7],
                }
                for row in rows
            ]
        return Response(clausulas)
    def post(self, request):
        serializer = TipDocumentoSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tm_tipodocumento 
                (str_CodTipoDocumento, str_idSuscripcion, str_Nombre, dt_FechaCreacion, int_idUsuarioCreacion)
                VALUES (%s, %s, %s, NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['str_CodTipoDocumento'],
                    serializer.validated_data['str_idSuscripcion'],
                    serializer.validated_data['str_Nombre'],
                    serializer.validated_data['int_idUsuarioCreacion'],
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TipDocumentoDetailAPIView(APIView):
    def get_object(self, pk):
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tm_tipodocumento WHERE int_idTipoDocumentos  = %s", [pk])
            row = cursor.fetchone()
            if row:
                return {
                    'int_idTipoDocumentos ': row[0],
                    'str_CodTipoDocumento ': row[1],
                    'str_idSuscripcion': row[2],
                    'str_Nombre': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreacion': row[6],
                    'int_idUsuarioModificacion': row[7],
                }
            return None

    def get(self, request, pk):
        clausula = self.get_object(pk)
        if clausula:
            return Response(clausula)
        return Response(status=status.HTTP_404_NOT_FOUND)

    def put(self, request, pk):
        clausula = self.get_object(pk)
        if not clausula:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = TipDocumentoUpdateSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                UPDATE tm_tipodocumento 
                SET  str_CodTipoDocumento=%s,str_Nombre=%s, dt_FechaModificacion=NOW(), int_idUsuarioModificacion=%s 
                WHERE int_idTipoDocumentos =%s
                """
                cursor.execute(query, [
                    serializer.validated_data['str_CodTipoDocumento'],
                    serializer.validated_data['str_Nombre'],
                    serializer.validated_data['int_idUsuarioModificacion'],
                    pk
                ])
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tm_tipodocumento WHERE int_idTipoDocumentos  = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)