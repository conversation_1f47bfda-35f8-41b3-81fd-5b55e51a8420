from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import TipoCambioSerializer
import re
from django.utils import timezone
from datetime import datetime
class TipoCambioAPIView(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )
        query = "SELECT * FROM tm_tipoCambio WHERE str_idSuscripcion = %s AND int_idEmpresa = %s ORDER BY dt_FechaCreacion DESC"
        params = [str_idSuscripcion, int_idEmpresa]
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'str_idSuscripcion': row[0],
                    'str_valorCambio': row[1],
                    'int_estado': row[2],
                    'dt_FechaCambio': row[3].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row[3], datetime) else None,
                    'str_idSuscripcion': row[4],
                    'dt_FechaCreacion': row[5].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row[5], datetime) else None,
                    'dt_FechaModificacion': row[6].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row[6], datetime) else None,
                    'int_idUsuarioCreacion': row[7],
                    'int_idUsuarioModificacion': row[8],
                    'int_idEmpresa': row[9]
                }
                for row in rows
            ]
        return Response(clausulas)
    def post(self, request):
        serializer = TipoCambioSerializer(data=request.data)
        
        if serializer.is_valid():
            suscripcion = serializer.validated_data['str_idSuscripcion']
            int_idUsuarioModificacion = serializer.validated_data['int_idUsuarioModificacion']
            int_idEmpresa = serializer.validated_data['int_idEmpresa']
            # Obtener el último registro
            select_query = """
            SELECT * FROM tm_tipoCambio
            WHERE str_idSuscripcion = %s AND  int_idEmpresa= %s
            ORDER BY dt_FechaCreacion DESC
            LIMIT 1;
            """
            params = [suscripcion,int_idEmpresa]

            with connection.cursor() as cursor:
                cursor.execute(select_query, params)
                last_record = cursor.fetchone()

                if last_record:
                    # Si existe un registro anterior, actualizamos su estado
                    update_query = """
                    UPDATE tm_tipoCambio
                    SET dt_FechaModificacion = NOW(),dt_FechaCambio=NOW(), int_idUsuarioModificacion = %s, int_estado = 0
                    WHERE int_idTipoCambio = %s ;
                    """
                    cursor.execute(update_query, [ int_idUsuarioModificacion, last_record[0]])

                # Realizar el insert en todos los casos
                insert_query = """
                INSERT INTO tm_tipoCambio (
                    str_valorCambio, int_estado, str_idSuscripcion,
                    dt_FechaCreacion, int_idUsuarioCreacion,int_idEmpresa
                ) VALUES (%s, %s,  %s, NOW(), %s, %s);
                """
                cursor.execute(insert_query, [
                    serializer.validated_data['str_valorCambio'],
                    1,
                    suscripcion,
                    int_idUsuarioModificacion,
                    int_idEmpresa,
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        

