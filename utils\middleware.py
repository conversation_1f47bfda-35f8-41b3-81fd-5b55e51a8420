import jwt
from .security import JWT
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin


class JWTMiddleware(MiddlewareMixin):

    def process_request(self, request):
        jwt_obj = JWT()
        token = None
        auth_header = request.headers.get("Authorization", None)
        print(auth_header)
        if auth_header:
            auth_parts = auth_header.split()
            if len(auth_parts) == 2 and auth_parts[0] == "Bearer":
                token = auth_parts[1]

        if not token:
            return JsonResponse({"message": "Token is missing!"}, status=401)

        payload = jwt_obj.decode_token(token)
        if "error" in payload:
            return JsonResponse(payload, status=401)
