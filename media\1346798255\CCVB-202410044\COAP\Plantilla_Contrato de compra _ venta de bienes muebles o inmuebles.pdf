-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost:3306
-- Tiempo de generación: 03-09-2024 a las 22:36:58
-- Versión del servidor: 8.0.30
-- Versión de PHP: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `prisma_procesos`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tc_empresas`
--

CREATE TABLE `tc_empresas` (
  `str_idEmpresa` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscriptor` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEmpresa` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RazonSocial` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Ruc` int NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `th_historialusuarios`
--

CREATE TABLE `th_historialusuarios` (
  `int_idHistorialUsuario` int NOT NULL,
  `str_IdSuscripcion` varchar(10) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Estado` int DEFAULT NULL,
  `dt_FechaEliminacion` datetime NOT NULL,
  `str_CorreoEliminacion` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `th_sesiones`
--

CREATE TABLE `th_sesiones` (
  `int_idSesiones` int NOT NULL,
  `str_idUsuario` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaInicio` datetime DEFAULT NULL,
  `dt_FechaCierre` datetime DEFAULT NULL,
  `str_Estado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_ipAddress` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `th_sesiones`
--

INSERT INTO `th_sesiones` (`int_idSesiones`, `str_idUsuario`, `dt_FechaInicio`, `dt_FechaCierre`, `str_Estado`, `str_ipAddress`) VALUES
(1, '<EMAIL>', '2024-08-28 14:21:02', NULL, 'Autenticacion 1 Fallida', '*************'),
(2, '<EMAIL>', '2024-08-28 14:21:03', NULL, 'Autenticacion 1 Fallida', '*************'),
(3, '<EMAIL>', '2024-08-28 14:21:04', NULL, 'Autenticacion 1 Fallida', '*************'),
(4, '<EMAIL>', '2024-08-28 14:21:05', NULL, 'Autenticacion 1 Fallida', '*************'),
(5, '<EMAIL>', '2024-08-28 14:25:51', NULL, 'Autenticacion 1 Exitoso', '*************'),
(6, '<EMAIL>', '2024-08-28 14:26:14', NULL, 'Credenciales Incorrectas', '*************'),
(7, '<EMAIL>', '2024-08-28 14:26:25', NULL, 'Ingreso Exitoso', '*************'),
(8, '<EMAIL>', '2024-08-28 14:27:13', NULL, 'Autenticacion 1 Exitoso', '*************'),
(9, '<EMAIL>', '2024-08-28 14:27:22', NULL, 'Ingreso Exitoso', '*************'),
(10, '<EMAIL>', '2024-08-28 14:27:58', NULL, 'Autenticacion 1 Exitoso', '*************'),
(11, '<EMAIL>', '2024-08-28 14:28:05', NULL, 'Ingreso Exitoso', '*************'),
(12, '<EMAIL>', '2024-08-28 14:29:06', NULL, 'Autenticacion 1 Exitoso', '*************'),
(13, '<EMAIL>', '2024-08-28 14:29:17', NULL, 'Ingreso Exitoso', '*************'),
(14, '<EMAIL>', '2024-08-28 14:30:27', NULL, 'Autenticacion 1 Exitoso', '*************'),
(15, '<EMAIL>', '2024-08-28 14:30:44', NULL, 'Autenticacion 1 Exitoso', '*************'),
(16, '<EMAIL>', '2024-08-28 14:30:53', NULL, 'Ingreso Exitoso', '*************'),
(17, '<EMAIL>', '2024-08-28 14:31:25', NULL, 'Autenticacion 1 Exitoso', '*************'),
(18, '<EMAIL>', '2024-08-28 14:31:35', NULL, 'Ingreso Exitoso', '*************'),
(19, '<EMAIL>', '2024-08-28 14:34:08', NULL, 'Autenticacion 1 Exitoso', '*************'),
(20, '<EMAIL>', '2024-08-28 14:34:15', NULL, 'Ingreso Exitoso', '*************'),
(21, '<EMAIL>', '2024-08-28 14:36:08', NULL, 'Autenticacion 1 Exitoso', '*************'),
(22, '<EMAIL>', '2024-08-28 14:36:16', NULL, 'Ingreso Exitoso', '*************'),
(23, '<EMAIL>', '2024-08-28 14:37:26', NULL, 'Autenticacion 1 Exitoso', '*************'),
(24, '<EMAIL>', '2024-08-28 14:37:33', NULL, 'Ingreso Exitoso', '*************'),
(25, '<EMAIL>', '2024-08-28 14:46:07', NULL, 'Autenticacion 1 Exitoso', '*************'),
(26, '<EMAIL>', '2024-08-28 14:46:15', NULL, 'Ingreso Exitoso', '*************'),
(27, '<EMAIL>', '2024-08-28 14:47:12', NULL, 'Autenticacion 1 Exitoso', '*************'),
(28, '<EMAIL>', '2024-08-28 14:47:21', NULL, 'Ingreso Exitoso', '*************'),
(29, '<EMAIL>', '2024-08-28 14:47:44', NULL, 'Ingreso Exitoso', '*************'),
(30, '<EMAIL>', '2024-08-28 14:47:59', NULL, 'Autenticacion 1 Exitoso', '*************'),
(31, '<EMAIL>', '2024-08-28 14:48:06', NULL, 'Ingreso Exitoso', '*************'),
(32, '<EMAIL>', '2024-08-28 14:48:32', NULL, 'Ingreso Exitoso', '*************'),
(33, '<EMAIL>', '2024-08-28 14:48:44', NULL, 'Autenticacion 1 Exitoso', '*************'),
(34, '<EMAIL>', '2024-08-28 14:48:55', NULL, 'Ingreso Exitoso', '*************'),
(35, '<EMAIL>', '2024-08-28 14:49:41', NULL, 'Autenticacion 1 Exitoso', '*************'),
(36, '<EMAIL>', '2024-08-28 14:50:01', NULL, 'Ingreso Exitoso', '*************'),
(37, '<EMAIL>', '2024-08-28 14:51:47', NULL, 'Autenticacion 1 Exitoso', '*************'),
(38, '<EMAIL>', '2024-08-28 14:51:57', NULL, 'Ingreso Exitoso', '*************'),
(39, '<EMAIL>', '2024-08-28 14:52:25', NULL, 'Autenticacion 1 Exitoso', '*************'),
(40, '<EMAIL>', '2024-08-28 14:52:42', NULL, 'Ingreso Exitoso', '*************'),
(41, '<EMAIL>', '2024-08-28 14:53:09', NULL, 'Autenticacion 1 Exitoso', '*************'),
(42, '<EMAIL>', '2024-08-28 14:53:21', NULL, 'Autenticacion 1 Exitoso', '*************'),
(43, '<EMAIL>', '2024-08-28 14:53:34', NULL, 'Ingreso Exitoso', '*************'),
(44, '<EMAIL>', '2024-08-28 15:00:57', NULL, 'Autenticacion 1 Exitoso', '*************'),
(45, '<EMAIL>', '2024-08-28 15:01:03', NULL, 'Autenticacion 1 Fallida', '*************'),
(46, '<EMAIL>', '2024-08-28 15:01:04', NULL, 'Autenticacion 1 Fallida', '*************'),
(47, '<EMAIL>', '2024-08-28 15:01:07', NULL, 'Autenticacion 1 Exitoso', '*************'),
(48, '<EMAIL>', '2024-08-28 15:01:29', NULL, 'Ingreso Exitoso', '*************'),
(49, '<EMAIL>', '2024-08-28 17:07:10', NULL, 'Autenticacion 1 Exitoso', '*************'),
(50, '<EMAIL>', '2024-08-28 17:07:23', NULL, 'Ingreso Exitoso', '*************'),
(51, '<EMAIL>', '2024-08-28 17:07:48', NULL, 'Autenticacion 1 Exitoso', '*************'),
(52, '<EMAIL>', '2024-08-28 17:07:48', NULL, 'Autenticacion 1 Exitoso', '*************'),
(53, '<EMAIL>', '2024-08-28 17:07:54', NULL, 'Ingreso Exitoso', '*************'),
(54, '<EMAIL>', '2024-08-28 17:24:59', NULL, 'Autenticacion 1 Exitoso', '*************'),
(55, '<EMAIL>', '2024-08-28 17:26:35', NULL, 'Ingreso Exitoso', '*************'),
(56, '<EMAIL>', '2024-08-29 14:37:21', NULL, 'Autenticacion 1 Exitoso', '192.10.10.1'),
(57, '<EMAIL>', '2024-08-29 14:46:53', NULL, 'Ingreso Exitoso', '192.10.10.1'),
(58, '<EMAIL>', '2024-09-02 09:29:43', '2024-09-02 09:31:45', 'Ingreso Exitoso', '192.10.10.1'),
(59, '<EMAIL>', '2024-09-02 10:02:13', NULL, 'Autenticacion 1 Exitoso', '192.10.10.1'),
(60, '<EMAIL>', '2024-09-02 10:06:05', NULL, 'Autenticacion 1 Exitoso', '*************'),
(61, '<EMAIL>', '2024-09-02 10:07:09', NULL, 'Autenticacion 1 Exitoso', '*************'),
(62, '<EMAIL>', '2024-09-02 10:07:46', NULL, 'Autenticacion 1 Fallida', '*************'),
(63, '<EMAIL>', '2024-09-02 10:08:53', NULL, 'Autenticacion 1 Exitoso', '*************'),
(64, '<EMAIL>', '2024-09-02 10:09:39', NULL, 'Ingreso Exitoso', '*************'),
(65, '<EMAIL>', '2024-09-02 10:52:08', NULL, 'Autenticacion 1 Exitoso', '*************'),
(66, '<EMAIL>', '2024-09-02 10:52:28', '2024-09-02 16:39:23', 'Ingreso Exitoso', '*************'),
(67, '<EMAIL>', '2024-09-02 16:39:28', NULL, 'Autenticacion 1 Exitoso', '*************'),
(68, '<EMAIL>', '2024-09-02 16:39:33', NULL, 'Credenciales Incorrectas', '*************'),
(69, '<EMAIL>', '2024-09-02 16:41:11', NULL, 'Autenticacion 1 Exitoso', '*************'),
(70, '<EMAIL>', '2024-09-02 16:41:17', NULL, 'Ingreso Exitoso', '*************'),
(71, '<EMAIL>', '2024-09-02 16:41:35', NULL, 'Autenticacion 1 Exitoso', '*************'),
(72, '<EMAIL>', '2024-09-02 16:41:44', '2024-09-02 16:52:56', 'Ingreso Exitoso', '*************'),
(73, '<EMAIL>', '2024-09-02 16:53:01', NULL, 'Autenticacion 1 Exitoso', '*************'),
(74, '<EMAIL>', '2024-09-02 16:53:05', '2024-09-02 17:15:09', 'Ingreso Exitoso', '*************'),
(75, '<EMAIL>', '2024-09-02 17:15:23', NULL, 'Autenticacion 1 Exitoso', '*************'),
(76, '<EMAIL>', '2024-09-02 17:15:34', '2024-09-02 17:38:10', 'Ingreso Exitoso', '*************'),
(77, '<EMAIL>', '2024-09-02 17:38:15', NULL, 'Autenticacion 1 Exitoso', '*************'),
(78, '<EMAIL>', '2024-09-02 17:38:19', '2024-09-02 17:38:30', 'Ingreso Exitoso', '*************'),
(79, '<EMAIL>', '2024-09-02 17:38:33', NULL, 'Autenticacion 1 Exitoso', '*************'),
(80, '<EMAIL>', '2024-09-02 17:38:38', '2024-09-02 17:43:50', 'Ingreso Exitoso', '*************'),
(81, '<EMAIL>', '2024-09-02 17:47:31', NULL, 'Autenticacion 1 Exitoso', '*************'),
(82, '<EMAIL>', '2024-09-02 17:47:38', NULL, 'Credenciales Incorrectas', '*************'),
(83, '<EMAIL>', '2024-09-02 17:47:44', '2024-09-02 17:48:11', 'Ingreso Exitoso', '*************'),
(84, '<EMAIL>', '2024-09-02 17:48:15', NULL, 'Autenticacion 1 Exitoso', '*************'),
(85, '<EMAIL>', '2024-09-02 17:48:18', '2024-09-02 17:48:58', 'Ingreso Exitoso', '*************'),
(86, '<EMAIL>', '2024-09-02 17:49:00', NULL, 'Autenticacion 1 Exitoso', '*************'),
(87, '<EMAIL>', '2024-09-02 17:50:40', NULL, 'Autenticacion 1 Exitoso', '*************'),
(88, '<EMAIL>', '2024-09-02 17:52:23', '2024-09-02 17:53:19', 'Ingreso Exitoso', '*************'),
(89, '<EMAIL>', '2024-09-02 17:53:26', NULL, 'Autenticacion 1 Exitoso', '*************'),
(90, '<EMAIL>', '2024-09-02 17:53:29', '2024-09-02 17:57:09', 'Ingreso Exitoso', '*************'),
(91, '<EMAIL>', '2024-09-03 08:10:55', NULL, 'Autenticacion 1 Exitoso', '*************'),
(92, '<EMAIL>', '2024-09-03 08:11:02', '2024-09-03 08:27:10', 'Ingreso Exitoso', '*************'),
(93, '<EMAIL>', '2024-09-03 08:27:15', NULL, 'Autenticacion 1 Exitoso', '*************'),
(94, '<EMAIL>', '2024-09-03 08:27:25', '2024-09-03 08:31:32', 'Ingreso Exitoso', '*************'),
(95, '<EMAIL>', '2024-09-03 08:31:34', NULL, 'Autenticacion 1 Exitoso', '*************'),
(96, '<EMAIL>', '2024-09-03 08:31:42', '2024-09-03 08:59:54', 'Ingreso Exitoso', '*************'),
(97, '<EMAIL>', '2024-09-03 08:31:45', NULL, 'Ingreso Exitoso', '*************'),
(98, '<EMAIL>', '2024-09-03 09:00:00', NULL, 'Autenticacion 1 Exitoso', '*************'),
(99, '<EMAIL>', '2024-09-03 09:00:03', '2024-09-03 09:04:35', 'Ingreso Exitoso', '*************'),
(100, '<EMAIL>', '2024-09-03 09:04:37', NULL, 'Autenticacion 1 Exitoso', '*************'),
(101, '<EMAIL>', '2024-09-03 09:04:46', NULL, 'Ingreso Exitoso', '*************');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_aplicaciones`
--

CREATE TABLE `tm_aplicaciones` (
  `int_idAplicacion` int NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `fl_avatar` blob NOT NULL,
  `str_descripcion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_estados`
--

CREATE TABLE `tm_estados` (
  `str_idEstado` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreEstado` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_interlocutores`
--

CREATE TABLE `tm_interlocutores` (
  `int_idInterlocutor` int NOT NULL,
  `str_IdSuscripcion` varchar(10) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_TipoDocumento` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Documento` int NOT NULL,
  `str_Domicilio` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombres` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_materias`
--

CREATE TABLE `tm_materias` (
  `str_idMateria` varchar(4) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_perfiles`
--

CREATE TABLE `tm_perfiles` (
  `int_idPerfil` int NOT NULL,
  `str_Nombre` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_perfiles`
--

INSERT INTO `tm_perfiles` (`int_idPerfil`, `str_Nombre`, `dt_FechaCreacion`, `dt_FechaModificacion`, `int_idUsuarioCreacion`, `int_idUsuarioModificacion`) VALUES
(5, 'Owner', NULL, NULL, NULL, NULL),
(6, 'Analista', NULL, NULL, NULL, NULL),
(7, 'Analista2', '2024-09-02 11:15:00', NULL, 1, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_probabilidadexito`
--

CREATE TABLE `tm_probabilidadexito` (
  `str_idProbabilidadExito` varchar(4) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_descripcion` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_suscripcion`
--

CREATE TABLE `tm_suscripcion` (
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime NOT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipoacceso`
--

CREATE TABLE `tm_tipoacceso` (
  `str_idTipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DescripcionTipoAcceso` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipodocumento`
--

CREATE TABLE `tm_tipodocumento` (
  `str_idTipoDocumento` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_tipoproceso`
--

CREATE TABLE `tm_tipoproceso` (
  `str_idTipoSolicitud` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Nombre` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int NOT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tm_usuarios`
--

CREATE TABLE `tm_usuarios` (
  `int_idUsuarios` int NOT NULL,
  `str_Nombres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Apellidos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Correo` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Documento` int NOT NULL,
  `str_UnidadNegocio` varchar(250) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Clave` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_Estado` int DEFAULT '1',
  `str_Codigo` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `str_idUsuarioCreacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_idUsuarioModificacion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Volcado de datos para la tabla `tm_usuarios`
--

INSERT INTO `tm_usuarios` (`int_idUsuarios`, `str_Nombres`, `str_Apellidos`, `str_Correo`, `int_Documento`, `str_UnidadNegocio`, `str_Clave`, `int_Estado`, `str_Codigo`, `dt_FechaCreacion`, `dt_FechaModificacion`, `str_idUsuarioCreacion`, `str_idUsuarioModificacion`) VALUES
(1, 'Jose Maria', 'Torres Chirinos', '<EMAIL>', 0, '', '$2b$12$iuDvLY.hKLsnIxeWnf5ec.ajaU7hHeBDK8sWoAX18uDEJa/9lEUIG', 1, NULL, NULL, NULL, NULL, NULL),
(4, 'Jose Analista', 'Torres Chirinos', '<EMAIL>', 0, '', '$2b$12$QZd/7QaOE5zZbwpibQcVGurG.rI48cy1./WeRMmbAsblHnmLxgmya', 1, NULL, NULL, NULL, NULL, NULL),
(5, 'Lauren David2', 'Arica guerrero2', '<EMAIL>', 0, '', '$2b$12$T7olckUkTeOq3joN5U1nbOxtsnBzWRP5lYF97v25qWTBjzqmg7HVO', 1, NULL, '2024-09-02 14:53:30', '2024-09-02 15:08:12', '1', '1');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_accesos`
--

CREATE TABLE `tr_accesos` (
  `int_idAccesos` int NOT NULL,
  `int_idPerfil` int NOT NULL,
  `str_TipoAcceso` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `str_Valor` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci DEFAULT NULL,
  `int_idAplicacion` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_accesousuario`
--

CREATE TABLE `tr_accesousuario` (
  `int_idAccesoUsuario` int NOT NULL,
  `int_idAcceso` int NOT NULL,
  `int_idPerfil` int NOT NULL,
  `int_idAplicacion` int NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_archivosdocumentos`
--

CREATE TABLE `tr_archivosdocumentos` (
  `int_idArchivos` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idProceso` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idTipoDoc` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_RutaArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_NombreArchivo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ExtencionArchivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_TamañoArchivo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_asisnacionaplicacion`
--

CREATE TABLE `tr_asisnacionaplicacion` (
  `int_idAsignacion` int NOT NULL,
  `int_idUsuario` int NOT NULL,
  `int_idAplicacion` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_correlativosolicitud`
--

CREATE TABLE `tr_correlativosolicitud` (
  `int_idCorrelativo` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idTipoSolicitud` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_CorrelativoTipoSolicitud` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_estadoregistros`
--

CREATE TABLE `tr_estadoregistros` (
  `int_idEstadoRegistros` int NOT NULL,
  `str_idSuscriptor` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idProceso` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idEstado` varchar(4) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCambio` datetime NOT NULL,
  `dt_FechaCreacion` datetime DEFAULT NULL,
  `dt_FechaModificacion` datetime DEFAULT NULL,
  `int_idUsuarioCreacion` int DEFAULT NULL,
  `int_idUsuarioModificacion` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_procesocontenido`
--

CREATE TABLE `tr_procesocontenido` (
  `int_idProcesoContenido` int NOT NULL,
  `str_idProceso` varchar(15) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idDemantante` int NOT NULL,
  `int_idRepresentanteDemandante` int NOT NULL,
  `int_idDemandado` int NOT NULL,
  `int_idRepresentanteDemandado` int NOT NULL,
  `str_Entidad` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Instancia` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Fuego` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DemandaObjeto` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_DemandaValor` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ContingenciaMoneda` varchar(20) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ContingenciaProvision` varchar(2) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_ContingenciaValor` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_Observaciones` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaCreacion` datetime NOT NULL,
  `dt_FechaModificacion` datetime NOT NULL,
  `int_IdUsuarioCreador` int NOT NULL,
  `int_IdUsuarioModificador` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_procesos`
--

CREATE TABLE `tr_procesos` (
  `str_idProceso` varchar(15) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idSuscripcion` varchar(10) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idEmpresa` varchar(15) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_UnidadNegocio` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idTipoProceso` varchar(4) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_idMateria` varchar(4) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `dt_FechaRegistro` datetime NOT NULL,
  `str_EstudioExterno` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_HonorarioEstudioExterno` double NOT NULL,
  `str_MonedaHoronariosEE` varchar(20) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str_IdProbabilidadExito` varchar(4) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str__clienteAsociado` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str__CARazonSocial` varchar(255) COLLATE utf8mb4_spanish2_ci NOT NULL,
  `str__CARUC` int NOT NULL,
  `str__CAContrato` int NOT NULL,
  `str__DocumentosAdjuntos` varchar(2) COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tr_suscriptores`
--

CREATE TABLE `tr_suscriptores` (
  `int_idSuscriptor` int NOT NULL,
  `int_idUsuario` int NOT NULL,
  `str_idSuscripcion` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_spanish2_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_spanish2_ci;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD PRIMARY KEY (`str_idEmpresa`),
  ADD UNIQUE KEY `int_Ruc` (`int_Ruc`),
  ADD KEY `empresas_suscriptor_FK` (`str_idSuscriptor`);

--
-- Indices de la tabla `th_historialusuarios`
--
ALTER TABLE `th_historialusuarios`
  ADD PRIMARY KEY (`int_idHistorialUsuario`);

--
-- Indices de la tabla `th_sesiones`
--
ALTER TABLE `th_sesiones`
  ADD PRIMARY KEY (`int_idSesiones`);

--
-- Indices de la tabla `tm_aplicaciones`
--
ALTER TABLE `tm_aplicaciones`
  ADD PRIMARY KEY (`int_idAplicacion`);

--
-- Indices de la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD PRIMARY KEY (`str_idEstado`),
  ADD KEY `Estados_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Estados_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD PRIMARY KEY (`int_idInterlocutor`),
  ADD KEY `interlocutores_suscripcion_FK` (`str_IdSuscripcion`);

--
-- Indices de la tabla `tm_materias`
--
ALTER TABLE `tm_materias`
  ADD PRIMARY KEY (`str_idMateria`),
  ADD KEY `materias_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `materias_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_perfiles`
--
ALTER TABLE `tm_perfiles`
  ADD PRIMARY KEY (`int_idPerfil`),
  ADD UNIQUE KEY `nombre` (`str_Nombre`),
  ADD KEY `Perfiles_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Perfiles_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_probabilidadexito`
--
ALTER TABLE `tm_probabilidadexito`
  ADD PRIMARY KEY (`str_idProbabilidadExito`);

--
-- Indices de la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD PRIMARY KEY (`str_idSuscripcion`),
  ADD KEY `Suscripciones_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `Suscripciones_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tipoacceso`
--
ALTER TABLE `tm_tipoacceso`
  ADD PRIMARY KEY (`str_idTipoAcceso`);

--
-- Indices de la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD PRIMARY KEY (`str_idTipoDocumento`),
  ADD UNIQUE KEY `codigo_tipoDocumento` (`str_idTipoDocumento`),
  ADD KEY `TipoDocumentos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoDocumentos_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_tipoproceso`
--
ALTER TABLE `tm_tipoproceso`
  ADD PRIMARY KEY (`str_idTipoSolicitud`),
  ADD UNIQUE KEY `codigo_tipoSolicitud` (`str_idTipoSolicitud`),
  ADD KEY `TipoSolicitud_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `TipoSolicitud_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  ADD PRIMARY KEY (`int_idUsuarios`) USING BTREE,
  ADD UNIQUE KEY `correo` (`str_Correo`);

--
-- Indices de la tabla `tr_accesos`
--
ALTER TABLE `tr_accesos`
  ADD PRIMARY KEY (`int_idAccesos`),
  ADD KEY `perfil_tabla_FK` (`int_idPerfil`),
  ADD KEY `acceso_Aplicacion_FK` (`int_idAplicacion`),
  ADD KEY `acceso_TipoAcceso_FK` (`str_TipoAcceso`);

--
-- Indices de la tabla `tr_accesousuario`
--
ALTER TABLE `tr_accesousuario`
  ADD PRIMARY KEY (`int_idAccesoUsuario`),
  ADD KEY `accesos_acceso_FK` (`int_idAcceso`),
  ADD KEY `accesos_perfil_FK` (`int_idPerfil`),
  ADD KEY `accesos_usuario_FK` (`int_idUsuario`),
  ADD KEY `accesos_aplicacion_FK` (`int_idAplicacion`),
  ADD KEY `accesos_suscripcion_FK` (`str_idSuscripcion`);

--
-- Indices de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD PRIMARY KEY (`int_idArchivos`),
  ADD KEY `archivos_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `archivos_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `archivos_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `archivos_Proceso_FK` (`str_idProceso`),
  ADD KEY `archivos_TipoDocumento_FK` (`str_idTipoDoc`);

--
-- Indices de la tabla `tr_asisnacionaplicacion`
--
ALTER TABLE `tr_asisnacionaplicacion`
  ADD PRIMARY KEY (`int_idAsignacion`),
  ADD KEY `asignacion_suscriptor_FK` (`int_idUsuario`),
  ADD KEY `asignacion_aplicacion_FK` (`int_idAplicacion`);

--
-- Indices de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  ADD PRIMARY KEY (`int_idCorrelativo`),
  ADD KEY `correlativo_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `correlativo_tipoSolicitud_FK` (`str_idTipoSolicitud`),
  ADD KEY `correlativo_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `correlativo_UsuarioModificador_FK` (`int_idUsuarioModificacion`);

--
-- Indices de la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  ADD PRIMARY KEY (`int_idEstadoRegistros`),
  ADD KEY `EstadoRegistros_suscriptor_FK` (`str_idSuscriptor`),
  ADD KEY `EstadoRegistros_UsuarioCreador_FK` (`int_idUsuarioCreacion`),
  ADD KEY `EstadoRegistros_UsuarioModificador_FK` (`int_idUsuarioModificacion`),
  ADD KEY `EstadoRegistros_proceso_FK` (`str_idProceso`),
  ADD KEY `EstadoRegistros_Estado_FK` (`str_idEstado`);

--
-- Indices de la tabla `tr_procesocontenido`
--
ALTER TABLE `tr_procesocontenido`
  ADD PRIMARY KEY (`int_idProcesoContenido`),
  ADD KEY `Contenido_Demandante_FK` (`int_idDemantante`),
  ADD KEY `Contenido_RepresentanteDemandante_FK` (`int_idRepresentanteDemandante`),
  ADD KEY `Contenido_Demandado_FK` (`int_idDemandado`),
  ADD KEY `Contenido_RepresentanteDemandado_FK` (`int_idRepresentanteDemandado`),
  ADD KEY `Contenido_UsuarioCreador_FK` (`int_IdUsuarioCreador`),
  ADD KEY `Contenido_UsuarioModificador_FK` (`int_IdUsuarioModificador`),
  ADD KEY `Contenido_Proceso_FK` (`str_idProceso`);

--
-- Indices de la tabla `tr_procesos`
--
ALTER TABLE `tr_procesos`
  ADD PRIMARY KEY (`str_idProceso`),
  ADD KEY `proceso_suscripcion_FK` (`str_idSuscripcion`),
  ADD KEY `proceso_usuario_FK` (`int_idUsuario`),
  ADD KEY `proceso_empresa_FK` (`str_idEmpresa`),
  ADD KEY `proceso_tipoProceso_FK` (`str_idTipoProceso`),
  ADD KEY `proceso_ProbabilidadExito_FK` (`str_IdProbabilidadExito`),
  ADD KEY `proceso_materia_FK` (`str_idMateria`);

--
-- Indices de la tabla `tr_suscriptores`
--
ALTER TABLE `tr_suscriptores`
  ADD PRIMARY KEY (`int_idSuscriptor`),
  ADD KEY `suscriptor_usuario_FK` (`int_idUsuario`),
  ADD KEY `suscriptor_suscripcion_FK` (`str_idSuscripcion`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `th_historialusuarios`
--
ALTER TABLE `th_historialusuarios`
  MODIFY `int_idHistorialUsuario` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `th_sesiones`
--
ALTER TABLE `th_sesiones`
  MODIFY `int_idSesiones` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=102;

--
-- AUTO_INCREMENT de la tabla `tm_aplicaciones`
--
ALTER TABLE `tm_aplicaciones`
  MODIFY `int_idAplicacion` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tm_perfiles`
--
ALTER TABLE `tm_perfiles`
  MODIFY `int_idPerfil` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT de la tabla `tm_usuarios`
--
ALTER TABLE `tm_usuarios`
  MODIFY `int_idUsuarios` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT de la tabla `tr_accesos`
--
ALTER TABLE `tr_accesos`
  MODIFY `int_idAccesos` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT de la tabla `tr_accesousuario`
--
ALTER TABLE `tr_accesousuario`
  MODIFY `int_idAccesoUsuario` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  MODIFY `int_idArchivos` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_asisnacionaplicacion`
--
ALTER TABLE `tr_asisnacionaplicacion`
  MODIFY `int_idAsignacion` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  MODIFY `int_idCorrelativo` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `tr_procesocontenido`
--
ALTER TABLE `tr_procesocontenido`
  MODIFY `int_idProcesoContenido` int NOT NULL AUTO_INCREMENT;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `tc_empresas`
--
ALTER TABLE `tc_empresas`
  ADD CONSTRAINT `empresas_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_estados`
--
ALTER TABLE `tm_estados`
  ADD CONSTRAINT `Estados_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Estados_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_interlocutores`
--
ALTER TABLE `tm_interlocutores`
  ADD CONSTRAINT `interlocutores_suscripcion_FK` FOREIGN KEY (`str_IdSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_materias`
--
ALTER TABLE `tm_materias`
  ADD CONSTRAINT `materias_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `materias_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_perfiles`
--
ALTER TABLE `tm_perfiles`
  ADD CONSTRAINT `Perfiles_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Perfiles_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_suscripcion`
--
ALTER TABLE `tm_suscripcion`
  ADD CONSTRAINT `Suscripciones_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Suscripciones_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tipodocumento`
--
ALTER TABLE `tm_tipodocumento`
  ADD CONSTRAINT `TipoDocumentos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoDocumentos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tm_tipoproceso`
--
ALTER TABLE `tm_tipoproceso`
  ADD CONSTRAINT `TipoSolicitud_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `TipoSolicitud_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_accesos`
--
ALTER TABLE `tr_accesos`
  ADD CONSTRAINT `acceso_Aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `acceso_perfil_FK` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `acceso_TipoAcceso_FK` FOREIGN KEY (`str_TipoAcceso`) REFERENCES `tm_tipoacceso` (`str_idTipoAcceso`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_accesousuario`
--
ALTER TABLE `tr_accesousuario`
  ADD CONSTRAINT `accesos_acceso_FK` FOREIGN KEY (`int_idAcceso`) REFERENCES `tr_accesos` (`int_idAccesos`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_perfil_FK` FOREIGN KEY (`int_idPerfil`) REFERENCES `tm_perfiles` (`int_idPerfil`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `accesos_usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_archivosdocumentos`
--
ALTER TABLE `tr_archivosdocumentos`
  ADD CONSTRAINT `archivos_Proceso_FK` FOREIGN KEY (`str_idProceso`) REFERENCES `tr_procesos` (`str_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_TipoDocumento_FK` FOREIGN KEY (`str_idTipoDoc`) REFERENCES `tm_tipodocumento` (`str_idTipoDocumento`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `archivos_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_asisnacionaplicacion`
--
ALTER TABLE `tr_asisnacionaplicacion`
  ADD CONSTRAINT `asignacion_aplicacion_FK` FOREIGN KEY (`int_idAplicacion`) REFERENCES `tm_aplicaciones` (`int_idAplicacion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `asignacion_Usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_correlativosolicitud`
--
ALTER TABLE `tr_correlativosolicitud`
  ADD CONSTRAINT `correlativo_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_tipoSolicitud_FK` FOREIGN KEY (`str_idTipoSolicitud`) REFERENCES `tm_tipoproceso` (`str_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `correlativo_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_estadoregistros`
--
ALTER TABLE `tr_estadoregistros`
  ADD CONSTRAINT `EstadoRegistros_Estado_FK` FOREIGN KEY (`str_idEstado`) REFERENCES `tm_estados` (`str_idEstado`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_proceso_FK` FOREIGN KEY (`str_idProceso`) REFERENCES `tr_procesos` (`str_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_suscriptor_FK` FOREIGN KEY (`str_idSuscriptor`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_UsuarioCreador_FK` FOREIGN KEY (`int_idUsuarioCreacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `EstadoRegistros_UsuarioModificador_FK` FOREIGN KEY (`int_idUsuarioModificacion`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_procesocontenido`
--
ALTER TABLE `tr_procesocontenido`
  ADD CONSTRAINT `Contenido_Demandado_FK` FOREIGN KEY (`int_idDemandado`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Contenido_Demandante_FK` FOREIGN KEY (`int_idDemantante`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Contenido_Proceso_FK` FOREIGN KEY (`str_idProceso`) REFERENCES `tr_procesos` (`str_idProceso`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Contenido_RepresentanteDemandado_FK` FOREIGN KEY (`int_idRepresentanteDemandado`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Contenido_RepresentanteDemandante_FK` FOREIGN KEY (`int_idRepresentanteDemandante`) REFERENCES `tm_interlocutores` (`int_idInterlocutor`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Contenido_UsuarioCreador_FK` FOREIGN KEY (`int_IdUsuarioCreador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `Contenido_UsuarioModificador_FK` FOREIGN KEY (`int_IdUsuarioModificador`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_procesos`
--
ALTER TABLE `tr_procesos`
  ADD CONSTRAINT `proceso_empresa_FK` FOREIGN KEY (`str_idEmpresa`) REFERENCES `tc_empresas` (`str_idEmpresa`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `proceso_materia_FK` FOREIGN KEY (`str_idMateria`) REFERENCES `tm_materias` (`str_idMateria`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `proceso_ProbabilidadExito_FK` FOREIGN KEY (`str_IdProbabilidadExito`) REFERENCES `tm_probabilidadexito` (`str_idProbabilidadExito`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `proceso_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `proceso_tipoProceso_FK` FOREIGN KEY (`str_idTipoProceso`) REFERENCES `tm_tipoproceso` (`str_idTipoSolicitud`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `proceso_usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;

--
-- Filtros para la tabla `tr_suscriptores`
--
ALTER TABLE `tr_suscriptores`
  ADD CONSTRAINT `suscriptor_suscripcion_FK` FOREIGN KEY (`str_idSuscripcion`) REFERENCES `tm_suscripcion` (`str_idSuscripcion`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  ADD CONSTRAINT `suscriptor_usuario_FK` FOREIGN KEY (`int_idUsuario`) REFERENCES `tm_usuarios` (`int_idUsuarios`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
