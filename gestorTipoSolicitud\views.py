from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from django.utils import timezone

class GestorTipoSolicitudAPIView(APIView):
    def get(self, request):
        # Obtener el filtro opcional (por suscripción o gestor)
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        int_idGestor = request.query_params.get('int_idGestor')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        int_idTipoSolicitud = request.query_params.get('int_idTipoSolicitud')
        # Construir la consulta con filtros opcionales
        query = """
        SELECT 
              * ,
              CONCAT(u.str_Nombres, ' ', u.str_Apellidos) AS nombre_completo
        FROM tr_gestortiposolicitud GTS 
        LEFT JOIN 
             tm_usuarios u ON GTS.int_idGestor = u.int_idUsuarios
        WHERE GTS.str_idSuscripcion=%s
        """
        params = [str_idSuscripcion]
        
        
        if int_idGestor:
            query += " AND int_idGestor = %s"
            params.append(int_idGestor)
            
        if int_idTipoSolicitud:
            query += " AND int_idTipoSolicitud = %s"
            params.append(int_idTipoSolicitud)
        
        if int_idEmpresa:
            query += " AND int_idEmpresa = %s"
            params.append(int_idEmpresa)
            
        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()

            registros = [
                {
                    'int_idGestorxTipo': row[0],
                    'int_idGestor': row[1],
                    'int_idTipoSolicitud': row[2],
                    'str_idSuscripcion': row[3],
                    'dt_FechaCreacion': row[4],
                    'dt_FechaModificacion': row[5],
                    'int_idUsuarioCreador': row[6],
                    'int_idUsuarioModificacion': row[7],
                    'nombre_gestor': row[23],
                }
                for row in rows
            ]

        return Response(registros, status=status.HTTP_200_OK)

    def post(self, request):
        # Obtener los datos de la solicitud
        data = request.data
        required_fields = ['int_idGestor','int_idEmpresa', 'int_idTipoSolicitud', 'str_idSuscripcion', 'int_idUsuarioCreador']

        # Validar campos obligatorios
        for field in required_fields:
            if field not in data:
                return Response(
                    {"error": f"El campo '{field}' es obligatorio."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Insertar en la base de datos
        query = """
        INSERT INTO tr_gestortiposolicitud (
            int_idGestor, int_idTipoSolicitud, str_idSuscripcion,
            dt_FechaCreacion, int_idUsuarioCreador, int_idEmpresa
        ) VALUES (%s, %s, %s, NOW(), %s, %s)
        """
        params = [
            data['int_idGestor'],
            data['int_idTipoSolicitud'],
            data['str_idSuscripcion'],
            data['int_idUsuarioCreador'],
            data['int_idEmpresa']
        ]

        with connection.cursor() as cursor:
            cursor.execute(query, params)

        return Response({"mensaje": "Registro creado con éxito."}, status=status.HTTP_201_CREATED)

    def put(self, request, pk):
        # Actualizar un registro existente
        data = request.data
        fields = ['int_idGestor', 'int_idTipoSolicitud', 'str_idSuscripcion', 'int_idUsuarioModificacion']
        updates = []
        params = []

        # Crear la consulta dinámica para actualizar
        for field in fields:
            if field in data:
                updates.append(f"{field} = %s")
                params.append(data[field])

        if not updates:
            return Response(
                {"error": "Debe proporcionar al menos un campo para actualizar."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Agregar campos de auditoría
        updates.append("dt_FechaModificacion = NOW()")
        query = f"UPDATE tr_gestortiposolicitud SET {', '.join(updates)} WHERE int_idGestorxTipo = %s"
        params.append(pk)

        with connection.cursor() as cursor:
            cursor.execute(query, params)

        return Response({"mensaje": "Registro actualizado con éxito."}, status=status.HTTP_200_OK)

    def delete(self, request, pk):
        # Eliminar un registro por su ID
        query = "DELETE FROM tr_gestortiposolicitud WHERE int_idGestorxTipo = %s"

        with connection.cursor() as cursor:
            cursor.execute(query, [pk])

        return Response({"mensaje": "Registro eliminado con éxito."}, status=status.HTTP_200_OK)
    
