from django.urls import path
from .views import (
    SolicitudesTodo,
    SolicitudesCreate,
    SolicitudesArchivadas,
    SolicitudesVisibles,
    FiltrarSolicitudes,
    MatrizGeneral,
    EditarEstadoSolicitud,
    ArchivarSolicitud,
    SolicitudesGestor,
    MatrizGestor,
    SolicitudesController,
    SolicitudesSolicitante,
    HistorialEstadosSolicitud,
    SolicitudDetailAPIView,
    AsignarGestor,
    FiltrarSolicitudesSolicitante,
    FiltrarSolicitudesGestor,
    FiltrarSolicitudesController,
    InsertarAprobadorView,
    ListarAprobadoresView,
    FirmarContrato,
    SolicitudesAprobador,
    ActualizarAprobadorView,
    RechazarSolicitud,
    SolicitudesPorFinalizar,
    BusquedaRegistros,
    SolicitudesVencidas,
    SolicitudesCreateAdenda,
    SolicitudesPorFinalizarGestor,
    BusquedaSolicitudxCod,
    ContadorPorGestor,
    ContadorPorGestorAnio,
    SolicitudesCreateExtraJudiciales,
    UploadExcelAndCreateRequests,
    DataBot,
    SolicitudesCreateHistoricos,
    SolicitudesArchivosComprimir,
    BusquedaRegistroComprimir,
    MatrizSolicitante
)

urlpatterns = [
    path('api/solicitudes/', SolicitudesTodo.as_view(), name='solicitudes_todo'),
    path('api/solicitudes/crear/', SolicitudesCreate.as_view(), name='solicitudes_crear'),
    path('api/solicitudes/crear/archivo/', UploadExcelAndCreateRequests.as_view(), name='solicitudes_crear'),
    path('api/solicitudes/crearAdenda/', SolicitudesCreateAdenda.as_view(), name='solicitudes_crear'),
    path('api/solicitudes/crearEJ/', SolicitudesCreateExtraJudiciales.as_view(), name='solicitudes_crear'),
    path('api/solicitudes/archivadas/', SolicitudesArchivadas.as_view(), name='solicitudes_archivadas'),
    path('api/solicitudes/visibles/', SolicitudesVisibles.as_view(), name='solicitudes_visibles'),
    path('api/solicitudes/general/matriz/', MatrizGeneral.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/filtrar/', FiltrarSolicitudes.as_view(), name='filtrar_solicitudes'),
    path('api/solicitudes/asignar-gestor/', AsignarGestor.as_view(), name='historial_solicitud'),
    path('api/solicitudes/editar-estado/', EditarEstadoSolicitud.as_view(), name='editar_estado_solicitud'),
    path('api/historial-estados/', HistorialEstadosSolicitud.as_view(), name='historial_solicitud'),
    path('api/solicitudes/archivar/', ArchivarSolicitud.as_view(), name='archivar_solicitud'),
    path('api/solicitudes/gestor/', SolicitudesGestor.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/gestor/matriz/', MatrizGestor.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/solicitante/matriz/', MatrizSolicitante.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/aprobador/', SolicitudesAprobador.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/aprobador/aprobar/<int:int_idSolicitudes>/<int:int_idUsuario>/<str:suscriptor>/', ActualizarAprobadorView.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/aprobador/rechazar/<int:int_idSolicitudes>/<int:int_idUsuario>/', RechazarSolicitud.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/controller/', SolicitudesController.as_view(), name='solicitudes_controller'),
    path('api/solicitudes/solicitante/', SolicitudesSolicitante.as_view(), name='solicitudes_solicitante'),
    path('api/solicitudes/filtrar-solicitante/', FiltrarSolicitudesSolicitante.as_view(), name='filtrar_solicitudes-solicitante'),
    path('api/solicitudes/filtrar-controller/', FiltrarSolicitudesController.as_view(), name='filtrar_solicitudes-controller'),
    path('api/solicitudes/<int:pk>/', SolicitudDetailAPIView.as_view(), name='solicitudes_id'),
    path('api/solicitudes/filtrar-gestor/', FiltrarSolicitudesGestor.as_view(), name='filtrar_solicitudes-solicitante'),
    path('api/solicitudes/aprobadores-insertar/', InsertarAprobadorView.as_view(), name='solicitudes_aprobadores/insertar'),
    path('api/solicitudes/aprobadores/<str:str_idSuscripcion>/<int:int_idSolicitudes>/', ListarAprobadoresView.as_view(), name='solicitudes_aprobadores/listar'),
    path('api/solicitudes/firmaContrato/', FirmarContrato.as_view(), name='firmar-contrato'),
    path('api/solicitudes/buscarxcodigo/<str:str_CodSolicitudes>/<str:str_idSuscriptor>/', BusquedaSolicitudxCod.as_view(), name='firmar-contrato'),
    path('api/solicitudes/SolicitudesxGestor/', ContadorPorGestor.as_view(), name='firmar-contrato'),
    path('api/solicitudes/SolicitudesxGestor/anio/', ContadorPorGestorAnio.as_view(), name='firmar-contrato'),
    #REGISTROS
    path('api/Registros/Buscar/', BusquedaRegistros.as_view(), name='Buscar-registros'),
    path('api/Registros/Buscar/descargar/comprimido/', BusquedaRegistroComprimir.as_view(), name='Buscar-registros-descargar-comprimido'),
    path('api/solicitudes/gestor/fin-contrato/', SolicitudesPorFinalizar.as_view(), name='solicitudes_gestor'),
    path('api/solicitudes/gestor/Vencidos/', SolicitudesVencidas.as_view(), name='solicitudes_Vencidas'),
    path('api/solicitudes/gestor/Vencidos/descargar/comprimido/', SolicitudesArchivosComprimir.as_view(), name='solicitudes_Vencidas_descargar_comprimir'),
    path('api/solicitudes/gestor/por-finalizar/', SolicitudesPorFinalizarGestor.as_view(), name='solicitudes_Vencidas'),
    path('api/solicitudes/data/bot/', DataBot.as_view(), name='solicitudes_Vencidas'),
    #CARGADOR
    path('api/solicitudes/transferir/', SolicitudesCreateHistoricos.as_view(), name='solicitudes_crear_historicos')
]