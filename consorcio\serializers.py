from rest_framework import serializers

class ConsorcioSerializer(serializers.Serializer):
    int_idInterlocutor = serializers.IntegerField()
    str_Obligacion = serializers.CharField(allow_null=True,allow_blank=True)
    int_idUsuarioCreacion = serializers.IntegerField()
    
    str_ValorAporte = serializers.CharField(required=False, allow_null=True,allow_blank=True)
    str_PorcentajeAporte = serializers.CharField(required=False, allow_null=True,allow_blank=True)
    str_ValorServicios = serializers.CharField(required=False, allow_null=True,allow_blank=True)
    str_ValorHonorarios = serializers.CharField(required=False, allow_null=True,allow_blank=True)
class ConsorcioEditarSerializer(serializers.Serializer):
    str_ValorAporte = serializers.CharField(max_length=50,required=False,allow_blank=True)
    str_PorcentajeAporte = serializers.Char<PERSON>ield(max_length=50,required=False,allow_blank=True)
    str_ValorServicios = serializers.CharField(max_length=50,required=False,allow_blank=True)
    str_ValorHonorarios = serializers.CharField(max_length=50,required=False,allow_blank=True)
    int_idUsuarioModificacion = serializers.IntegerField(required=False)
    int_idSolicitudCont = serializers.IntegerField(required=False)
    int_idInterlocutor= serializers.IntegerField(required=False)
class AsignacionSerializer(serializers.Serializer):
    int_idConsorcio  = serializers.IntegerField(required=False)
    int_idSolicitudCont  = serializers.IntegerField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField(required=False)
