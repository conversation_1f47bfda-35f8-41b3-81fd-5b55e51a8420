from rest_framework import serializers

class SolicitudSerializer(serializers.Serializer):
    int_idSolicitante = serializers.IntegerField(required=False, allow_null=True)
    str_idSuscriptor = serializers.CharField(max_length=15)
    int_idEmpresa  = serializers.IntegerField(required=False, allow_null=True)
    int_idUnidadNegocio  = serializers.IntegerField(required=False,allow_null=True)
    str_DeTerceros = serializers.CharField(max_length=255,allow_blank=True,required=False)
    int_idTipoSol = serializers.IntegerField()
    int_SolicitudGuardada = serializers.IntegerField()
    dt_FechaEsperada = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()
    str_Visible = serializers.CharField(default="si",required=False,allow_blank=True)  
    int_idEstado = serializers.IntegerField(required=False) 
    int_idClienteAsociado = serializers.IntegerField(required=False,allow_null=True)
    db_Honorarios = serializers.FloatField(required=False,allow_null=True) 

class AprobadorSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField(max_length=255)
    int_idUsuario = serializers.IntegerField()
    int_idSolicitudes = serializers.IntegerField()
    dt_FechaAceptacion = serializers.DateTimeField(required=False) 
    int_OrdenAprobacion = serializers.IntegerField()
    int_EstadoAprobacion = serializers.IntegerField(default=1) 
    int_idUsuarioCreacion = serializers.IntegerField()
    int_idUsuarioModificacion = serializers.IntegerField(required=False)


class SolicitudUpdateSerializer(serializers.Serializer):
    int_idEmpresa = serializers.IntegerField()
    int_idEstado = serializers.IntegerField(required=False)
    int_idUnidadNegocio = serializers.IntegerField()
    str_DeTerceros = serializers.CharField(max_length=255)
    int_idTipoSol = serializers.IntegerField()
    dt_FechaEsperada = serializers.DateTimeField()
    int_idClienteAsociado = serializers.IntegerField(required=False,allow_null=True)
    int_SolicitudGuardada = serializers.IntegerField()
    int_idUsuarioModificacion = serializers.IntegerField()
    db_Honorarios = serializers.FloatField(required=False) 

class SolicitudSerializerAdenda(serializers.Serializer):
    str_CodSolicitudes = serializers.CharField(max_length = 19,required=False)
    int_idSolicitante = serializers.IntegerField(required=False, allow_null=True)
    str_idSuscriptor = serializers.CharField(max_length=15)
    int_idEmpresa  = serializers.IntegerField(required=False, allow_null=True)
    int_idUnidadNegocio  = serializers.IntegerField(required=False,allow_null=True)
    str_DeTerceros = serializers.CharField(max_length=255,required=False,allow_blank=True )
    int_idTipoSol = serializers.IntegerField()
    int_SolicitudGuardada = serializers.IntegerField()
    dt_FechaEsperada = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()
    str_Visible = serializers.CharField(default="si",required=False,allow_blank=True)  
    int_idEstado = serializers.IntegerField(required=False) 
    int_idClienteAsociado = serializers.IntegerField(required=False)
    db_Honorarios = serializers.FloatField(required=False,allow_null=True) 
    
class SolicitudSerializerHistorico(serializers.Serializer):
    int_idSolicitante = serializers.IntegerField(required=False, allow_null=True)
    str_idSuscriptor = serializers.CharField(max_length=15)
    int_idEmpresa  = serializers.IntegerField(required=False, allow_null=True)
    int_idUnidadNegocio  = serializers.IntegerField(required=False,allow_null=True)
    str_DeTerceros = serializers.CharField(max_length=255,allow_blank=True,required=False)
    str_TipoContrato = serializers.CharField(max_length=255,allow_blank=True,required=False)
    int_SolicitudGuardada = serializers.IntegerField()
    dt_FechaEsperada = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()
    str_Visible = serializers.CharField(default="si",required=False,allow_blank=True)  
    int_idEstado = serializers.IntegerField(required=False) 
    int_idClienteAsociado = serializers.IntegerField(required=False,allow_null=True)
    db_Honorarios = serializers.FloatField(required=False) 

class SolicitudSerializerExtraJudicial(serializers.Serializer):
    str_CodSolicitudes = serializers.CharField(max_length = 19,required=False)
    int_idSolicitante = serializers.IntegerField(required=False, allow_null=True)
    str_idSuscriptor = serializers.CharField(max_length=15)
    int_idEmpresa  = serializers.IntegerField(required=False, allow_null=True)
    int_idUnidadNegocio  = serializers.IntegerField(required=False,allow_null=True)
    str_DeTerceros = serializers.CharField(max_length=255,required=False,allow_blank=True )
    int_idTipoSol = serializers.IntegerField()
    int_SolicitudGuardada = serializers.IntegerField()
    int_idUsuarioCreacion = serializers.IntegerField()
    str_Visible = serializers.CharField(default="si",required=False,allow_blank=True)  
    int_idEstado = serializers.IntegerField(required=False) 
    int_idClienteAsociado = serializers.IntegerField(required=False)
    db_Honorarios = serializers.FloatField(required=False) 
    str_DetalleAcuerdo= serializers.CharField(max_length=255,required=False,allow_blank=True)
    str_documentoFirmante1= serializers.CharField(max_length=255,required=False,allow_blank=True)
    str_documentoFirmante2= serializers.CharField(max_length=255,required=False,allow_blank=True)
    str_Firmante1= serializers.CharField(max_length=255,required=False,allow_blank=True)
    str_Firmante2= serializers.CharField(max_length=255,required=False,allow_blank=True)
    dt_FechaFirmaEJ= serializers.DateTimeField(required=False)