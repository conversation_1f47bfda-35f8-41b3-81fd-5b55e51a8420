
from django.db import connection

from utils.security import JWT
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

class getToken(APIView):
    def get(self, request, format=None):
        jwt_obj = JWT()
        token = jwt_obj.get_token()
        refresh_token = jwt_obj.get_refresh_token()
        
        return Response({
            "token": token,
            "refresh_token": refresh_token
        }, status=status.HTTP_200_OK)

            
                


