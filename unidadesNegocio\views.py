from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import connection
from .serializers import UnidadNegocioSerializer,UnidadNegocioUpdateSerializer


class UnidadNegocioListGeneralAPIView(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = "SELECT * FROM tm_unidadesnegocios WHERE str_idSuscripcion = %s "
        params = [str_idSuscripcion]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'int_idUnidadesNegocio': row[0],
                    'str_Descripcion': row[1],
                    'str_idSuscripcion': row[2],
                    'dt_FechaCreacion': row[3],
                    'dt_FechaModificacion': row[4],
                    'int_idUsuarioCreador': row[5],
                    'int_idUsuarioModificacion': row[6],
                    'int_idEmpresa': row[7],
                }
                for row in rows
            ]
        return Response(clausulas) 
class UnidadNegocioListCreateAPIView(APIView):
    def get(self, request):
        str_idSuscripcion = request.query_params.get('str_idSuscripcion')
        int_idEmpresa = request.query_params.get('int_idEmpresa')
        if not str_idSuscripcion:
            return Response(
                {"error": "El parámetro 'str_idSuscripcion' es obligatorio."},
                status=status.HTTP_400_BAD_REQUEST
            )

        query = "SELECT * FROM tm_unidadesnegocios WHERE str_idSuscripcion = %s AND int_idEmpresa = %s"
        params = [str_idSuscripcion,int_idEmpresa]

        with connection.cursor() as cursor:
            cursor.execute(query, params)
            rows = cursor.fetchall()
            clausulas = [
                {
                    'int_idUnidadesNegocio': row[0],
                    'str_Descripcion': row[1],
                    'str_idSuscripcion': row[2],
                    'dt_FechaCreacion': row[3],
                    'dt_FechaModificacion': row[4],
                    'int_idUsuarioCreador': row[5],
                    'int_idUsuarioModificacion': row[6],
                    'int_idEmpresa': row[7],
                }
                for row in rows
            ]
        return Response(clausulas)
    def post(self, request):
        serializer = UnidadNegocioSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                INSERT INTO tm_unidadesnegocios 
                (str_Descripcion , str_idSuscripcion, dt_FechaCreacion, int_idUsuarioCreador,int_idEmpresa )
                VALUES (%s, %s,  NOW(), %s)
                """
                cursor.execute(query, [
                    serializer.validated_data['str_Descripcion'],
                    serializer.validated_data['str_idSuscripcion'],
                    serializer.validated_data['int_idUsuarioCreador'],
                    serializer.validated_data['int_idEmpresa']
                ])
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UnidadNegocioDetailAPIView(APIView):
    def get_object(self, pk):
        # Obtiene un solo registro por clave primaria
        with connection.cursor() as cursor:
            cursor.execute("SELECT * FROM tm_unidadesnegocios WHERE int_idUnidadesNegocio = %s", [pk])
            row = cursor.fetchone()
            if row:
                return {
                    'int_idUnidadesNegocio': row[0],
                    'str_Descripcion': row[1],
                    'str_idSuscripcion': row[2],
                    'dt_FechaCreacion': row[3],
                    'dt_FechaModificacion': row[4],
                    'int_idUsuarioCreador': row[5],
                    'int_idUsuarioModificacion': row[6],
                    'int_idEmpresa': row[7],
                }
            return None
    def get(self, request, pk):
        clausula = self.get_object(pk)
        if clausula:
            return Response(clausula)
        return Response(status=status.HTTP_404_NOT_FOUND)
    def put(self, request, pk):
        clausula = self.get_object(pk)
        if not clausula:
            return Response(status=status.HTTP_404_NOT_FOUND)
        serializer = UnidadNegocioUpdateSerializer(data=request.data)
        if serializer.is_valid():
            with connection.cursor() as cursor:
                query = """
                UPDATE tm_unidadesnegocios 
                SET  str_Descripcion=%s, dt_FechaModificacion=NOW(), int_idUsuarioModificador=%s 
                WHERE int_idUnidadesNegocio=%s 
                """
                cursor.execute(query, [
                    serializer.validated_data['str_Descripcion'],
                    serializer.validated_data['int_idUsuarioModificador'],
                    pk
                ])
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tm_unidadesnegocios WHERE int_idUnidadesNegocio = %s", [pk])
        return Response(status=status.HTTP_204_NO_CONTENT)