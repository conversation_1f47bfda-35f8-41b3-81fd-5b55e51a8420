from rest_framework import serializers

class ArchivoSerializer(serializers.Serializer):
    str_idSuscriptor = serializers.Char<PERSON>ield(max_length=50)
    int_idSolicitudes = serializers.IntegerField()
    int_idUsuarioCreacion = serializers.IntegerField()
    archivo = serializers.FileField()
    str_CodSolicitudes = serializers.CharField(max_length=19)
    str_CodTipoDocumento = serializers.CharField(max_length=4)

class PlantillaSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField(max_length=50)
    int_idUsuarioCreacion = serializers.IntegerField()
    archivo = serializers.FileField()
    str_CodTipoSol = serializers.CharField(max_length=4)
    int_idEmpresa = serializers.CharField()
    str_TipoPlantilla = serializers.CharField()
class FirmaSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField(max_length=50)
    int_idUsuario= serializers.IntegerField()
    firma = serializers.FileField()
    

class ArchivoFirmadoSerializer(serializers.Serializer):
    str_idSuscriptor = serializers.CharField(max_length=50)
    int_idUsuarioCreacion = serializers.CharField()
    
    str_CodSolicitudes = serializers.CharField(max_length=19)
    str_CodTipoDocumento = serializers.CharField(max_length=4)