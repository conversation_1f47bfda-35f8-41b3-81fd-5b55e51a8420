from rest_framework import serializers

# Serializer para visualizar los datos de una solicitud
class SolicitudContSerializer(serializers.Serializer):
    str_idSuscriptor = serializers.CharField(max_length=255)
    int_idSolicitudes = serializers.IntegerField()
    int_idInterlocutor = serializers.IntegerField()
    int_idInterlocutorComprador = serializers.IntegerField()
    int_NumAsociados = serializers.IntegerField()
    str_ObjetivoSolicitud = serializers.CharField(max_length=255)
    str_Moneda = serializers.CharField(max_length=3)
    db_Presupuesto = serializers.FloatField(required=False) 
    str_Margen = serializers.CharField(max_length=255)
    str_PlazoSolicitud = serializers.CharField(max_length=255)
    str_TipoServicio = serializers.CharField(max_length=255)
    str_InfoAdicional = serializers.Char<PERSON>ield(max_length=500, allow_blank=True)
    str_DocAdjuntos = serializers.CharField(max_length=500, allow_blank=True)
    str_CondicionPago = serializers.CharField(max_length=500, allow_blank=True)
    str_ConsultorAsignado = serializers.CharField(max_length=255)
    str_RenovacionAuto = serializers.CharField(max_length=255)
    str_DetalleRenovAuto = serializers.CharField(max_length=500, allow_blank=True)
    str_AjusteHonorarios = serializers.CharField(max_length=255)
    str_DetalleAjusteHonorarios = serializers.CharField(max_length=500, allow_blank=True)
    str_Garantia = serializers.CharField(max_length=255)
    str_DetalleGarantia = serializers.CharField(max_length=500, allow_blank=True)
    str_ResolucionAnticipada = serializers.CharField(max_length=255)
    str_DetalleResolucionAnticipada = serializers.CharField(max_length=500, allow_blank=True)
    str_Penalidades = serializers.CharField(max_length=255)
    str_DetallePenalidades = serializers.CharField(max_length=500, allow_blank=True)
    str_BienMuebleInmueble = serializers.CharField(max_length=255)
    str_BienPartidaCertificada = serializers.CharField(max_length=255)
    str_BienDireccion = serializers.CharField(max_length=500)
    str_BienUso = serializers.CharField(max_length=255)
    str_BienDescripcion = serializers.CharField(max_length=500)
    str_ObjetoContrato = serializers.CharField(max_length=500)
    str_MonedaContrato = serializers.CharField(max_length=3)
    str_RentaPactada = serializers.CharField(max_length=255)
    str_ImporteVenta = serializers.CharField(max_length=255)
    str_FormaPago = serializers.CharField(max_length=255)
    str_PlazoArriendo = serializers.CharField(max_length=255)
    dt_FechaArriendo = serializers.DateTimeField()
    dt_FechaVenta = serializers.DateTimeField()
    str_InteresRetraso = serializers.CharField(max_length=255)
    str_ObligacionesConjuntas = serializers.CharField(max_length=500)
    str_GarantiaVenta = serializers.CharField(max_length=255)
    str_DetalleGarantiaVenta = serializers.CharField(max_length=500, allow_blank=True)
    str_InfoCompartida = serializers.CharField(max_length=500, allow_blank=True)


# Serializer para actualizar los datos de una solicitud
class SolicitudContUpdateSerializer(serializers.Serializer):
    str_idSuscriptor = serializers.CharField(max_length=255)
    int_idSolicitudes = serializers.IntegerField()
    int_idInterlocutor = serializers.IntegerField()
    int_idInterlocutorComprador = serializers.IntegerField()
    int_NumAsociados = serializers.IntegerField()
    str_ObjetivoSolicitud = serializers.CharField(max_length=255)
    str_Moneda = serializers.CharField(max_length=3)
    db_Presupuesto = serializers.FloatField(required=False) 
    str_Margen = serializers.CharField(max_length=255)
    str_PlazoSolicitud = serializers.CharField(max_length=255)
    str_TipoServicio = serializers.CharField(max_length=255)
    str_InfoAdicional = serializers.CharField(max_length=500, allow_blank=True)
    str_DocAdjuntos = serializers.CharField(max_length=500, allow_blank=True)
    str_CondicionPago = serializers.CharField(max_length=500, allow_blank=True)
    str_ConsultorAsignado = serializers.CharField(max_length=255)
    str_RenovacionAuto = serializers.CharField(max_length=255)
    str_DetalleRenovAuto = serializers.CharField(max_length=500, allow_blank=True)
    str_AjusteHonorarios = serializers.CharField(max_length=255)
    str_DetalleAjusteHonorarios = serializers.CharField(max_length=500, allow_blank=True)
    str_Garantia = serializers.CharField(max_length=255)
    str_DetalleGarantia = serializers.CharField(max_length=500, allow_blank=True)
    str_ResolucionAnticipada = serializers.CharField(max_length=255)
    str_DetalleResolucionAnticipada = serializers.CharField(max_length=500, allow_blank=True)
    str_Penalidades = serializers.CharField(max_length=255)
    str_DetallePenalidades = serializers.CharField(max_length=500, allow_blank=True)
    str_BienMuebleInmueble = serializers.CharField(max_length=255)
    str_BienPartidaCertificada = serializers.CharField(max_length=255)
    str_BienDireccion = serializers.CharField(max_length=500)
    str_BienUso = serializers.CharField(max_length=255)
    str_BienDescripcion = serializers.CharField(max_length=500)
    str_ObjetoContrato = serializers.CharField(max_length=500)
    str_MonedaContrato = serializers.CharField(max_length=3)
    str_RentaPactada = serializers.CharField(max_length=255)
    str_ImporteVenta = serializers.CharField(max_length=255)
    str_FormaPago = serializers.CharField(max_length=255)
    str_PlazoArriendo = serializers.CharField(max_length=255)
    dt_FechaArriendo = serializers.DateTimeField()
    dt_FechaVenta = serializers.DateTimeField()
    str_InteresRetraso = serializers.CharField(max_length=255)
    str_ObligacionesConjuntas = serializers.CharField(max_length=500)
    str_GarantiaVenta = serializers.CharField(max_length=255)
    str_DetalleGarantiaVenta = serializers.CharField(max_length=500, allow_blank=True)
    str_InfoCompartida = serializers.CharField(max_length=500, allow_blank=True)