from rest_framework import serializers

class EmpresasSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.Char<PERSON>ield(max_length=50)
    str_NombreEmpresa = serializers.CharField(max_length=250)
    str_RazonSocial = serializers.Char<PERSON>ield(max_length=250)
    str_Ruc  = serializers.CharField(max_length=25)
    dt_FechaCreacion = serializers.DateTimeField(required=False)
    int_idUsuarioCreacion = serializers.IntegerField()

class EmpresaUpdateSerializer(serializers.Serializer):
    str_NombreEmpresa = serializers.Char<PERSON>ield(max_length=150)
    str_RazonSocial = serializers.Char<PERSON>ield(max_length=150)
    str_Ruc  = serializers.CharField(max_length=25)
    int_idUsuarioModificacion = serializers.IntegerField()